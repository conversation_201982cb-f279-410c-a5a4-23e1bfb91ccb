package com.hzwangda.edu.gateway.service;

import com.alibaba.fastjson2.JSON;
import com.hzwangda.edu.gateway.filter.LogContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
// import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 日志存储服务
 * 支持多种存储方式：文件、Redis、Kafka等
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LogStorageService {
    
    private final ReactiveRedisTemplate<String, String> redisTemplate;
    // private final KafkaTemplate<String, String> kafkaTemplate;
    
    @Value("${gateway.logging.storage.type:file}")
    private String storageType;
    
    @Value("${gateway.logging.storage.file.path:/var/log/gateway}")
    private String logFilePath;
    
    @Value("${gateway.logging.storage.file.max-size:104857600}")
    private long maxFileSize;
    
    @Value("${gateway.logging.storage.redis.key-prefix:gateway:logs:}")
    private String redisKeyPrefix;
    
    @Value("${gateway.logging.storage.redis.expire-hours:24}")
    private int redisExpireHours;
    
    @Value("${gateway.logging.storage.kafka.topic:gateway-logs}")
    private String kafkaTopic;
    
    @Value("${gateway.logging.storage.buffer.enabled:true}")
    private boolean bufferEnabled;
    
    @Value("${gateway.logging.storage.buffer.size:1000}")
    private int bufferSize;
    
    /**
     * 存储请求日志
     */
    public Mono<Void> storeRequestLog(LogContext logContext) {
        return Mono.fromRunnable(() -> {
            try {
                String logJson = JSON.toJSONString(logContext);
                
                switch (storageType.toLowerCase()) {
                    case "file":
                        writeToFile(logJson, logContext);
                        break;
                    case "redis":
                        writeToRedis(logJson, logContext);
                        break;
                    case "kafka":
                        writeToKafka(logJson, logContext);
                        break;
                    case "mixed":
                        // 混合存储：重要日志写文件，全量日志写Kafka
                        if (logContext.isError() || logContext.getDuration() > 1000) {
                            writeToFile(logJson, logContext);
                        }
                        writeToKafka(logJson, logContext);
                        break;
                    default:
                        log.warn("Unknown storage type: {}", storageType);
                }
            } catch (Exception e) {
                log.error("Failed to store request log", e);
            }
        }).then().subscribeOn(Schedulers.boundedElastic());
    }
    
    /**
     * 异步存储请求日志
     */
    @Async
    public void storeRequestLogAsync(LogContext logContext) {
        storeRequestLog(logContext).subscribe();
    }
    
    /**
     * 更新响应日志
     */
    public Mono<Void> updateResponseLog(LogContext logContext) {
        return storeRequestLog(logContext);
    }
    
    /**
     * 异步更新响应日志
     */
    @Async
    public void updateResponseLogAsync(LogContext logContext) {
        updateResponseLog(logContext).subscribe();
    }
    
    /**
     * 存储错误日志
     */
    public Mono<Void> storeErrorLog(LogContext logContext) {
        // 错误日志总是写入文件
        return Mono.fromRunnable(() -> {
            try {
                String logJson = JSON.toJSONString(logContext);
                writeToFile(logJson, logContext);
                
                // 同时发送告警
                sendAlert(logContext);
            } catch (Exception e) {
                log.error("Failed to store error log", e);
            }
        }).then().subscribeOn(Schedulers.boundedElastic());
    }
    
    /**
     * 异步存储错误日志
     */
    @Async
    public void storeErrorLogAsync(LogContext logContext) {
        storeErrorLog(logContext).subscribe();
    }
    
    /**
     * 写入文件
     */
    private void writeToFile(String logJson, LogContext logContext) {
        try {
            // 按日期分割文件
            String fileName = String.format("gateway-%s.log", 
                    LocalDate.now().format(DateTimeFormatter.ISO_DATE));
            Path filePath = Paths.get(logFilePath, fileName);
            
            // 确保目录存在
            Files.createDirectories(filePath.getParent());
            
            // 检查文件大小
            if (Files.exists(filePath) && Files.size(filePath) > maxFileSize) {
                // 轮转文件
                rotateFile(filePath);
            }
            
            // 写入日志
            try (BufferedWriter writer = Files.newBufferedWriter(filePath, 
                    StandardOpenOption.CREATE, StandardOpenOption.APPEND)) {
                writer.write(logJson);
                writer.newLine();
            }
            
        } catch (IOException e) {
            log.error("Failed to write log to file", e);
        }
    }
    
    /**
     * 写入Redis
     */
    private void writeToRedis(String logJson, LogContext logContext) {
        String key = redisKeyPrefix + logContext.getRequestId();
        
        redisTemplate.opsForValue()
                .set(key, logJson, Duration.ofHours(redisExpireHours))
                .doOnError(e -> log.error("Failed to write log to Redis", e))
                .subscribe();
        
        // 同时写入按时间的有序集合，便于查询
        String timeKey = redisKeyPrefix + "timeline:" + 
                LocalDate.now().format(DateTimeFormatter.ISO_DATE);
        redisTemplate.opsForZSet()
                .add(timeKey, logContext.getRequestId(), logContext.getTimestamp())
                .doOnError(e -> log.error("Failed to write log timeline to Redis", e))
                .subscribe();
    }
    
    /**
     * 写入Kafka
     */
    private void writeToKafka(String logJson, LogContext logContext) {
        // Kafka写入功能暂时禁用，需要添加kafka依赖
        log.debug("Kafka logging is currently disabled");
    }
    
    /**
     * 文件轮转
     */
    private void rotateFile(Path filePath) throws IOException {
        String rotatedName = filePath.toString() + "." + System.currentTimeMillis();
        Files.move(filePath, Paths.get(rotatedName));
        log.info("Rotated log file: {} -> {}", filePath, rotatedName);
    }
    
    /**
     * 发送告警
     */
    private void sendAlert(LogContext logContext) {
        // 这里可以集成告警系统
        log.error("Alert: Error occurred - RequestId: {}, Path: {}, Error: {}", 
                logContext.getRequestId(), 
                logContext.getPath(), 
                logContext.getError());
    }
    
    /**
     * 查询日志
     */
    public Mono<LogContext> getLog(String requestId) {
        if ("redis".equalsIgnoreCase(storageType)) {
            String key = redisKeyPrefix + requestId;
            return redisTemplate.opsForValue().get(key)
                    .map(json -> JSON.parseObject(json, LogContext.class));
        }
        return Mono.empty();
    }
    
    /**
     * 查询时间范围内的日志
     */
    public Mono<List<String>> getLogsByTimeRange(long startTime, long endTime) {
        if ("redis".equalsIgnoreCase(storageType)) {
            String timeKey = redisKeyPrefix + "timeline:" + 
                    LocalDate.now().format(DateTimeFormatter.ISO_DATE);
            return redisTemplate.opsForZSet()
                    .rangeByScore(timeKey, org.springframework.data.domain.Range.closed((double) startTime, (double) endTime))
                    .collectList()
                    .flatMap(requestIds -> {
                        // 批量获取日志详情
                        return Mono.just(requestIds);
                    });
        }
        return Mono.empty();
    }
}