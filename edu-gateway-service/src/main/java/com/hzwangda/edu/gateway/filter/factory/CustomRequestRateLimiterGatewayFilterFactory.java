package com.hzwangda.edu.gateway.filter.factory;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.cloud.gateway.filter.ratelimit.RateLimiter;
import org.springframework.cloud.gateway.filter.ratelimit.RedisRateLimiter;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * 自定义请求限流过滤器工厂
 * 支持多种限流策略和自定义响应
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class CustomRequestRateLimiterGatewayFilterFactory 
        extends AbstractGatewayFilterFactory<CustomRequestRateLimiterGatewayFilterFactory.Config> {
    
    @Autowired
    private Map<String, KeyResolver> keyResolvers;
    
    @Autowired
    private Map<String, RateLimiter> rateLimiters;
    
    public CustomRequestRateLimiterGatewayFilterFactory() {
        super(Config.class);
    }
    
    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            // 获取限流器
            RateLimiter rateLimiter = getRateLimiter(config);
            if (rateLimiter == null) {
                log.error("Unable to find rate limiter: {}", config.getRateLimiter());
                return chain.filter(exchange);
            }
            
            // 获取键解析器
            KeyResolver keyResolver = getKeyResolver(config);
            if (keyResolver == null) {
                log.error("Unable to find key resolver: {}", config.getKeyResolver());
                return chain.filter(exchange);
            }
            
            // 获取路由ID
            Route route = exchange.getAttribute(ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR);
            String routeId = route != null ? route.getId() : "unknown";
            
            // 解析限流键
            return keyResolver.resolve(exchange)
                    .flatMap(key -> {
                        log.debug("Rate limit key: {} for route: {}", key, routeId);
                        
                        // 应用限流
                        return rateLimiter.isAllowed(routeId, key)
                                .flatMap(response -> {
                                    RateLimiter.Response rateLimitResponse = (RateLimiter.Response) response;
                                    if (rateLimitResponse.isAllowed()) {
                                        // 添加限流响应头
                                        ServerHttpResponse httpResponse = exchange.getResponse();
                                        httpResponse.getHeaders().add("X-RateLimit-Remaining", "1");
                                        httpResponse.getHeaders().add("X-RateLimit-Capacity",
                                                String.valueOf(getRateLimitCapacity(rateLimiter)));
                                        httpResponse.getHeaders().add("X-RateLimit-Requested",
                                                String.valueOf(config.getRequestedTokens()));

                                        return chain.filter(exchange);
                                    } else {
                                        // 限流响应
                                        log.warn("Rate limit exceeded for key: {} on route: {}", key, routeId);
                                        return handleRateLimitExceeded(exchange, config);
                                    }
                                });
                    });
        };
    }
    
    /**
     * 获取限流器
     */
    private RateLimiter getRateLimiter(Config config) {
        String rateLimiterName = config.getRateLimiter();
        return rateLimiters.get(rateLimiterName + "RateLimiter");
    }
    
    /**
     * 获取键解析器
     */
    private KeyResolver getKeyResolver(Config config) {
        String keyResolverName = config.getKeyResolver();
        return keyResolvers.get(keyResolverName + "KeyResolver");
    }
    
    /**
     * 获取限流容量
     */
    private long getRateLimitCapacity(RateLimiter rateLimiter) {
        if (rateLimiter instanceof RedisRateLimiter) {
            // 使用反射或其他方式获取配置的容量
            return 200L; // 默认值
        }
        return -1L;
    }
    
    /**
     * 处理限流超出
     */
    private Mono<Void> handleRateLimitExceeded(ServerWebExchange exchange, Config config) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.TOO_MANY_REQUESTS);
        response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
        
        // 构建响应体
        String body = String.format(
                "{\"code\":429,\"message\":\"%s\",\"timestamp\":%d}",
                config.getDenyMessage(),
                System.currentTimeMillis()
        );
        
        DataBuffer buffer = response.bufferFactory()
                .wrap(body.getBytes(StandardCharsets.UTF_8));
        
        return response.writeWith(Mono.just(buffer));
    }
    
    /**
     * 配置类
     */
    @Data
    public static class Config {
        /**
         * 限流器名称
         */
        private String rateLimiter = "default";
        
        /**
         * 键解析器名称
         */
        private String keyResolver = "user";
        
        /**
         * 请求令牌数
         */
        private int requestedTokens = 1;
        
        /**
         * 拒绝消息
         */
        private String denyMessage = "Too many requests, please try again later";
        
        /**
         * 是否包含响应头
         */
        private boolean includeHeaders = true;
    }
}