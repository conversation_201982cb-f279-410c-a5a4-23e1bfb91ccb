package com.hzwangda.edu.gateway.filter;

import com.hzwangda.edu.gateway.context.AuthContext;
import com.hzwangda.edu.gateway.service.LogStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 增强的请求日志记录过滤器
 * 支持请求体记录、采样、敏感信息脱敏等高级功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EnhancedRequestLogFilter implements GlobalFilter, Ordered {
    
    private final LogStorageService logStorageService;
    
    @Value("${gateway.logging.enhanced.enabled:true}")
    private boolean enabled;
    
    @Value("${gateway.logging.enhanced.log-headers:true}")
    private boolean logHeaders;
    
    @Value("${gateway.logging.enhanced.log-request-body:false}")
    private boolean logRequestBody;
    
    @Value("${gateway.logging.enhanced.log-response-body:false}")
    private boolean logResponseBody;
    
    @Value("${gateway.logging.enhanced.max-body-size:1024}")
    private int maxBodySize;
    
    @Value("${gateway.logging.enhanced.sampling-rate:1.0}")
    private double samplingRate;
    
    @Value("${gateway.logging.enhanced.async:true}")
    private boolean asyncLogging;
    
    private static final String REQUEST_ID_HEADER = "X-Request-Id";
    private static final String REQUEST_START_TIME = "requestStartTime";
    private static final String REQUEST_LOG_CONTEXT = "requestLogContext";
    
    // 需要脱敏的请求头
    private static final Set<String> SENSITIVE_HEADERS = Set.of(
            "authorization", "cookie", "x-auth-token", 
            "api-key", "secret", "password", "token"
    );
    
    // 需要脱敏的参数
    private static final Set<String> SENSITIVE_PARAMS = Set.of(
            "password", "pwd", "secret", "token", 
            "key", "apikey", "api_key", "auth"
    );
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        if (!enabled || !shouldLog(exchange)) {
            return chain.filter(exchange);
        }
        
        // 初始化日志上下文
        LogContext logContext = initializeLogContext(exchange);
        exchange.getAttributes().put(REQUEST_LOG_CONTEXT, logContext);
        exchange.getAttributes().put(REQUEST_START_TIME, System.currentTimeMillis());
        
        // 修改请求，添加请求ID
        ServerHttpRequest modifiedRequest = exchange.getRequest().mutate()
                .header(REQUEST_ID_HEADER, logContext.getRequestId())
                .build();
        
        ServerWebExchange modifiedExchange = exchange.mutate()
                .request(modifiedRequest)
                .build();
        
        // 记录请求信息
        logRequest(modifiedExchange, logContext);
        
        // 如果需要记录响应体，需要装饰响应
        if (logResponseBody) {
            return chain.filter(modifiedExchange.mutate()
                    .response(decorateResponse(modifiedExchange, logContext))
                    .build());
        } else {
            return chain.filter(modifiedExchange)
                    .doOnSuccess(aVoid -> logResponse(modifiedExchange, logContext))
                    .doOnError(throwable -> logError(modifiedExchange, logContext, throwable));
        }
    }
    
    /**
     * 判断是否需要记录日志（采样）
     */
    private boolean shouldLog(ServerWebExchange exchange) {
        // 健康检查接口不记录
        String path = exchange.getRequest().getPath().value();
        if (path.contains("/actuator/health")) {
            return false;
        }
        
        // 采样判断
        return ThreadLocalRandom.current().nextDouble() <= samplingRate;
    }
    
    /**
     * 初始化日志上下文
     */
    private LogContext initializeLogContext(ServerWebExchange exchange) {
        ServerHttpRequest request = exchange.getRequest();
        String requestId = request.getHeaders().getFirst(REQUEST_ID_HEADER);
        if (requestId == null) {
            requestId = UUID.randomUUID().toString();
        }
        
        LogContext context = new LogContext();
        context.setRequestId(requestId);
        context.setTimestamp(System.currentTimeMillis());
        context.setMethod(request.getMethod().toString());
        context.setPath(request.getPath().value());
        context.setUri(request.getURI().toString());
        
        // 获取客户端IP
        context.setClientIp(getClientIp(request));
        
        // 获取认证信息
        AuthContext authContext = exchange.getAttribute("authContext");
        if (authContext != null) {
            context.setUserId(authContext.getUserId());
            context.setUsername(authContext.getUsername());
            context.setTenantId(authContext.getTenantId());
        }
        
        // 获取路由信息
        Route route = exchange.getAttribute(ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR);
        if (route != null) {
            context.setRouteId(route.getId());
            context.setTargetUri(route.getUri().toString());
        }
        
        return context;
    }
    
    /**
     * 记录请求信息
     */
    private void logRequest(ServerWebExchange exchange, LogContext logContext) {
        ServerHttpRequest request = exchange.getRequest();
        
        // 记录查询参数
        MultiValueMap<String, String> queryParams = request.getQueryParams();
        if (!queryParams.isEmpty()) {
            logContext.setQueryParams(maskSensitiveParams(queryParams));
        }
        
        // 记录请求头
        if (logHeaders) {
            logContext.setRequestHeaders(maskSensitiveHeaders(request.getHeaders()));
        }
        
        // 记录请求体
        if (logRequestBody && isTextContentType(request.getHeaders().getContentType())) {
            // 请求体记录需要特殊处理，这里简化处理
            logContext.setRequestBody("[Request body logging requires body caching]");
        }
        
        // 异步存储日志
        if (asyncLogging) {
            logStorageService.storeRequestLogAsync(logContext);
        } else {
            logStorageService.storeRequestLog(logContext).subscribe();
        }
        
        // 输出到日志文件
        if (log.isDebugEnabled()) {
            log.debug("Gateway Request: {}", logContext.toLogString());
        }
    }
    
    /**
     * 装饰响应以记录响应体
     */
    private ServerHttpResponseDecorator decorateResponse(ServerWebExchange exchange, LogContext logContext) {
        ServerHttpResponse originalResponse = exchange.getResponse();
        DataBufferFactory bufferFactory = originalResponse.bufferFactory();
        
        return new ServerHttpResponseDecorator(originalResponse) {
            @Override
            public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {
                if (body instanceof Flux) {
                    Flux<? extends DataBuffer> fluxBody = (Flux<? extends DataBuffer>) body;
                    
                    return super.writeWith(fluxBody.map(dataBuffer -> {
                        // 读取响应体内容
                        byte[] content = new byte[dataBuffer.readableByteCount()];
                        dataBuffer.read(content);
                        
                        // 记录响应体（限制大小）
                        if (content.length <= maxBodySize) {
                            String responseBody = new String(content, StandardCharsets.UTF_8);
                            logContext.setResponseBody(maskSensitiveContent(responseBody));
                        } else {
                            logContext.setResponseBody("[Response body too large: " + content.length + " bytes]");
                        }
                        
                        // 重新包装数据
                        return bufferFactory.wrap(content);
                    })).doFinally(signalType -> {
                        logResponse(exchange, logContext);
                    });
                }
                
                return super.writeWith(body);
            }
        };
    }
    
    /**
     * 记录响应信息
     */
    private void logResponse(ServerWebExchange exchange, LogContext logContext) {
        Long startTime = exchange.getAttribute(REQUEST_START_TIME);
        if (startTime != null) {
            logContext.setDuration(System.currentTimeMillis() - startTime);
        }
        
        ServerHttpResponse response = exchange.getResponse();
        HttpStatusCode statusCode = response.getStatusCode();
        logContext.setStatusCode(statusCode != null ? statusCode.value() : 0);
        
        // 记录响应头
        if (logHeaders) {
            logContext.setResponseHeaders(maskSensitiveHeaders(response.getHeaders()));
        }
        
        // 存储完整日志
        if (asyncLogging) {
            logStorageService.updateResponseLogAsync(logContext);
        } else {
            logStorageService.updateResponseLog(logContext).subscribe();
        }
        
        // 输出到日志文件
        log.info("Gateway Response: {} {} {}ms - Status: {}", 
                logContext.getMethod(), 
                logContext.getPath(), 
                logContext.getDuration(),
                logContext.getStatusCode());
    }
    
    /**
     * 记录错误信息
     */
    private void logError(ServerWebExchange exchange, LogContext logContext, Throwable throwable) {
        logContext.setError(throwable.getMessage());
        logContext.setErrorType(throwable.getClass().getSimpleName());
        
        Long startTime = exchange.getAttribute(REQUEST_START_TIME);
        if (startTime != null) {
            logContext.setDuration(System.currentTimeMillis() - startTime);
        }
        
        // 存储错误日志
        if (asyncLogging) {
            logStorageService.storeErrorLogAsync(logContext);
        } else {
            logStorageService.storeErrorLog(logContext).subscribe();
        }
        
        log.error("Gateway Error: {} {} - {}", 
                logContext.getMethod(), 
                logContext.getPath(), 
                throwable.getMessage(), 
                throwable);
    }
    
    /**
     * 获取客户端IP
     */
    private String getClientIp(ServerHttpRequest request) {
        // 从请求头获取
        String[] headers = {"X-Forwarded-For", "X-Real-IP", "Proxy-Client-IP"};
        for (String header : headers) {
            String ip = request.getHeaders().getFirst(header);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                return ip.split(",")[0].trim();
            }
        }
        
        // 从连接获取
        InetSocketAddress remoteAddress = request.getRemoteAddress();
        return remoteAddress != null ? remoteAddress.getAddress().getHostAddress() : "unknown";
    }
    
    /**
     * 脱敏请求头
     */
    private Map<String, String> maskSensitiveHeaders(HttpHeaders headers) {
        Map<String, String> maskedHeaders = new HashMap<>();
        headers.forEach((name, values) -> {
            String value = String.join(",", values);
            if (isSensitiveHeader(name)) {
                maskedHeaders.put(name, maskValue(value));
            } else {
                maskedHeaders.put(name, value);
            }
        });
        return maskedHeaders;
    }
    
    /**
     * 脱敏查询参数
     */
    private Map<String, String> maskSensitiveParams(MultiValueMap<String, String> params) {
        Map<String, String> maskedParams = new HashMap<>();
        params.forEach((name, values) -> {
            String value = String.join(",", values);
            if (isSensitiveParam(name)) {
                maskedParams.put(name, maskValue(value));
            } else {
                maskedParams.put(name, value);
            }
        });
        return maskedParams;
    }
    
    /**
     * 脱敏内容
     */
    private String maskSensitiveContent(String content) {
        // 简单的JSON密码字段脱敏
        return content.replaceAll("\"(password|pwd|secret|token)\"\\s*:\\s*\"[^\"]*\"", 
                "\"$1\":\"******\"");
    }
    
    /**
     * 判断是否是敏感请求头
     */
    private boolean isSensitiveHeader(String headerName) {
        return SENSITIVE_HEADERS.contains(headerName.toLowerCase());
    }
    
    /**
     * 判断是否是敏感参数
     */
    private boolean isSensitiveParam(String paramName) {
        return SENSITIVE_PARAMS.contains(paramName.toLowerCase());
    }
    
    /**
     * 脱敏值
     */
    private String maskValue(String value) {
        if (value == null || value.length() <= 3) {
            return "***";
        }
        int len = value.length();
        int showLen = Math.min(3, len / 4);
        return value.substring(0, showLen) + "*".repeat(len - showLen * 2) + value.substring(len - showLen);
    }
    
    /**
     * 判断是否是文本内容类型
     */
    private boolean isTextContentType(MediaType contentType) {
        if (contentType == null) {
            return false;
        }
        return contentType.includes(MediaType.APPLICATION_JSON) ||
               contentType.includes(MediaType.APPLICATION_XML) ||
               contentType.includes(MediaType.TEXT_PLAIN) ||
               contentType.includes(MediaType.TEXT_HTML);
    }
    
    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 1;
    }
}