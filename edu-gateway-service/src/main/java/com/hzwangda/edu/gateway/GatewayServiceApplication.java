package com.hzwangda.edu.gateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;

/**
 * API网关服务启动类
 *
 * 提供统一的API入口，包括：
 * - 动态路由
 * - 统一认证
 * - 限流熔断
 * - 请求日志
 * - 监控统计
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication(scanBasePackages = "com.hzwangda.edu")
@EnableDiscoveryClient
public class GatewayServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(GatewayServiceApplication.class, args);
    }

}