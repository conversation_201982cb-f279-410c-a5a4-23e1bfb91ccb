package com.hzwangda.edu.gateway.service;

import com.hzwangda.edu.gateway.config.GrayReleaseConfig;
import com.hzwangda.edu.gateway.strategy.GrayReleaseStrategy;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ServerWebExchange;

import java.net.URI;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 灰度发布服务
 * 负责灰度策略判断和服务实例选择
 *
 * <AUTHOR>
 * @date 2025-01-05
 */
@Service
public class GrayReleaseService {

    private static final Logger logger = LoggerFactory.getLogger(GrayReleaseService.class);

    @Autowired
    private GrayReleaseConfig grayReleaseConfig;

    @Autowired
    private DiscoveryClient discoveryClient;

    @Autowired
    private List<GrayReleaseStrategy> strategies;

    private Map<String, GrayReleaseStrategy> strategyMap = new HashMap<>();

    @PostConstruct
    public void init() {
        // 初始化策略映射
        for (GrayReleaseStrategy strategy : strategies) {
            strategyMap.put(strategy.getStrategyName(), strategy);
        }
        logger.info("Initialized gray release strategies: {}", strategyMap.keySet());
    }

    /**
     * 判断是否路由到灰度版本
     */
    public boolean shouldRouteToGray(ServerWebExchange exchange, String serviceName, GrayReleaseConfig.GrayRule grayRule) {
        String strategyName = grayReleaseConfig.getStrategy();
        GrayReleaseStrategy strategy = strategyMap.get(strategyName);

        if (strategy == null) {
            logger.warn("Gray release strategy not found: {}", strategyName);
            return false;
        }

        return strategy.shouldRouteToGray(exchange, serviceName, grayRule);
    }

    /**
     * 获取灰度服务URI
     */
    public URI getGrayServiceUri(String serviceName, GrayReleaseConfig.GrayRule grayRule) {
        try {
            // 优先使用配置的灰度实例
            String[] grayInstances = grayRule.getGrayInstances();
            if (grayInstances != null && grayInstances.length > 0) {
                String instance = selectRandomInstanceUrl(Arrays.asList(grayInstances));
                return new URI(instance);
            }

            // 从服务发现中获取灰度实例
            List<ServiceInstance> instances = discoveryClient.getInstances(serviceName);
            if (instances.isEmpty()) {
                logger.warn("No instances found for service: {}", serviceName);
                return null;
            }

            // 过滤灰度版本的实例
            String grayVersion = grayRule.getVersion() != null ? grayRule.getVersion() : grayReleaseConfig.getGrayVersion();
            List<ServiceInstance> grayServiceInstances = instances.stream()
                .filter(instance -> {
                    String version = instance.getMetadata().get("version");
                    return grayVersion.equals(version);
                })
                .collect(Collectors.toList());

            if (grayServiceInstances.isEmpty()) {
                logger.warn("No gray instances found for service: {} with version: {}", serviceName, grayVersion);
                return null;
            }

            // 随机选择一个灰度实例
            ServiceInstance selectedInstance = selectRandomInstance(grayServiceInstances);
            return selectedInstance.getUri();

        } catch (Exception e) {
            logger.error("Error getting gray service URI: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 随机选择实例
     */
    private ServiceInstance selectRandomInstance(List<ServiceInstance> instances) {
        if (instances.isEmpty()) {
            return null;
        }

        Random random = new Random();
        return instances.get(random.nextInt(instances.size()));
    }

    /**
     * 随机选择实例URL
     */
    private String selectRandomInstanceUrl(List<String> instances) {
        if (instances.isEmpty()) {
            return null;
        }

        Random random = new Random();
        return instances.get(random.nextInt(instances.size()));
    }

    /**
     * 获取服务的所有版本信息
     */
    public Map<String, List<ServiceInstance>> getServiceVersions(String serviceName) {
        List<ServiceInstance> instances = discoveryClient.getInstances(serviceName);

        return instances.stream()
            .collect(Collectors.groupingBy(instance ->
                instance.getMetadata().getOrDefault("version", grayReleaseConfig.getDefaultVersion())
            ));
    }

    /**
     * 动态更新灰度配置
     */
    public void updateGrayRule(String serviceName, GrayReleaseConfig.GrayRule newRule) {
        grayReleaseConfig.getRules().put(serviceName, newRule);
        logger.info("Updated gray rule for service: {}", serviceName);
    }

    /**
     * 获取当前灰度配置
     */
    public GrayReleaseConfig getCurrentConfig() {
        return grayReleaseConfig;
    }
}