# GitLab CI/CD 选择性部署使用指南

## 📋 概述

优化后的 `.gitlab-ci.yml` 文件支持在 GitLab 网页端运行 pipeline 时选择性地构建和部署特定服务，而不是强制构建所有服务。

## 🎯 支持的服务

1. **edu-file-service** (文件服务)
2. **edu-auth-service** (认证服务)

**注意**：`edu-common` (通用模块) 会在任一服务需要构建时自动构建，无需单独配置。

## 🚀 使用方法

### 方法一：GitLab 网页端手动触发 (推荐)

1. 登录 GitLab，进入项目页面
2. 点击左侧菜单 **CI/CD** → **Pipelines**
3. 点击右上角 **Run pipeline** 按钮
4. 在 **Variables** 部分设置以下变量（可选）：

| 变量名 | 可选值 | 默认值 | 说明     |
|--------|--------|--------|--------|
| `DEPLOY_SERVICE_NAME` | `edu-file-service` / `edu-auth-service` | `edu-file-service` | 部署服务名称 |

5. 点击 **Run pipeline** 开始执行

### 快速使用示例

**只部署文件服务：**
```
DEPLOY_SERVICE_NAME: "edu-file-service"
```

**只部署认证服务：**
```
DEPLOY_SERVICE_NAME: "edu-auth-service"
```

**部署所有服务（默认）：**
```
不设置任何变量
```

## 📊 部署场景示例

### 场景1：只部署文件服务
```yaml
Variables:
  DEPLOY_SERVICE_NAME: "edu-file-service"
```

**执行的作业：**
- ✅ build_common (自动构建)
- ✅ build_file_service
- ✅ build_file_service_image
- ✅ deploy_file_service_rancher
- ✅ deployment_summary
- ❌ build_auth_service (跳过)
- ❌ build_auth_service_image (跳过)
- ❌ deploy_auth_service_rancher (跳过)

### 场景2：只部署认证服务
```yaml
Variables:
  DEPLOY_SERVICE_NAME: "edu-auth-service"
```

### 场景3：部署所有服务 (默认行为)
```yaml
Variables:
```

## 🔄 Pipeline 流程

```mermaid
graph TD
    A[build_common] --> B[build_file_service]
    A --> C[build_auth_service]
    B --> D[build_file_service_image]
    C --> E[build_auth_service_image]
    D --> F[deploy_file_service_rancher]
    E --> G[deploy_auth_service_rancher]
    F --> H[deployment_summary]
    G --> H
```

## 🎛️ 高级配置

### 环境变量说明

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `KUBE_NAMESPACE` | Kubernetes 命名空间 | `edu-test` |
| `DOCKER_REPO_FILE_SERVICE` | 文件服务镜像地址 | `registry.example.com/edu-file-service:build-123` |
| `DOCKER_REPO_AUTH_SERVICE` | 认证服务镜像地址 | `registry.example.com/edu-auth-service:build-123` |

### 分支策略

当前配置支持以下分支的自动触发：
- ✅ `prev*` 分支 (预发布环境)
- ✅ `test*` 分支 (测试环境)
- ✅ 手动触发 (任意分支)
- ❌ `master` 分支 (生产环境，需要特殊处理)
- ❌ `dev` 分支 (开发环境，避免频繁部署)

## 🔍 监控和日志

### 查看部署状态
每次 pipeline 执行完成后，`deployment_summary` 作业会输出详细的部署状态：

```
=== 部署状态总结 ===
Pipeline ID: 12345
分支: test/feature-update
命名空间: edu-test

✅ 文件服务已部署: registry.example.com/edu-file-service:build-12345
⏭️  认证服务跳过部署

=== 部署完成 ===
```

### 常见问题排查

1. **作业被跳过**：检查对应的 `DEPLOY_*` 变量是否设置为 `true`
2. **构建失败**：查看具体作业的日志，通常是依赖问题或代码编译错误
3. **部署失败**：检查 Rancher 连接配置和 Kubernetes 集群状态

## 💡 最佳实践

1. **开发阶段**：只部署正在开发的服务，节省时间和资源
2. **测试阶段**：根据测试需求选择性部署相关服务
3. **生产发布**：建议部署所有服务，确保系统完整性
4. **热修复**：只部署有问题的服务，快速修复线上问题

## 🔧 故障排除

如果遇到问题，请检查：
1. GitLab Runner 是否正常运行
2. Docker Registry 连接是否正常
3. Rancher/Kubernetes 集群是否可访问
4. 环境变量是否正确配置

---

**注意**：此配置向后兼容，如果不设置任何变量，默认会构建和部署所有服务。
