# 工作流服务开发指南

## 项目概述

工作流服务是业务管理系统的核心组件之一，基于Flowable引擎提供完整的BPMN流程定义管理功能。

## 技术栈

- **框架**: Spring Boot 3.4.x
- **工作流引擎**: Flowable 7.1.0
- **数据库**: PostgreSQL 17
- **缓存**: Redis 6.0
- **安全**: Spring Security + JWT
- **文档**: Swagger 3.0
- **测试**: JUnit 5 + Mockito

## 项目结构

```
hky-workflow-service/
├── src/main/java/com/hky/hr/workflow/
│   ├── controller/          # 控制器层
│   │   └── ProcessDefinitionController.java
│   ├── service/            # 服务层
│   │   ├── ProcessDefinitionService.java
│   │   └── impl/ProcessDefinitionServiceImpl.java
│   ├── dto/                # 数据传输对象
│   │   ├── ProcessDefinitionDTO.java
│   │   ├── ProcessDefinitionQueryRequest.java
│   │   └── ProcessDefinitionMetadataUpdateRequest.java
│   ├── config/             # 配置类
│   │   └── WorkflowSecurityConfig.java
│   ├── aspect/             # 切面
│   │   └── WorkflowAuditAspect.java
│   └── WorkflowServiceApplication.java
├── src/test/java/          # 测试代码
│   ├── controller/         # 控制器测试
│   ├── service/           # 服务测试
│   └── integration/       # 集成测试
├── docs/                  # 文档
│   ├── API文档.md
│   └── 开发指南.md
└── pom.xml               # Maven配置
```

## 核心功能模块

### 1. 流程定义管理

#### 主要类
- `ProcessDefinitionController`: REST API控制器
- `ProcessDefinitionService`: 业务逻辑接口
- `ProcessDefinitionServiceImpl`: 业务逻辑实现

#### 核心功能
- BPMN文件部署和管理
- 流程定义查询和统计
- 流程图生成和XML获取
- 流程定义状态管理（激活/挂起）

### 2. 版本管理

#### 功能特性
- 多版本并存
- 版本比较和差异分析
- 智能版本切换
- 版本回滚机制

#### 实现原理
```java
// 版本切换实现
public boolean setDefaultVersion(String id) {
    // 1. 激活目标版本
    repositoryService.activateProcessDefinitionById(id);
    
    // 2. 挂起同Key的其他版本
    List<ProcessDefinition> otherVersions = repositoryService
        .createProcessDefinitionQuery()
        .processDefinitionKey(processDefinition.getKey())
        .processDefinitionIdNotEquals(id)
        .active()
        .list();
    
    for (ProcessDefinition otherVersion : otherVersions) {
        repositoryService.suspendProcessDefinitionById(otherVersion.getId());
    }
    
    return true;
}
```

### 3. 导入导出

#### 支持格式
- 单个BPMN文件导入
- ZIP批量导入导出
- 格式验证和错误处理

#### 实现要点
```java
// ZIP文件处理
try (ZipInputStream zis = new ZipInputStream(file.getInputStream())) {
    ZipEntry zipEntry;
    while ((zipEntry = zis.getNextEntry()) != null) {
        if (zipEntry.getName().endsWith(".bpmn")) {
            // 处理BPMN文件
            processDefinitionService.deployProcess(bpmnContent, deployRequest);
        }
    }
}
```

### 4. 安全控制

#### 权限体系
- 基于Spring Security的方法级权限控制
- 细粒度的操作权限划分
- JWT令牌认证

#### 配置示例
```java
@PreAuthorize("hasRole('ADMIN') or hasAuthority('WORKFLOW_DEPLOY')")
@Transactional
public Result<ProcessDefinitionDTO> deployProcess(...) {
    // 部署逻辑
}
```

### 5. 审计日志

#### 功能特性
- AOP切面自动记录
- 操作分类和用户追踪
- 结构化日志输出

## 开发规范

### 1. 代码规范

#### 命名规范
- 类名：大驼峰命名法（PascalCase）
- 方法名：小驼峰命名法（camelCase）
- 常量：全大写下划线分隔（UPPER_SNAKE_CASE）
- 包名：全小写点分隔

#### 注释规范
```java
/**
 * 部署流程定义
 * 
 * @param file BPMN文件
 * @param request 部署请求参数
 * @return 部署后的流程定义信息
 * @throws BusinessException 业务异常
 */
@Operation(summary = "部署流程定义", description = "通过上传BPMN文件部署流程定义")
public Result<ProcessDefinitionDTO> deployProcess(MultipartFile file, ProcessDeploymentRequest request) {
    // 实现逻辑
}
```

### 2. 异常处理

#### 统一异常处理
```java
// 业务异常
throw new BusinessException(ResultCode.NOT_FOUND, "流程定义不存在: " + id);

// 系统异常
try {
    // 业务逻辑
} catch (Exception e) {
    log.error("操作失败: {}", e.getMessage(), e);
    throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "操作失败: " + e.getMessage());
}
```

### 3. 事务管理

#### 事务注解使用
```java
@Transactional(rollbackFor = Exception.class)
public ProcessDefinitionDTO deployProcess(...) {
    // 需要事务保护的操作
}
```

### 4. 测试规范

#### 单元测试
- 测试覆盖率要求：≥90%
- 使用Mockito模拟依赖
- 测试方法命名：`test{功能}_{场景}`

#### 集成测试
- 使用@SpringBootTest
- 测试完整的业务流程
- 使用@Transactional自动回滚

## 部署配置

### 1. 数据库配置

```yaml
spring:
  datasource:
    url: ********************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver
```

### 2. Flowable配置

```yaml
flowable:
  database-schema-update: true
  async-executor-activate: true
  history-level: full
  check-process-definitions: true
```

### 3. 安全配置

```yaml
security:
  jwt:
    secret: ${JWT_SECRET:your-secret-key}
    expiration: 86400000  # 24小时
```

## 性能优化

### 1. 缓存策略

```java
@Cacheable(value = "processDefinitions", key = "#id")
public ProcessDefinitionDTO getProcessDefinitionById(String id) {
    // 查询逻辑
}
```

### 2. 数据库优化

- 为常用查询字段添加索引
- 使用分页查询避免大结果集
- 定期清理历史数据

### 3. 异步处理

```java
@Async
public CompletableFuture<byte[]> generateProcessDiagramAsync(String id) {
    // 异步生成流程图
}
```

## 监控和运维

### 1. 健康检查

```java
@Component
public class WorkflowHealthIndicator implements HealthIndicator {
    @Override
    public Health health() {
        // 检查Flowable引擎状态
        return Health.up().build();
    }
}
```

### 2. 指标监控

- 流程定义部署数量
- API调用次数和响应时间
- 错误率统计

### 3. 日志配置

```yaml
logging:
  level:
    com.hky.hr.workflow: DEBUG
    org.flowable: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

## 常见问题

### 1. Flowable引擎初始化失败
- 检查数据库连接配置
- 确认数据库表是否正确创建
- 查看Flowable版本兼容性

### 2. 权限控制不生效
- 确认@EnableGlobalMethodSecurity配置
- 检查JWT令牌是否正确传递
- 验证用户权限配置

### 3. 流程图生成失败
- 检查字体配置
- 确认图片生成依赖
- 查看内存使用情况

## 扩展开发

### 1. 自定义任务类型

```java
@Component
public class CustomServiceTask implements JavaDelegate {
    @Override
    public void execute(DelegateExecution execution) {
        // 自定义任务逻辑
    }
}
```

### 2. 流程监听器

```java
@Component
public class ProcessEventListener implements FlowableEventListener {
    @Override
    public void onEvent(FlowableEvent event) {
        // 处理流程事件
    }
}
```

### 3. 自定义表单

```java
@RestController
@RequestMapping("/api/v1/forms")
public class FormController {
    // 自定义表单管理
}
```

## 更新日志

### v1.0.0 (2025-06-17)
- 初始版本发布
- 完成核心功能开发
- 添加版本管理和导入导出功能
- 完善安全控制和审计日志
