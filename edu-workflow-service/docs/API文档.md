# 工作流服务API文档

## 概述

工作流服务提供完整的BPMN流程定义管理功能，包括部署、查询、版本管理、导入导出等核心功能。

**服务地址**: `/api/v1/process-definitions`  
**版本**: v1.0.0  
**更新时间**: 2025-06-17

## 认证和授权

### 权限体系

| 权限 | 描述 | 适用操作 |
|------|------|----------|
| `WORKFLOW_READ` | 流程定义查询权限 | GET操作 |
| `WORKFLOW_DEPLOY` | 流程定义部署权限 | 部署操作 |
| `WORKFLOW_DELETE` | 流程定义删除权限 | 删除操作 |
| `WORKFLOW_MANAGE` | 流程定义管理权限 | 版本管理、状态变更 |
| `WORKFLOW_IMPORT` | 流程定义导入权限 | 导入操作 |
| `WORKFLOW_EXPORT` | 流程定义导出权限 | 导出操作 |
| `ADMIN` | 管理员权限 | 所有操作 |

### 角色定义

| 角色 | 包含权限 | 描述 |
|------|----------|------|
| `ROLE_WORKFLOW_USER` | WORKFLOW_READ | 普通用户 |
| `ROLE_WORKFLOW_ADMIN` | 所有WORKFLOW权限 | 工作流管理员 |
| `ROLE_ADMIN` | 所有权限 | 系统管理员 |

## API接口

### 1. 基础CRUD操作

#### 1.1 部署流程定义
```http
POST /api/v1/process-definitions/deploy
Content-Type: multipart/form-data
Authorization: Bearer {token}
```

**权限要求**: `WORKFLOW_DEPLOY` 或 `ADMIN`

**请求参数**:
- `file` (required): BPMN文件
- `name` (required): 部署名称
- `category` (optional): 流程分类
- `tenantId` (optional): 租户ID
- `activate` (optional): 是否激活，默认true
- `description` (optional): 描述

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "process-def-123",
    "key": "leave_approval",
    "name": "请假审批流程",
    "version": 1,
    "category": "人事管理",
    "deploymentId": "deployment-456",
    "suspended": false,
    "deploymentTime": "2025-06-17T10:30:00"
  }
}
```

#### 1.2 分页查询流程定义
```http
POST /api/v1/process-definitions/query
Content-Type: application/json
```

**权限要求**: `WORKFLOW_READ` 或更高权限

**请求体**:
```json
{
  "page": 1,
  "size": 10,
  "key": "leave_approval",
  "name": "请假",
  "category": "人事管理",
  "suspended": false
}
```

#### 1.3 根据ID查询流程定义
```http
GET /api/v1/process-definitions/{id}
```

#### 1.4 删除流程定义
```http
DELETE /api/v1/process-definitions/{deploymentId}?cascade=false
```

**权限要求**: `WORKFLOW_DELETE` 或 `ADMIN`

### 2. 版本管理功能

#### 2.1 获取所有版本
```http
GET /api/v1/process-definitions/{key}/versions
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "process-def-123",
      "key": "leave_approval",
      "version": 2,
      "name": "请假审批流程V2"
    },
    {
      "id": "process-def-124",
      "key": "leave_approval", 
      "version": 1,
      "name": "请假审批流程V1"
    }
  ]
}
```

#### 2.2 设置默认版本
```http
POST /api/v1/process-definitions/{id}/set-default
```

**权限要求**: `WORKFLOW_MANAGE` 或 `ADMIN`

#### 2.3 版本比较
```http
GET /api/v1/process-definitions/{id1}/compare/{id2}
```

#### 2.4 版本回滚
```http
POST /api/v1/process-definitions/{id}/rollback?reason=修复bug
```

**权限要求**: `WORKFLOW_MANAGE` 或 `ADMIN`

### 3. 元数据管理

#### 3.1 更新元数据
```http
PUT /api/v1/process-definitions/{id}/metadata
Content-Type: application/json
```

**请求体**:
```json
{
  "name": "更新后的流程名称",
  "description": "更新后的描述",
  "category": "新分类",
  "enabled": true,
  "updateReason": "优化流程"
}
```

#### 3.2 更新分类
```http
PUT /api/v1/process-definitions/{id}/category?category=新分类
```

#### 3.3 更新描述
```http
PUT /api/v1/process-definitions/{id}/description?description=新描述
```

### 4. 导入导出功能

#### 4.1 批量导出
```http
GET /api/v1/process-definitions/export?ids=id1,id2,id3
```

**权限要求**: `WORKFLOW_EXPORT` 或 `ADMIN`

**响应**: ZIP文件下载

#### 4.2 批量导入
```http
POST /api/v1/process-definitions/import
Content-Type: multipart/form-data
```

**权限要求**: `WORKFLOW_IMPORT` 或 `ADMIN`

**请求参数**:
- `file` (required): ZIP文件
- `overwrite` (optional): 是否覆盖，默认false

**响应示例**:
```json
{
  "success": true,
  "data": {
    "total": 5,
    "success": 4,
    "failure": 1,
    "skipped": 0,
    "successList": ["process1.bpmn", "process2.bpmn"],
    "failureList": ["invalid.bpmn: 格式错误"],
    "skippedList": [],
    "importTime": "2025-06-17T10:30:00"
  }
}
```

#### 4.3 验证BPMN文件
```http
POST /api/v1/process-definitions/validate
Content-Type: multipart/form-data
```

**请求参数**:
- `file` (required): BPMN文件

#### 4.4 获取模板
```http
GET /api/v1/process-definitions/template?type=basic
```

**模板类型**:
- `basic`: 基础流程模板
- `approval`: 审批流程模板
- `sequential`: 顺序流程模板

### 5. 流程图和统计

#### 5.1 生成流程图
```http
GET /api/v1/process-definitions/{id}/diagram
```

**响应**: PNG图片

#### 5.2 获取统计信息
```http
GET /api/v1/process-definitions/{id}/statistics
```

#### 5.3 获取XML内容
```http
GET /api/v1/process-definitions/{id}/xml
```

## 错误码

| 错误码 | 描述 | HTTP状态码 |
|--------|------|------------|
| 200 | 成功 | 200 |
| 400 | 请求参数错误 | 400 |
| 401 | 未认证 | 401 |
| 403 | 权限不足 | 403 |
| 404 | 资源不存在 | 404 |
| 500 | 服务器内部错误 | 500 |

## 使用示例

### 完整的流程定义管理流程

```bash
# 1. 部署流程定义
curl -X POST "http://localhost:8080/api/v1/process-definitions/deploy" \
  -H "Authorization: Bearer {token}" \
  -F "file=@leave_approval.bpmn" \
  -F "name=请假审批流程" \
  -F "category=人事管理"

# 2. 查询流程定义
curl -X GET "http://localhost:8080/api/v1/process-definitions/process-def-123" \
  -H "Authorization: Bearer {token}"

# 3. 获取流程图
curl -X GET "http://localhost:8080/api/v1/process-definitions/process-def-123/diagram" \
  -H "Authorization: Bearer {token}" \
  -o process-diagram.png

# 4. 导出流程定义
curl -X GET "http://localhost:8080/api/v1/process-definitions/export?ids=process-def-123" \
  -H "Authorization: Bearer {token}" \
  -o exported-processes.zip
```

## 注意事项

1. **文件大小限制**: BPMN文件最大10MB，ZIP文件最大50MB
2. **并发限制**: 同时导入的文件数量不超过100个
3. **版本管理**: 同一Key的流程定义支持最多50个版本
4. **缓存策略**: 流程定义查询结果缓存5分钟
5. **审计日志**: 所有重要操作都会记录审计日志

## 更新日志

### v1.0.0 (2025-06-17)
- 新增版本管理功能
- 新增导入导出功能
- 新增元数据管理功能
- 完善安全控制和权限管理
- 添加审计日志功能
