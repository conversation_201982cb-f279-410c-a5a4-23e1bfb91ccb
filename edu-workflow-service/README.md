# 业务管理系统 - 工作流服务

## 📋 服务概述

工作流服务是业务管理系统的核心平台服务之一，基于Flowable 7.1工作流引擎构建，负责管理系统中所有业务流程的建模、执行、监控和优化，为各业务模块提供统一的审批流程支持。

## 🎯 核心功能

### 🔄 流程管理功能
- **流程建模**：支持BPMN 2.0标准的流程建模，可视化流程设计器
- **流程部署**：支持流程定义的版本管理和动态部署
- **流程启动**：支持多种方式启动流程实例，包括API调用和事件触发
- **流程监控**：实时监控流程执行状态和性能指标

### 📋 任务管理功能
- **任务分配**：支持用户任务、候选组任务的智能分配
- **任务处理**：支持任务的认领、完成、委派、转办等操作
- **任务查询**：提供待办任务、已办任务的多维度查询
- **任务超时**：支持任务超时提醒和自动处理

### 🎨 流程设计功能
- **可视化建模**：集成Flowable Modeler，支持在线流程设计
- **表单集成**：支持动态表单配置和数据绑定
- **条件路由**：支持复杂的条件判断和分支路由
- **子流程**：支持嵌套子流程和调用活动

### 📊 监控分析功能
- **流程监控**：实时监控流程实例的执行状态
- **性能分析**：分析流程执行效率和瓶颈节点
- **统计报表**：提供流程执行的统计分析报表
- **历史查询**：完整的流程执行历史记录查询

## 🏗️ 技术架构

### 技术栈
- **工作流引擎**：Flowable 7.1
- **框架**：Spring Boot 3.4
- **数据库**：PostgreSQL（流程数据存储）
- **缓存**：Redis
- **安全**：Spring Security
- **文档**：SpringDoc OpenAPI 3
- **标准**：BPMN 2.0

### 架构特点
- **微服务架构**：独立部署，松耦合
- **标准化建模**：遵循BPMN 2.0国际标准
- **高性能引擎**：基于Flowable高性能工作流引擎
- **可视化设计**：提供直观的流程设计界面
- **灵活扩展**：支持自定义任务类型和监听器

## 📊 数据模型

### 流程定义信息表 (wf_process_definition_info)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| process_key | VARCHAR(64) | 流程键 |
| process_name | VARCHAR(128) | 流程名称 |
| process_category | VARCHAR(64) | 流程分类 |
| description | VARCHAR(500) | 流程描述 |
| version | INTEGER | 版本号 |
| status | VARCHAR(20) | 状态 |
| flowable_definition_id | VARCHAR(64) | Flowable流程定义ID |
| flowable_deployment_id | VARCHAR(64) | Flowable部署ID |
| bpmn_xml | TEXT | BPMN XML内容 |
| form_config | TEXT | 表单配置JSON |
| approval_config | TEXT | 审批配置JSON |
| is_default | BOOLEAN | 是否默认版本 |
| sort_order | INTEGER | 排序顺序 |

### 流程实例信息表 (wf_process_instance_info)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| flowable_instance_id | VARCHAR(64) | Flowable流程实例ID |
| process_key | VARCHAR(64) | 流程键 |
| process_name | VARCHAR(128) | 流程名称 |
| business_key | VARCHAR(64) | 业务键 |
| business_type | VARCHAR(64) | 业务类型 |
| business_id | VARCHAR(64) | 业务ID |
| initiator_id | BIGINT | 发起人ID |
| initiator_username | VARCHAR(64) | 发起人用户名 |
| initiator_name | VARCHAR(64) | 发起人姓名 |
| start_time | TIMESTAMP | 开始时间 |
| end_time | TIMESTAMP | 结束时间 |
| status | VARCHAR(20) | 状态 |
| current_activity | VARCHAR(64) | 当前节点 |
| current_activity_name | VARCHAR(128) | 当前节点名称 |
| title | VARCHAR(255) | 流程标题 |
| description | VARCHAR(500) | 流程描述 |
| priority | INTEGER | 优先级 |
| duration | BIGINT | 持续时间(毫秒) |
| variables | TEXT | 流程变量JSON |
| end_reason | VARCHAR(500) | 结束原因 |

### 任务信息表 (wf_task_info)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| flowable_task_id | VARCHAR(64) | Flowable任务ID |
| process_instance_id | VARCHAR(64) | 流程实例ID |
| process_key | VARCHAR(64) | 流程键 |
| task_key | VARCHAR(64) | 任务键 |
| task_name | VARCHAR(128) | 任务名称 |
| task_description | VARCHAR(500) | 任务描述 |
| assignee_id | BIGINT | 指派人ID |
| assignee_username | VARCHAR(64) | 指派人用户名 |
| assignee_name | VARCHAR(64) | 指派人姓名 |
| candidate_groups | VARCHAR(64) | 候选组 |
| candidate_users | VARCHAR(255) | 候选用户 |
| task_create_time | TIMESTAMP | 任务创建时间 |
| claim_time | TIMESTAMP | 认领时间 |
| complete_time | TIMESTAMP | 完成时间 |
| due_date | TIMESTAMP | 到期时间 |
| status | VARCHAR(20) | 状态 |
| priority | INTEGER | 优先级 |
| duration | BIGINT | 持续时间(毫秒) |
| approval_result | VARCHAR(20) | 审批结果 |
| approval_comment | VARCHAR(500) | 审批意见 |
| form_data | TEXT | 表单数据JSON |
| variables | TEXT | 任务变量JSON |
| business_key | VARCHAR(64) | 业务键 |
| business_type | VARCHAR(64) | 业务类型 |

## 🚀 API接口

### 核心接口

#### 1. 启动流程实例
```http
POST /api/v1/workflow/processes/start

请求体:
{
  "processKey": "leave_approval",
  "businessKey": "LEAVE_20241219_001",
  "businessType": "LEAVE_APPLICATION",
  "businessId": "123",
  "initiatorId": 1,
  "initiatorUsername": "zhangsan",
  "initiatorName": "张三",
  "title": "张三的请假申请",
  "description": "因事请假2天",
  "priority": 50,
  "variables": {
    "leaveType": "事假",
    "leaveDays": 2,
    "startDate": "2024-12-20",
    "endDate": "2024-12-21"
  }
}
```

#### 2. 完成任务
```http
POST /api/v1/workflow/tasks/{taskId}/complete

请求体:
{
  "assigneeId": 2,
  "assigneeUsername": "manager",
  "assigneeName": "李经理",
  "approvalResult": "APPROVED",
  "approvalComment": "同意请假",
  "variables": {
    "managerApproval": true,
    "approvalTime": "2024-12-19T10:30:00"
  }
}
```

#### 3. 查询待办任务
```http
GET /api/v1/workflow/tasks/pending?username=manager&page=1&size=20

响应:
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "data": [...],
    "total": 50,
    "page": 1,
    "size": 20
  }
}
```

#### 4. 查询流程实例
```http
GET /api/v1/workflow/processes/{processInstanceId}

响应:
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "flowableInstanceId": "12345",
    "processKey": "leave_approval",
    "processName": "请假审批流程",
    "status": "RUNNING",
    "currentActivity": "manager_approval",
    "currentActivityName": "部门经理审批"
  }
}
```

### 完整API文档
启动服务后访问: http://localhost:8006/swagger-ui.html

## 🎨 支持的业务流程

### 考勤管理流程
- **请假审批**：员工请假申请的多级审批流程
- **调休审批**：调休申请的审批流程
- **加班审批**：加班申请和加班费审批流程

### 人事变动流程
- **入职审批**：新员工入职的审批流程
- **调岗审批**：员工岗位调整的审批流程
- **离职审批**：员工离职的审批流程
- **退休审批**：员工退休的审批流程

### 合同管理流程
- **合同签订**：新合同签订的审批流程
- **合同续签**：合同续签的审批流程
- **合同变更**：合同条款变更的审批流程
- **合同终止**：合同终止的审批流程

### 薪酬福利流程
- **薪酬调整**：员工薪酬调整的审批流程
- **福利申请**：各类福利申请的审批流程
- **补贴申请**：各类补贴申请的审批流程

### 招聘管理流程
- **招聘计划**：招聘计划的审批流程
- **面试安排**：面试安排的审批流程
- **录用决定**：录用决定的审批流程

### 师资发展流程
- **培训申请**：培训申请的审批流程
- **进修申请**：进修申请的审批流程
- **学术活动**：学术活动申请的审批流程

### 考核管理流程
- **年度考核**：年度考核的审批流程
- **专项考核**：专项考核的审批流程

### 职称评聘流程
- **职称申报**：职称申报的审批流程
- **职称评审**：职称评审的审批流程
- **职称聘任**：职称聘任的审批流程

## 🚀 快速开始

### 1. 环境准备
```bash
# 启动基础设施服务
docker-compose up -d postgresql redis

# 等待服务启动完成
docker-compose ps
```

### 2. 编译运行
```bash
# 编译项目
mvn clean compile

# 运行服务
mvn spring-boot:run

# 或者打包后运行
mvn clean package
java -jar target/hky-workflow-service-1.0.0-SNAPSHOT.jar
```

### 3. 验证服务
```bash
# 健康检查
curl http://localhost:8006/api/v1/workflow/health

# 查看API文档
open http://localhost:8006/swagger-ui.html

# 访问Flowable UI
open http://localhost:8006/flowable-ui
```

## 🎨 流程设计

### 1. 访问流程设计器
```
URL: http://localhost:8006/flowable-ui/modeler
```

### 2. 创建流程定义
- 使用可视化设计器创建BPMN 2.0流程
- 配置任务节点、网关、事件等
- 设置任务分配规则和表单

### 3. 部署流程
- 保存并部署流程定义
- 设置流程版本和激活状态

## 🔍 监控与运维

### 健康检查
- **应用健康**: GET /api/v1/workflow/health
- **系统健康**: GET /actuator/health
- **Flowable健康**: GET /actuator/flowable

### 流程监控
- **流程管理**: http://localhost:8006/flowable-ui/admin
- **任务管理**: http://localhost:8006/flowable-ui/task
- **流程监控**: 实时监控流程执行状态

### 性能监控
- **Prometheus**: GET /actuator/prometheus
- **JVM指标**: 内存、GC、线程等
- **业务指标**: 流程执行数、任务处理时间等

## ⚙️ 配置说明

### 核心配置
```yaml
hky:
  workflow:
    process-definition-cache-size: 100    # 流程定义缓存大小
    task-timeout-check-interval: 30       # 任务超时检查间隔(分钟)
    process-instance-timeout-hours: 72    # 流程实例超时时间(小时)
    task-default-timeout-hours: 24        # 任务默认超时时间(小时)
    enable-process-diagram: true          # 是否启用流程图生成
    diagram-font: 宋体                    # 流程图字体
```

### Flowable配置
```yaml
flowable:
  database-schema-update: true           # 自动更新数据库结构
  history-level: full                    # 历史级别
  async-executor-activate: true          # 启用异步执行器
  enable-safe-xml: true                  # 启用安全XML验证
```

## 🔒 安全机制

### 权限控制
- `workflow:start` - 启动流程权限
- `workflow:task:view` - 查看任务权限
- `workflow:task:complete` - 完成任务权限
- `workflow:task:claim` - 认领任务权限
- `workflow:task:delegate` - 委派任务权限
- `workflow:task:transfer` - 转办任务权限
- `workflow:process:view` - 查看流程权限
- `workflow:process:manage` - 管理流程权限
- `workflow:process:terminate` - 终止流程权限

### 数据安全
- **流程数据隔离**: 不同业务流程数据隔离
- **任务权限验证**: 严格的任务操作权限验证
- **审计日志**: 完整的流程操作审计日志

## 📈 性能优化

### 引擎优化
- **异步执行**: 启用异步任务执行器
- **数据库优化**: 针对流程查询优化索引
- **缓存机制**: 流程定义和实例缓存

### 查询优化
- **分页查询**: 避免大数据量一次性加载
- **索引优化**: 针对常用查询字段建立索引
- **历史数据**: 定期清理过期历史数据

## 🧪 测试

### 单元测试
```bash
mvn test
```

### 集成测试
```bash
mvn integration-test
```

### 流程测试
使用Flowable Test框架进行流程单元测试

## 📝 开发指南

### 自定义任务类型
1. 实现JavaDelegate接口
2. 注册为Spring Bean
3. 在流程中配置使用

### 自定义监听器
1. 实现相应的监听器接口
2. 配置监听器到流程节点
3. 处理流程事件

### 表单集成
1. 设计动态表单配置
2. 实现表单数据绑定
3. 配置表单验证规则

## 📋 版本历史

- **v1.0.0**: 初始版本，实现基于Flowable 7.1的工作流引擎，支持流程建模、执行、任务管理和监控等核心功能

## 📞 联系方式

- 项目负责人: HKY-HR-System
- 技术支持: <EMAIL>
- 文档更新: 2024-12-19
