package com.hzwangda.edu.workflow.enums;

/**
 * 流程分类枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum ProcessCategory {

    // 考勤管理相关流程
    ATTENDANCE("考勤管理", "请假、调休、加班等考勤相关审批流程"),

    // 人事变动相关流程
    PERSONNEL_CHANGE("人事变动", "入职、调岗、离职、退休等人事变动审批流程"),

    // 合同管理相关流程
    CONTRACT("合同管理", "合同签订、续签、变更、终止等审批流程"),

    // 薪酬福利相关流程
    COMPENSATION("薪酬福利", "薪酬调整、福利申请等审批流程"),

    // 招聘管理相关流程
    RECRUITMENT("招聘管理", "招聘计划、面试安排、录用决定等审批流程"),

    // 师资发展相关流程
    FACULTY_DEVELOPMENT("师资发展", "培训申请、进修申请、学术活动等审批流程"),

    // 考核管理相关流程
    APPRAISAL("考核管理", "年度考核、专项考核等审批流程"),

    // 职称评聘相关流程
    TITLE_EVALUATION("职称评聘", "职称申报、评审、聘任等审批流程"),

    // 组织管理相关流程
    ORGANIZATION("组织管理", "机构设置、调整、撤销等审批流程"),

    // 员工信息相关流程
    EMPLOYEE_INFO("员工信息", "个人信息修改、证件变更等审批流程"),

    // 综合服务相关流程
    INTEGRATED_SERVICE("综合服务", "各类综合服务申请审批流程"),

    // 系统管理相关流程
    SYSTEM("系统管理", "系统配置、权限变更等审批流程"),

    // 其他流程
    OTHER("其他", "其他类型的审批流程");

    private final String description;
    private final String detail;

    ProcessCategory(String description, String detail) {
        this.description = description;
        this.detail = detail;
    }

    public String getDescription() {
        return description;
    }

    public String getDetail() {
        return detail;
    }

    public String getCode() {
        return this.name();
    }

    /**
     * 根据代码获取枚举
     */
    public static ProcessCategory fromCode(String code) {
        for (ProcessCategory category : values()) {
            if (category.getCode().equals(code)) {
                return category;
            }
        }
        return OTHER;
    }
}
