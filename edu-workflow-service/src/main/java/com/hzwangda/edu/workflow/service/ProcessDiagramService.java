package com.hzwangda.edu.workflow.service;

import java.util.List;

/**
 * 流程图服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public interface ProcessDiagramService {

    /**
     * 生成流程定义图
     *
     * @param processDefinitionId 流程定义ID
     * @return 流程图字节数组
     */
    byte[] generateProcessDefinitionDiagram(String processDefinitionId);

    /**
     * 生成流程实例图（高亮当前节点）
     *
     * @param processInstanceId 流程实例ID
     * @return 流程图字节数组
     */
    byte[] generateProcessInstanceDiagram(String processInstanceId);

    /**
     * 生成流程实例图（高亮指定节点）
     *
     * @param processInstanceId 流程实例ID
     * @param highlightedActivities 高亮的活动节点
     * @param highlightedFlows 高亮的流程线
     * @return 流程图字节数组
     */
    byte[] generateProcessInstanceDiagram(String processInstanceId,
                                        List<String> highlightedActivities,
                                        List<String> highlightedFlows);

    /**
     * 生成SVG格式的流程图
     *
     * @param processDefinitionId 流程定义ID
     * @return SVG内容
     */
    String generateProcessDefinitionSvg(String processDefinitionId);

    /**
     * 生成SVG格式的流程实例图
     *
     * @param processInstanceId 流程实例ID
     * @return SVG内容
     */
    String generateProcessInstanceSvg(String processInstanceId);

    /**
     * 清理流程图缓存
     *
     * @param processDefinitionId 流程定义ID
     */
    void clearDiagramCache(String processDefinitionId);

    /**
     * 清理所有流程图缓存
     */
    void clearAllDiagramCache();

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    Object getDiagramCacheStats();
}
