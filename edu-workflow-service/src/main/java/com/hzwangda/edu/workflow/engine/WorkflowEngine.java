package com.hzwangda.edu.workflow.engine;

import com.hzwangda.edu.workflow.dto.*;
import com.hzwangda.edu.workflow.entity.ProcessDefinitionInfo;
import com.hzwangda.edu.workflow.entity.ProcessInstanceInfo;
import com.hzwangda.edu.workflow.entity.TaskInfo;
import com.hzwangda.edu.workflow.enums.ProcessStatus;
import com.hzwangda.edu.workflow.enums.TaskStatus;
import com.hzwangda.edu.workflow.repository.ProcessDefinitionInfoRepository;
import com.hzwangda.edu.workflow.repository.ProcessInstanceInfoRepository;
import com.hzwangda.edu.workflow.repository.TaskInfoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工作流引擎核心服务
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkflowEngine {

    private final ProcessDefinitionInfoRepository processDefinitionInfoRepository;
    private final ProcessInstanceInfoRepository processInstanceInfoRepository;
    private final TaskInfoRepository taskInfoRepository;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 启动工作流实例
     *
     * @param request 启动请求
     * @return 流程实例
     */
    @Transactional
    public ProcessInstanceResponse startProcess(ProcessStartRequest request) {
        log.info("启动工作流: processKey={}, businessKey={}", request.getProcessKey(), request.getBusinessKey());

        // 1. 获取流程定义
        ProcessDefinitionInfo processDefinition = processDefinitionInfoRepository
            .findByProcessKeyAndActiveTrue(request.getProcessKey())
            .orElseThrow(() -> new IllegalArgumentException("流程定义不存在: " + request.getProcessKey()));

        // 2. 创建流程实例
        ProcessInstanceInfo processInstance = new ProcessInstanceInfo();
        processInstance.setFlowableInstanceId("temp_" + System.currentTimeMillis()); // 临时ID，后续会更新
        processInstance.setProcessKey(request.getProcessKey());
        processInstance.setBusinessKey(request.getBusinessKey());
        processInstance.setProcessName(processDefinition.getProcessName());
        processInstance.setInitiatorId(request.getInitiatorId());
        processInstance.setInitiatorName(request.getInitiatorName());
        processInstance.setStatus(ProcessStatus.RUNNING.name());
        processInstance.setStartTime(LocalDateTime.now());
        processInstance.setVariables(convertMapToJson(request.getVariables()));
        processInstance.setCreateTime(LocalDateTime.now());
        processInstance.setCreateBy(request.getInitiatorId().toString());

        processInstance = processInstanceInfoRepository.save(processInstance);

        // 3. 创建第一个任务
        TaskInfo firstTask = createFirstTask(processDefinition, processInstance, request);

        // 4. 发布流程启动事件
        publishProcessStartedEvent(processInstance, firstTask);

        return convertToResponse(processInstance);
    }

    /**
     * 完成任务
     *
     * @param taskId 任务ID
     * @param request 完成请求
     * @return 任务结果
     */
    @Transactional
    public TaskCompleteResponse completeTask(String taskId, TaskCompleteRequest request) {
        log.info("完成任务: taskId={}, assignee={}", taskId, request.getAssigneeId());

        // 1. 获取任务实例
        TaskInfo taskInstance = taskInfoRepository.findById(Long.parseLong(taskId))
            .orElseThrow(() -> new IllegalArgumentException("任务不存在: " + taskId));

        // 2. 验证任务状态和权限
        validateTaskCompletion(taskInstance, request);

        // 3. 更新任务状态
        taskInstance.setStatus(TaskStatus.COMPLETED.name());
        taskInstance.setAssigneeId(request.getAssigneeId());
        taskInstance.setAssigneeName(request.getAssigneeName());
        taskInstance.setCompleteTime(LocalDateTime.now());
        taskInstance.setApprovalComment(request.getComment());
        taskInstance.setApprovalResult(request.getResult());
        taskInstance.setVariables(convertMapToJson(request.getVariables()));
        taskInstance.setUpdateTime(LocalDateTime.now());
        taskInstance.setUpdateBy(String.valueOf(request.getAssigneeId()));

        taskInstance = taskInfoRepository.save(taskInstance);

        // 4. 获取流程实例
        ProcessInstanceInfo processInstance = processInstanceInfoRepository.findById(Long.parseLong(taskInstance.getProcessInstanceId()))
            .orElseThrow(() -> new IllegalArgumentException("流程实例不存在"));

        // 5. 处理流程流转
        TaskCompleteResponse response = processTaskCompletion(processInstance, taskInstance, request);

        // 6. 发布任务完成事件
        publishTaskCompletedEvent(taskInstance, processInstance);

        return response;
    }

    /**
     * 获取待办任务列表
     *
     * @param assigneeId 处理人ID
     * @return 待办任务列表
     */
    public List<TaskInstanceResponse> getPendingTasks(String assigneeId) {
        log.debug("获取待办任务: assigneeId={}", assigneeId);

        List<TaskInfo> tasks = taskInfoRepository
            .findByAssigneeIdAndStatusOrderByCreateTimeDesc(Long.parseLong(assigneeId), TaskStatus.PENDING.name());

        return tasks.stream()
            .map(this::convertToTaskResponse)
            .collect(Collectors.toList());
    }

    /**
     * 获取流程实例详情
     *
     * @param processInstanceId 流程实例ID
     * @return 流程实例详情
     */
    public ProcessInstanceDetailResponse getProcessInstanceDetail(String processInstanceId) {
        log.debug("获取流程实例详情: processInstanceId={}", processInstanceId);

        ProcessInstanceInfo processInstance = processInstanceInfoRepository.findById(Long.parseLong(processInstanceId))
            .orElseThrow(() -> new IllegalArgumentException("流程实例不存在: " + processInstanceId));

        List<TaskInfo> tasks = taskInfoRepository
            .findByProcessInstanceIdOrderByCreateTimeAsc(processInstanceId);

        ProcessInstanceDetailResponse response = new ProcessInstanceDetailResponse();
        response.setProcessInstance(convertToResponse(processInstance));
        response.setTasks(tasks.stream()
            .map(this::convertToTaskResponse)
            .collect(Collectors.toList()));

        return response;
    }

    /**
     * 撤回流程
     *
     * @param processInstanceId 流程实例ID
     * @param request 撤回请求
     */
    @Transactional
    public void withdrawProcess(String processInstanceId, ProcessWithdrawRequest request) {
        log.info("撤回流程: processInstanceId={}, initiator={}", processInstanceId, request.getInitiatorId());

        ProcessInstanceInfo processInstance = processInstanceInfoRepository.findById(Long.parseLong(processInstanceId))
            .orElseThrow(() -> new IllegalArgumentException("流程实例不存在: " + processInstanceId));

        // 验证撤回权限
        if (!processInstance.getInitiatorId().toString().equals(request.getInitiatorId())) {
            throw new IllegalArgumentException("只有流程发起人可以撤回流程");
        }

        if (!ProcessStatus.RUNNING.name().equals(processInstance.getStatus())) {
            throw new IllegalArgumentException("只能撤回运行中的流程");
        }

        // 更新流程状态
        processInstance.setStatus(ProcessStatus.CANCELLED.name());
        processInstance.setEndTime(LocalDateTime.now());
        processInstance.setEndReason(request.getReason());
        processInstance.setUpdateTime(LocalDateTime.now());
        processInstance.setUpdateBy(request.getInitiatorId());

        processInstanceInfoRepository.save(processInstance);

        // 取消所有待办任务
        List<TaskInfo> pendingTasks = taskInfoRepository
            .findByProcessInstanceIdAndStatus(processInstanceId, TaskStatus.PENDING.name());

        for (TaskInfo task : pendingTasks) {
            task.setStatus(TaskStatus.CANCELLED.name());
            task.setUpdateTime(LocalDateTime.now());
            task.setUpdateBy(request.getInitiatorId());
        }

        taskInfoRepository.saveAll(pendingTasks);

        // 发布流程撤回事件
        publishProcessWithdrawnEvent(processInstance);
    }

    /**
     * 获取流程统计信息
     *
     * @param request 统计请求
     * @return 统计结果
     */
    public ProcessStatisticsResponse getProcessStatistics(ProcessStatisticsRequest request) {
        log.debug("获取流程统计信息: {}", request);

        ProcessStatisticsResponse response = new ProcessStatisticsResponse();

        // 按状态统计流程实例 - 简化实现
        // Map<ProcessStatus, Long> statusCounts = processInstanceInfoRepository
        //     .countByStatusGroupByStatus(request.getStartTime(), request.getEndTime());
        // response.setStatusStatistics(statusCounts);

        // 按流程类型统计 - 简化实现
        // Map<String, Long> processKeyCounts = processInstanceInfoRepository
        //     .countByProcessKeyGroupByProcessKey(request.getStartTime(), request.getEndTime());
        // response.setProcessKeyStatistics(processKeyCounts);

        // 平均处理时长 - 简化实现
        // Double avgDuration = processInstanceInfoRepository
        //     .getAverageProcessDuration(request.getStartTime(), request.getEndTime());
        // response.setAverageProcessDuration(avgDuration);

        // 待办任务统计
        Long pendingTaskCount = taskInfoRepository.countByStatus(TaskStatus.PENDING.name());
        response.setPendingTaskCount(pendingTaskCount);

        return response;
    }

    // ==================== 私有方法 ====================

    /**
     * 创建第一个任务
     */
    private TaskInfo createFirstTask(ProcessDefinitionInfo processDefinition,
                                       ProcessInstanceInfo processInstance,
                                       ProcessStartRequest request) {

        // 解析流程定义获取第一个节点
        Map<String, Object> firstNode = parseFirstNode(processDefinition.getBpmnXml());

        TaskInfo task = new TaskInfo();
        task.setProcessInstanceId(processInstance.getId().toString());
        task.setTaskKey((String) firstNode.get("taskKey"));
        task.setTaskName((String) firstNode.get("taskName"));
        task.setAssigneeId((Long) firstNode.get("assigneeId"));
        task.setAssigneeName((String) firstNode.get("assigneeName"));
        task.setStatus(TaskStatus.PENDING.name());
        task.setCreateTime(LocalDateTime.now());
        task.setCreateBy(request.getInitiatorId().toString());

        return taskInfoRepository.save(task);
    }

    /**
     * 解析流程定义的第一个节点
     */
    private Map<String, Object> parseFirstNode(String processDefinition) {
        // 简化实现，实际应该解析BPMN或自定义流程定义
        Map<String, Object> firstNode = new HashMap<>();
        firstNode.put("taskKey", "start_task");
        firstNode.put("taskName", "开始任务");
        firstNode.put("assigneeId", 1L); // 使用Long类型
        firstNode.put("assigneeName", "管理员");
        return firstNode;
    }

    /**
     * 验证任务完成
     */
    private void validateTaskCompletion(TaskInfo taskInstance, TaskCompleteRequest request) {
        if (!TaskStatus.PENDING.name().equals(taskInstance.getStatus())) {
            throw new IllegalArgumentException("任务已完成或已取消");
        }

        if (!taskInstance.getAssigneeId().toString().equals(request.getAssigneeId())) {
            throw new IllegalArgumentException("只有任务处理人可以完成任务");
        }
    }

    /**
     * 处理任务完成后的流程流转
     */
    private TaskCompleteResponse processTaskCompletion(ProcessInstanceInfo processInstance,
                                                     TaskInfo completedTask,
                                                     TaskCompleteRequest request) {

        TaskCompleteResponse response = new TaskCompleteResponse();
        response.setTaskId(completedTask.getId().toString());
        response.setProcessInstanceId(processInstance.getId().toString());
        response.setCompleted(true);

        // 根据任务结果决定下一步
        String nextAction = determineNextAction(completedTask, request);

        switch (nextAction) {
            case "CONTINUE":
                // 创建下一个任务
                TaskInfo nextTask = createNextTask(processInstance, completedTask, request);
                response.setNextTaskId(nextTask.getId().toString());
                response.setProcessCompleted(false);
                break;

            case "APPROVE":
                // 流程通过
                completeProcess(processInstance, ProcessStatus.APPROVED.name());
                response.setProcessCompleted(true);
                response.setProcessResult("APPROVED");
                break;

            case "REJECT":
                // 流程拒绝
                completeProcess(processInstance, ProcessStatus.REJECTED.name());
                response.setProcessCompleted(true);
                response.setProcessResult("REJECTED");
                break;

            default:
                throw new IllegalArgumentException("未知的流程流转结果: " + nextAction);
        }

        return response;
    }

    /**
     * 确定下一步操作
     */
    private String determineNextAction(TaskInfo completedTask, TaskCompleteRequest request) {
        // 简化实现，实际应该根据流程定义和任务结果决定
        String result = request.getResult();

        if ("APPROVED".equals(result)) {
            // 检查是否还有下一个节点
            return hasNextNode(completedTask) ? "CONTINUE" : "APPROVE";
        } else if ("REJECTED".equals(result)) {
            return "REJECT";
        } else {
            return "CONTINUE";
        }
    }

    /**
     * 检查是否有下一个节点
     */
    private boolean hasNextNode(TaskInfo currentTask) {
        // 简化实现，实际应该解析流程定义
        return !"final_task".equals(currentTask.getTaskKey());
    }

    /**
     * 创建下一个任务
     */
    private TaskInfo createNextTask(ProcessInstanceInfo processInstance,
                                      TaskInfo currentTask,
                                      TaskCompleteRequest request) {

        // 解析下一个节点信息
        Map<String, Object> nextNode = parseNextNode(currentTask, request);

        TaskInfo nextTask = new TaskInfo();
        nextTask.setProcessInstanceId(processInstance.getId().toString());
        nextTask.setTaskKey((String) nextNode.get("taskKey"));
        nextTask.setTaskName((String) nextNode.get("taskName"));
        nextTask.setAssigneeId((Long) nextNode.get("assigneeId"));
        nextTask.setAssigneeName((String) nextNode.get("assigneeName"));
        nextTask.setStatus(TaskStatus.PENDING.name());
        nextTask.setCreateTime(LocalDateTime.now());
        nextTask.setCreateBy(String.valueOf(request.getAssigneeId()));

        return taskInfoRepository.save(nextTask);
    }

    /**
     * 解析下一个节点
     */
    private Map<String, Object> parseNextNode(TaskInfo currentTask, TaskCompleteRequest request) {
        // 简化实现，实际应该根据流程定义解析
        Map<String, Object> nextNode = new HashMap<>();
        nextNode.put("taskKey", "next_task");
        nextNode.put("taskName", "下一个任务");
        nextNode.put("assigneeId", 1L); // 使用Long类型
        nextNode.put("assigneeName", "下一个处理人");
        return nextNode;
    }

    /**
     * 完成流程
     */
    private void completeProcess(ProcessInstanceInfo processInstance, String status) {
        processInstance.setStatus(status);
        processInstance.setEndTime(LocalDateTime.now());
        processInstance.setUpdateTime(LocalDateTime.now());

        processInstanceInfoRepository.save(processInstance);

        // 发布流程完成事件
        publishProcessCompletedEvent(processInstance);
    }

    /**
     * 转换为响应对象
     */
    private ProcessInstanceResponse convertToResponse(ProcessInstanceInfo processInstance) {
        ProcessInstanceResponse response = new ProcessInstanceResponse();
        response.setId(processInstance.getId());
        response.setProcessKey(processInstance.getProcessKey());
        response.setBusinessKey(processInstance.getBusinessKey());
        response.setProcessName(processInstance.getProcessName());
        response.setInitiatorId(processInstance.getInitiatorId());
        response.setInitiatorName(processInstance.getInitiatorName());
        response.setStatus(processInstance.getStatus());
        response.setStartTime(processInstance.getStartTime());
        response.setEndTime(processInstance.getEndTime());
        response.setVariables(convertJsonToMap(processInstance.getVariables()));
        return response;
    }

    /**
     * 转换为任务响应对象
     */
    private TaskInstanceResponse convertToTaskResponse(TaskInfo taskInstance) {
        TaskInstanceResponse response = new TaskInstanceResponse();
        response.setId(taskInstance.getId().toString());
        response.setProcessInstanceId(taskInstance.getProcessInstanceId());
        response.setTaskKey(taskInstance.getTaskKey());
        response.setTaskName(taskInstance.getTaskName());
        response.setAssigneeId(taskInstance.getAssigneeId() != null ? taskInstance.getAssigneeId().toString() : null);
        response.setAssigneeName(taskInstance.getAssigneeName());
        response.setStatus(taskInstance.getStatus());
        response.setCreateTime(taskInstance.getCreateTime());
        response.setCompleteTime(taskInstance.getCompleteTime());
        response.setComment(taskInstance.getApprovalComment());
        response.setResult(taskInstance.getApprovalResult());
        response.setVariables(taskInstance.getVariables());
        return response;
    }

    // ==================== 事件发布 ====================

    private void publishProcessStartedEvent(ProcessInstanceInfo processInstance, TaskInfo firstTask) {
        // 发布流程启动事件
    }

    private void publishTaskCompletedEvent(TaskInfo taskInstance, ProcessInstanceInfo processInstance) {
        // 发布任务完成事件
    }

    private void publishProcessCompletedEvent(ProcessInstanceInfo processInstance) {
        // 发布流程完成事件
    }

    private void publishProcessWithdrawnEvent(ProcessInstanceInfo processInstance) {
        // 发布流程撤回事件
    }

    /**
     * 将Map转换为JSON字符串
     */
    private String convertMapToJson(Map<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return null;
        }
        try {
            // 简单的JSON转换，实际项目中应该使用Jackson或其他JSON库
            StringBuilder json = new StringBuilder("{");
            boolean first = true;
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                if (!first) {
                    json.append(",");
                }
                json.append("\"").append(entry.getKey()).append("\":");
                if (entry.getValue() instanceof String) {
                    json.append("\"").append(entry.getValue()).append("\"");
                } else {
                    json.append(entry.getValue());
                }
                first = false;
            }
            json.append("}");
            return json.toString();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将JSON字符串转换为Map
     */
    private Map<String, Object> convertJsonToMap(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        try {
            // 简单的JSON解析，实际项目中应该使用Jackson或其他JSON库
            // 这里只是为了编译通过，实际应该使用专业的JSON库
            return new java.util.HashMap<>();
        } catch (Exception e) {
            return null;
        }
    }
}
