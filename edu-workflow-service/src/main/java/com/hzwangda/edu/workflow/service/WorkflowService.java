package com.hzwangda.edu.workflow.service;

import com.hzwangda.edu.common.dto.PageResult;
import com.hzwangda.edu.workflow.dto.ProcessInstanceResponse;
import com.hzwangda.edu.workflow.dto.ProcessStartRequest;
import com.hzwangda.edu.workflow.dto.TaskCompleteRequest;
import com.hzwangda.edu.workflow.dto.TaskResponse;

import java.util.List;
import java.util.Map;

/**
 * 工作流服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface WorkflowService {

    /**
     * 启动流程实例
     *
     * @param request 流程启动请求
     * @return 流程实例响应
     */
    ProcessInstanceResponse startProcess(ProcessStartRequest request);

    /**
     * 完成任务
     *
     * @param request 任务完成请求
     * @return 任务响应
     */
    TaskResponse completeTask(TaskCompleteRequest request);

    /**
     * 认领任务
     *
     * @param taskId 任务ID
     * @param assigneeId 指派人ID
     * @param assigneeUsername 指派人用户名
     * @return 任务响应
     */
    TaskResponse claimTask(String taskId, Long assigneeId, String assigneeUsername);

    /**
     * 委派任务
     *
     * @param taskId 任务ID
     * @param targetUser 目标用户
     * @param reason 委派原因
     * @return 任务响应
     */
    TaskResponse delegateTask(String taskId, String targetUser, String reason);

    /**
     * 转办任务
     *
     * @param taskId 任务ID
     * @param targetUser 目标用户
     * @param reason 转办原因
     * @return 任务响应
     */
    TaskResponse transferTask(String taskId, String targetUser, String reason);

    /**
     * 终止流程实例
     *
     * @param processInstanceId 流程实例ID
     * @param reason 终止原因
     * @return 流程实例响应
     */
    ProcessInstanceResponse terminateProcess(String processInstanceId, String reason);

    /**
     * 挂起流程实例
     *
     * @param processInstanceId 流程实例ID
     * @return 流程实例响应
     */
    ProcessInstanceResponse suspendProcess(String processInstanceId);

    /**
     * 激活流程实例
     *
     * @param processInstanceId 流程实例ID
     * @return 流程实例响应
     */
    ProcessInstanceResponse activateProcess(String processInstanceId);

    /**
     * 根据ID查询流程实例
     *
     * @param processInstanceId 流程实例ID
     * @return 流程实例响应
     */
    ProcessInstanceResponse getProcessInstance(String processInstanceId);

    /**
     * 根据业务键查询流程实例
     *
     * @param businessKey 业务键
     * @return 流程实例响应
     */
    ProcessInstanceResponse getProcessInstanceByBusinessKey(String businessKey);

    /**
     * 根据ID查询任务
     *
     * @param taskId 任务ID
     * @return 任务响应
     */
    TaskResponse getTask(String taskId);

    /**
     * 查询用户的待办任务
     *
     * @param username 用户名
     * @param page 页码
     * @param size 每页大小
     * @return 分页任务列表
     */
    PageResult<TaskResponse> getPendingTasks(String username, Integer page, Integer size);

    /**
     * 查询用户的已办任务
     *
     * @param username 用户名
     * @param page 页码
     * @param size 每页大小
     * @return 分页任务列表
     */
    PageResult<TaskResponse> getCompletedTasks(String username, Integer page, Integer size);

    /**
     * 查询流程实例的任务历史
     *
     * @param processInstanceId 流程实例ID
     * @return 任务列表
     */
    List<TaskResponse> getProcessTaskHistory(String processInstanceId);

    /**
     * 查询流程实例的活动历史
     *
     * @param processInstanceId 流程实例ID
     * @return 活动历史列表
     */
    List<Map<String, Object>> getProcessActivityHistory(String processInstanceId);

    /**
     * 获取流程图
     *
     * @param processInstanceId 流程实例ID
     * @return 流程图数据
     */
    byte[] getProcessDiagram(String processInstanceId);

    /**
     * 获取流程定义的流程图
     *
     * @param processDefinitionId 流程定义ID
     * @return 流程图数据
     */
    byte[] getProcessDefinitionDiagram(String processDefinitionId);

    /**
     * 查询用户发起的流程实例
     *
     * @param initiatorId 发起人ID
     * @param page 页码
     * @param size 每页大小
     * @return 分页流程实例列表
     */
    PageResult<ProcessInstanceResponse> getProcessInstancesByInitiator(Long initiatorId, Integer page, Integer size);

    /**
     * 查询指定流程键的流程实例
     *
     * @param processKey 流程键
     * @param page 页码
     * @param size 每页大小
     * @return 分页流程实例列表
     */
    PageResult<ProcessInstanceResponse> getProcessInstancesByKey(String processKey, Integer page, Integer size);

    /**
     * 设置流程变量
     *
     * @param processInstanceId 流程实例ID
     * @param variables 变量Map
     */
    void setProcessVariables(String processInstanceId, Map<String, Object> variables);

    /**
     * 获取流程变量
     *
     * @param processInstanceId 流程实例ID
     * @return 变量Map
     */
    Map<String, Object> getProcessVariables(String processInstanceId);

    /**
     * 设置任务变量
     *
     * @param taskId 任务ID
     * @param variables 变量Map
     */
    void setTaskVariables(String taskId, Map<String, Object> variables);

    /**
     * 获取任务变量
     *
     * @param taskId 任务ID
     * @return 变量Map
     */
    Map<String, Object> getTaskVariables(String taskId);

    // ==================== 复杂审批流程增强功能 ====================

    /**
     * 退回任务到指定节点
     *
     * @param taskId 任务ID
     * @param targetNodeId 目标节点ID
     * @param reason 退回原因
     * @return 任务响应
     */
    TaskResponse returnTask(String taskId, String targetNodeId, String reason);

    /**
     * 跳转任务到指定节点
     *
     * @param taskId 任务ID
     * @param targetNodeId 目标节点ID
     * @param reason 跳转原因
     * @return 任务响应
     */
    TaskResponse jumpTask(String taskId, String targetNodeId, String reason);

    /**
     * 批量分配任务
     *
     * @param taskId 任务ID
     * @param assignees 分配的用户列表
     * @param reason 分配原因
     * @return 任务响应列表
     */
    List<TaskResponse> batchAssignTask(String taskId, List<String> assignees, String reason);

    /**
     * 加签任务
     *
     * @param taskId 任务ID
     * @param signUser 加签用户
     * @param signType 加签类型（before/after/parallel）
     * @param reason 加签原因
     * @return 任务响应
     */
    TaskResponse addSignTask(String taskId, String signUser, String signType, String reason);

    /**
     * 减签任务
     *
     * @param taskId 任务ID
     * @param signUser 减签用户
     * @param reason 减签原因
     * @return 任务响应
     */
    TaskResponse reduceSignTask(String taskId, String signUser, String reason);

    /**
     * 获取任务可跳转的节点列表
     *
     * @param taskId 任务ID
     * @return 可跳转节点列表
     */
    List<Map<String, Object>> getAvailableNodes(String taskId);

    /**
     * 流程回滚到指定节点
     *
     * @param processInstanceId 流程实例ID
     * @param targetNodeId 目标节点ID
     * @param reason 回滚原因
     * @return 流程实例响应
     */
    ProcessInstanceResponse rollbackProcess(String processInstanceId, String targetNodeId, String reason);
}
