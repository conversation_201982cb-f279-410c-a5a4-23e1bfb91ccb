package com.hzwangda.edu.workflow.service;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 流程监控服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public interface ProcessMonitorService {

    /**
     * 获取流程实例统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getProcessInstanceStatistics();

    /**
     * 获取任务统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getTaskStatistics();

    /**
     * 获取流程定义统计信息
     *
     * @param processDefinitionId 流程定义ID
     * @return 统计信息
     */
    Map<String, Object> getProcessDefinitionStatistics(String processDefinitionId);

    /**
     * 获取用户任务统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> getUserTaskStatistics(String userId);

    /**
     * 获取流程执行时长统计
     *
     * @param processDefinitionKey 流程定义Key
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 执行时长统计
     */
    Map<String, Object> getProcessDurationStatistics(String processDefinitionKey,
                                                    LocalDateTime startTime,
                                                    LocalDateTime endTime);

    /**
     * 获取流程完成率统计
     *
     * @param processDefinitionKey 流程定义Key
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 完成率统计
     */
    Map<String, Object> getProcessCompletionRateStatistics(String processDefinitionKey,
                                                          LocalDateTime startTime,
                                                          LocalDateTime endTime);

    /**
     * 获取任务处理效率统计
     *
     * @param assignee 处理人
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 处理效率统计
     */
    Map<String, Object> getTaskEfficiencyStatistics(String assignee,
                                                   LocalDateTime startTime,
                                                   LocalDateTime endTime);

    /**
     * 获取流程瓶颈分析
     *
     * @param processDefinitionKey 流程定义Key
     * @return 瓶颈分析结果
     */
    Map<String, Object> getProcessBottleneckAnalysis(String processDefinitionKey);

    /**
     * 获取实时流程监控数据
     *
     * @return 实时监控数据
     */
    Map<String, Object> getRealTimeMonitoringData();

    /**
     * 获取流程健康度评估
     *
     * @return 健康度评估结果
     */
    Map<String, Object> getProcessHealthAssessment();

    /**
     * 获取异常流程实例
     *
     * @return 异常流程实例列表
     */
    Map<String, Object> getAbnormalProcessInstances();

    /**
     * 获取超时任务统计
     *
     * @return 超时任务统计
     */
    Map<String, Object> getOverdueTaskStatistics();
}
