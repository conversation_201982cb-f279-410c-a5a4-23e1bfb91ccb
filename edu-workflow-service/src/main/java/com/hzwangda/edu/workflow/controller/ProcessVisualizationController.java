package com.hzwangda.edu.workflow.controller;

import com.hzwangda.edu.common.result.Result;
import com.hzwangda.edu.workflow.service.ProcessVisualizationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 流程可视化控制器
 * 提供流程图渲染、在线编辑、状态展示等可视化功能
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/v1/process-visualization")
@RequiredArgsConstructor
@Slf4j
@Validated
@Tag(name = "流程可视化", description = "流程图渲染、在线编辑、状态展示等可视化功能")
public class ProcessVisualizationController {

    private final ProcessVisualizationService processVisualizationService;

    // ==================== 流程图渲染功能 ====================

    @Operation(summary = "获取流程图HTML", description = "获取可在浏览器中显示的流程图HTML页面")
    @GetMapping("/{processDefinitionId}/diagram-html")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_READ', 'WORKFLOW_MANAGE', 'ADMIN')")
    public ResponseEntity<String> getProcessDiagramHtml(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String processDefinitionId,
            @Parameter(description = "是否显示网格")
            @RequestParam(value = "showGrid", defaultValue = "true") boolean showGrid,
            @Parameter(description = "是否只读模式")
            @RequestParam(value = "readonly", defaultValue = "true") boolean readonly) {

        log.info("获取流程图HTML: processDefinitionId={}, showGrid={}, readonly={}",
                processDefinitionId, showGrid, readonly);

        String html = processVisualizationService.getProcessDiagramHtml(processDefinitionId, showGrid, readonly);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_HTML);

        return ResponseEntity.ok()
                .headers(headers)
                .body(html);
    }

    @Operation(summary = "获取流程图JSON", description = "获取BPMN.js可用的流程图JSON数据")
    @GetMapping("/{processDefinitionId}/diagram-json")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_READ', 'WORKFLOW_MANAGE', 'ADMIN')")
    public Result<Map<String, Object>> getProcessDiagramJson(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String processDefinitionId) {

        log.info("获取流程图JSON: {}", processDefinitionId);

        Map<String, Object> diagramData = processVisualizationService.getProcessDiagramJson(processDefinitionId);
        return Result.success(diagramData);
    }

    @Operation(summary = "获取流程实例状态图", description = "获取带有实例状态的流程图")
    @GetMapping("/{processInstanceId}/instance-diagram")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_READ', 'WORKFLOW_MANAGE', 'ADMIN')")
    public Result<Map<String, Object>> getProcessInstanceDiagram(
            @Parameter(description = "流程实例ID", required = true)
            @PathVariable @NotBlank(message = "流程实例ID不能为空") String processInstanceId) {

        log.info("获取流程实例状态图: {}", processInstanceId);

        Map<String, Object> instanceDiagram = processVisualizationService.getProcessInstanceDiagram(processInstanceId);
        return Result.success(instanceDiagram);
    }

    // ==================== 在线编辑功能 ====================

    @Operation(summary = "获取流程编辑器", description = "获取BPMN在线编辑器页面")
    @GetMapping("/editor")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_DEPLOY', 'WORKFLOW_MANAGE', 'ADMIN')")
    public ResponseEntity<String> getProcessEditor(
            @Parameter(description = "流程定义ID，为空时创建新流程")
            @RequestParam(value = "processDefinitionId", required = false) String processDefinitionId,
            @Parameter(description = "模板类型")
            @RequestParam(value = "template", defaultValue = "basic") String template) {

        log.info("获取流程编辑器: processDefinitionId={}, template={}", processDefinitionId, template);

        String editorHtml = processVisualizationService.getProcessEditor(processDefinitionId, template);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_HTML);

        return ResponseEntity.ok()
                .headers(headers)
                .body(editorHtml);
    }

    @Operation(summary = "保存编辑的流程", description = "保存在线编辑器中编辑的流程定义")
    @PostMapping("/editor/save")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_DEPLOY', 'WORKFLOW_MANAGE', 'ADMIN')")
    public Result<Map<String, Object>> saveEditedProcess(
            @Parameter(description = "BPMN XML内容", required = true)
            @RequestParam @NotBlank(message = "BPMN内容不能为空") String bpmnXml,
            @Parameter(description = "流程名称", required = true)
            @RequestParam @NotBlank(message = "流程名称不能为空") String processName,
            @Parameter(description = "流程分类")
            @RequestParam(value = "category", required = false) String category,
            @Parameter(description = "描述")
            @RequestParam(value = "description", required = false) String description) {

        log.info("保存编辑的流程: processName={}, category={}", processName, category);

        Map<String, Object> result = processVisualizationService.saveEditedProcess(
                bpmnXml, processName, category, description);
        return Result.success(result);
    }

    @Operation(summary = "验证流程定义", description = "验证在线编辑器中的流程定义")
    @PostMapping("/editor/validate")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_DEPLOY', 'WORKFLOW_MANAGE', 'ADMIN')")
    public Result<Map<String, Object>> validateProcess(
            @Parameter(description = "BPMN XML内容", required = true)
            @RequestParam @NotBlank(message = "BPMN内容不能为空") String bpmnXml) {

        log.info("验证流程定义");

        Map<String, Object> validationResult = processVisualizationService.validateProcess(bpmnXml);
        return Result.success(validationResult);
    }

    // ==================== 流程图导出功能 ====================

    @Operation(summary = "导出流程图为PNG", description = "将流程图导出为PNG图片")
    @GetMapping("/{processDefinitionId}/export/png")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_READ', 'WORKFLOW_EXPORT', 'ADMIN')")
    public ResponseEntity<byte[]> exportProcessDiagramAsPng(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String processDefinitionId,
            @Parameter(description = "图片宽度")
            @RequestParam(value = "width", defaultValue = "1200") int width,
            @Parameter(description = "图片高度")
            @RequestParam(value = "height", defaultValue = "800") int height) {

        log.info("导出流程图PNG: processDefinitionId={}, width={}, height={}",
                processDefinitionId, width, height);

        byte[] pngData = processVisualizationService.exportProcessDiagramAsPng(processDefinitionId, width, height);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.IMAGE_PNG);
        headers.setContentDispositionFormData("attachment", "process-diagram-" + processDefinitionId + ".png");

        return ResponseEntity.ok()
                .headers(headers)
                .body(pngData);
    }

    @Operation(summary = "导出流程图为SVG", description = "将流程图导出为SVG矢量图")
    @GetMapping("/{processDefinitionId}/export/svg")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_READ', 'WORKFLOW_EXPORT', 'ADMIN')")
    public ResponseEntity<String> exportProcessDiagramAsSvg(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String processDefinitionId) {

        log.info("导出流程图SVG: {}", processDefinitionId);

        String svgData = processVisualizationService.exportProcessDiagramAsSvg(processDefinitionId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.valueOf("image/svg+xml"));
        headers.setContentDispositionFormData("attachment", "process-diagram-" + processDefinitionId + ".svg");

        return ResponseEntity.ok()
                .headers(headers)
                .body(svgData);
    }

    // ==================== 流程统计可视化 ====================

    @Operation(summary = "获取流程统计图表数据", description = "获取流程执行统计的图表数据")
    @GetMapping("/{processDefinitionId}/statistics-chart")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_READ', 'WORKFLOW_MANAGE', 'ADMIN')")
    public Result<Map<String, Object>> getProcessStatisticsChart(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String processDefinitionId,
            @Parameter(description = "统计时间范围（天）")
            @RequestParam(value = "days", defaultValue = "30") int days) {

        log.info("获取流程统计图表数据: processDefinitionId={}, days={}", processDefinitionId, days);

        Map<String, Object> chartData = processVisualizationService.getProcessStatisticsChart(processDefinitionId, days);
        return Result.success(chartData);
    }

    @Operation(summary = "获取流程热力图数据", description = "获取流程节点执行频率的热力图数据")
    @GetMapping("/{processDefinitionId}/heatmap")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_READ', 'WORKFLOW_MANAGE', 'ADMIN')")
    public Result<Map<String, Object>> getProcessHeatmap(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String processDefinitionId,
            @Parameter(description = "统计时间范围（天）")
            @RequestParam(value = "days", defaultValue = "30") int days) {

        log.info("获取流程热力图数据: processDefinitionId={}, days={}", processDefinitionId, days);

        Map<String, Object> heatmapData = processVisualizationService.getProcessHeatmap(processDefinitionId, days);
        return Result.success(heatmapData);
    }
}
