package com.hzwangda.edu.workflow.task;

import com.hzwangda.edu.workflow.service.TaskTimeoutService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 任务超时监控定时任务
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "hky.workflow.timeout-monitor.enabled", havingValue = "true", matchIfMissing = true)
public class TaskTimeoutMonitorTask {

    @Autowired
    private TaskTimeoutService taskTimeoutService;

    /**
     * 每5分钟检查一次超时任务
     */
    @Scheduled(fixedRate = 300000) // 5分钟 = 300000毫秒
    public void checkTimeoutTasks() {
        if (!taskTimeoutService.isTimeoutMonitoringEnabled()) {
            return;
        }

        try {
            log.debug("开始执行任务超时检查定时任务");

            Map<String, Object> result = taskTimeoutService.checkAndHandleTimeoutTasks();

            Integer totalTimeoutTasks = (Integer) result.get("totalTimeoutTasks");
            Integer processedCount = (Integer) result.get("processedCount");

            if (totalTimeoutTasks != null && totalTimeoutTasks > 0) {
                log.info("任务超时检查完成: 发现超时任务 {} 个，处理 {} 个", totalTimeoutTasks, processedCount);
            }

        } catch (Exception e) {
            log.error("任务超时检查定时任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 每小时执行一次超时任务统计
     */
    @Scheduled(cron = "0 0 * * * ?") // 每小时的0分0秒执行
    public void generateTimeoutStatistics() {
        if (!taskTimeoutService.isTimeoutMonitoringEnabled()) {
            return;
        }

        try {
            log.debug("开始生成超时任务统计");

            var timeoutTasks = taskTimeoutService.getTimeoutTasks();

            if (!timeoutTasks.isEmpty()) {
                log.info("当前系统中有 {} 个超时任务需要处理", timeoutTasks.size());

                // 可以在这里添加更详细的统计逻辑
                // 比如按流程分组、按处理人分组等
            }

        } catch (Exception e) {
            log.error("生成超时任务统计失败: {}", e.getMessage(), e);
        }
    }
}
