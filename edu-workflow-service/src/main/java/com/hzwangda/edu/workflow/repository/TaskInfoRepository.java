package com.hzwangda.edu.workflow.repository;

import com.hzwangda.edu.workflow.entity.TaskInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 任务信息Repository接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface TaskInfoRepository extends JpaRepository<TaskInfo, Long>, JpaSpecificationExecutor<TaskInfo> {

    /**
     * 根据Flowable任务ID查询
     */
    Optional<TaskInfo> findByFlowableTaskIdAndDeletedFalse(String flowableTaskId);

    /**
     * 根据流程实例ID查询任务
     */
    List<TaskInfo> findByProcessInstanceIdAndDeletedFalseOrderByTaskCreateTimeAsc(String processInstanceId);

    /**
     * 根据流程实例ID查询任务（按创建时间升序）
     */
    List<TaskInfo> findByProcessInstanceIdOrderByCreateTimeAsc(String processInstanceId);

    /**
     * 根据指派人ID和状态查询任务（按创建时间降序）
     */
    List<TaskInfo> findByAssigneeIdAndStatusOrderByCreateTimeDesc(Long assigneeId, String status);

    /**
     * 根据流程实例ID和状态查询任务
     */
    List<TaskInfo> findByProcessInstanceIdAndStatus(String processInstanceId, String status);

    /**
     * 根据状态统计任务数量
     */
    Long countByStatus(String status);

    /**
     * 根据指派人查询待处理任务
     */
    Page<TaskInfo> findByAssigneeIdAndStatusAndDeletedFalse(Long assigneeId, String status, Pageable pageable);

    /**
     * 根据指派人用户名查询待处理任务
     */
    Page<TaskInfo> findByAssigneeUsernameAndStatusAndDeletedFalse(String assigneeUsername, String status, Pageable pageable);

    /**
     * 根据候选组查询任务
     */
    @Query("SELECT t FROM TaskInfo t WHERE t.candidateGroups LIKE %:candidateGroup% AND t.status = 'PENDING' AND t.deleted = false")
    Page<TaskInfo> findByCandidateGroup(@Param("candidateGroup") String candidateGroup, Pageable pageable);

    /**
     * 根据候选用户查询任务
     */
    @Query("SELECT t FROM TaskInfo t WHERE t.candidateUsers LIKE %:candidateUser% AND t.status = 'PENDING' AND t.deleted = false")
    Page<TaskInfo> findByCandidateUser(@Param("candidateUser") String candidateUser, Pageable pageable);

    /**
     * 查询用户的所有待处理任务（包括指派和候选）
     */
    @Query("SELECT t FROM TaskInfo t WHERE (t.assigneeUsername = :username OR t.candidateUsers LIKE %:username%) " +
           "AND t.status = 'PENDING' AND t.deleted = false ORDER BY t.priority DESC, t.taskCreateTime ASC")
    List<TaskInfo> findPendingTasksByUser(@Param("username") String username);

    /**
     * 查询用户的历史任务
     */
    @Query("SELECT t FROM TaskInfo t WHERE t.assigneeUsername = :username AND t.status IN ('APPROVED', 'REJECTED') " +
           "AND t.deleted = false ORDER BY t.completeTime DESC")
    Page<TaskInfo> findHistoryTasksByUser(@Param("username") String username, Pageable pageable);

    /**
     * 根据流程键查询任务
     */
    Page<TaskInfo> findByProcessKeyAndDeletedFalse(String processKey, Pageable pageable);

    /**
     * 根据任务键查询任务
     */
    Page<TaskInfo> findByTaskKeyAndDeletedFalse(String taskKey, Pageable pageable);

    /**
     * 根据状态查询任务
     */
    Page<TaskInfo> findByStatusAndDeletedFalse(String status, Pageable pageable);

    /**
     * 查询超时任务
     */
    @Query("SELECT t FROM TaskInfo t WHERE t.dueDate < :currentTime AND t.status = 'PENDING' AND t.deleted = false")
    List<TaskInfo> findOverdueTasks(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查询即将超时的任务
     */
    @Query("SELECT t FROM TaskInfo t WHERE t.dueDate BETWEEN :currentTime AND :warningTime AND t.status = 'PENDING' AND t.deleted = false")
    List<TaskInfo> findTasksNearDue(@Param("currentTime") LocalDateTime currentTime, @Param("warningTime") LocalDateTime warningTime);

    /**
     * 统计各状态下的任务数量
     */
    @Query("SELECT t.status, COUNT(t) FROM TaskInfo t WHERE t.deleted = false GROUP BY t.status")
    List<Object[]> countByStatus();

    /**
     * 统计各流程键下的任务数量
     */
    @Query("SELECT t.processKey, COUNT(t) FROM TaskInfo t WHERE t.deleted = false GROUP BY t.processKey")
    List<Object[]> countByProcessKey();

    /**
     * 统计各任务键下的任务数量
     */
    @Query("SELECT t.taskKey, COUNT(t) FROM TaskInfo t WHERE t.deleted = false GROUP BY t.taskKey")
    List<Object[]> countByTaskKey();

    /**
     * 统计指定时间范围内的任务数量
     */
    @Query("SELECT COUNT(t) FROM TaskInfo t WHERE t.taskCreateTime BETWEEN :startTime AND :endTime AND t.deleted = false")
    Long countByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内已完成的任务数量
     */
    @Query("SELECT COUNT(t) FROM TaskInfo t WHERE t.completeTime BETWEEN :startTime AND :endTime " +
           "AND t.status IN ('APPROVED', 'REJECTED') AND t.deleted = false")
    Long countCompletedByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询平均任务处理时长
     */
    @Query("SELECT AVG(t.duration) FROM TaskInfo t WHERE t.status IN ('APPROVED', 'REJECTED') AND t.duration IS NOT NULL AND t.deleted = false")
    Double findAverageTaskDuration();

    /**
     * 查询指定任务键的平均处理时长
     */
    @Query("SELECT AVG(t.duration) FROM TaskInfo t WHERE t.taskKey = :taskKey AND t.status IN ('APPROVED', 'REJECTED') " +
           "AND t.duration IS NOT NULL AND t.deleted = false")
    Double findAverageTaskDurationByKey(@Param("taskKey") String taskKey);

    /**
     * 查询最活跃的处理人TOP10
     */
    @Query("SELECT t.assigneeId, t.assigneeUsername, t.assigneeName, COUNT(t) as taskCount " +
           "FROM TaskInfo t WHERE t.completeTime BETWEEN :startTime AND :endTime AND t.status IN ('APPROVED', 'REJECTED') " +
           "AND t.deleted = false GROUP BY t.assigneeId, t.assigneeUsername, t.assigneeName ORDER BY taskCount DESC")
    List<Object[]> findTopAssignees(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, Pageable pageable);

    /**
     * 按日期统计任务数量
     */
    @Query("SELECT DATE(t.taskCreateTime), COUNT(t) FROM TaskInfo t WHERE t.taskCreateTime BETWEEN :startTime AND :endTime " +
           "AND t.deleted = false GROUP BY DATE(t.taskCreateTime) ORDER BY DATE(t.taskCreateTime)")
    List<Object[]> countByDate(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 按小时统计任务数量
     */
    @Query("SELECT HOUR(t.taskCreateTime), COUNT(t) FROM TaskInfo t WHERE t.taskCreateTime BETWEEN :startTime AND :endTime " +
           "AND t.deleted = false GROUP BY HOUR(t.taskCreateTime) ORDER BY HOUR(t.taskCreateTime)")
    List<Object[]> countByHour(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定业务类型的任务
     */
    @Query("SELECT t FROM TaskInfo t WHERE t.businessType = :businessType AND t.deleted = false ORDER BY t.taskCreateTime DESC")
    List<TaskInfo> findByBusinessType(@Param("businessType") String businessType);

    /**
     * 查询用户的待办任务数量
     */
    @Query("SELECT COUNT(t) FROM TaskInfo t WHERE (t.assigneeUsername = :username OR t.candidateUsers LIKE %:username%) " +
           "AND t.status = 'PENDING' AND t.deleted = false")
    Long countPendingTasksByUser(@Param("username") String username);
}
