package com.hzwangda.edu.workflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务完成响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "任务完成响应")
public class TaskCompleteResponse {

    @Schema(description = "任务ID", example = "task_001")
    private String taskId;

    @Schema(description = "流程实例ID", example = "process_001")
    private String processInstanceId;

    @Schema(description = "是否完成", example = "true")
    private Boolean completed;

    @Schema(description = "完成时间")
    private LocalDateTime completeTime;

    @Schema(description = "下一个任务ID", example = "task_002")
    private String nextTaskId;

    @Schema(description = "下一个任务名称", example = "部门经理审批")
    private String nextTaskName;

    @Schema(description = "下一个处理人ID", example = "user_002")
    private String nextAssigneeId;

    @Schema(description = "下一个处理人姓名", example = "张经理")
    private String nextAssigneeName;

    @Schema(description = "流程是否完成", example = "false")
    private Boolean processCompleted;

    @Schema(description = "流程结果", example = "APPROVED")
    private String processResult;

    @Schema(description = "流程结束时间")
    private LocalDateTime processEndTime;

    @Schema(description = "消息", example = "任务完成成功")
    private String message;

    @Schema(description = "扩展数据")
    private Object extData;

    /**
     * 创建成功响应
     */
    public static TaskCompleteResponse success(String taskId, String processInstanceId) {
        TaskCompleteResponse response = new TaskCompleteResponse();
        response.setTaskId(taskId);
        response.setProcessInstanceId(processInstanceId);
        response.setCompleted(true);
        response.setCompleteTime(LocalDateTime.now());
        response.setProcessCompleted(false);
        response.setMessage("任务完成成功");
        return response;
    }

    /**
     * 创建流程完成响应
     */
    public static TaskCompleteResponse processCompleted(String taskId, String processInstanceId, String result) {
        TaskCompleteResponse response = success(taskId, processInstanceId);
        response.setProcessCompleted(true);
        response.setProcessResult(result);
        response.setProcessEndTime(LocalDateTime.now());
        response.setMessage("流程已完成");
        return response;
    }
}
