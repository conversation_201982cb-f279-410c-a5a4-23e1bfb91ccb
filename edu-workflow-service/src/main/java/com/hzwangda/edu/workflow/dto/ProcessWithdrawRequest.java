package com.hzwangda.edu.workflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 流程撤回请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "流程撤回请求")
public class ProcessWithdrawRequest {

    @NotBlank(message = "发起人ID不能为空")
    @Schema(description = "发起人ID", example = "user_001", required = true)
    private String initiatorId;

    @Schema(description = "发起人姓名", example = "张三")
    private String initiatorName;

    @Size(max = 500, message = "撤回原因长度不能超过500个字符")
    @Schema(description = "撤回原因", example = "申请信息有误，需要重新提交")
    private String reason;

    @Schema(description = "是否强制撤回", example = "false")
    private Boolean forceWithdraw = false;

    @Schema(description = "撤回到指定节点ID")
    private String targetActivityId;

    @Schema(description = "撤回到指定节点名称")
    private String targetActivityName;

    @Schema(description = "通知相关人员", example = "true")
    private Boolean notifyRelated = true;

    @Schema(description = "扩展数据")
    private Object extData;

    /**
     * 创建简单撤回请求
     */
    public static ProcessWithdrawRequest simple(String initiatorId, String reason) {
        ProcessWithdrawRequest request = new ProcessWithdrawRequest();
        request.setInitiatorId(initiatorId);
        request.setReason(reason);
        return request;
    }

    /**
     * 创建强制撤回请求
     */
    public static ProcessWithdrawRequest force(String initiatorId, String reason) {
        ProcessWithdrawRequest request = simple(initiatorId, reason);
        request.setForceWithdraw(true);
        return request;
    }

    /**
     * 创建撤回到指定节点的请求
     */
    public static ProcessWithdrawRequest toActivity(String initiatorId, String reason,
                                                   String targetActivityId, String targetActivityName) {
        ProcessWithdrawRequest request = simple(initiatorId, reason);
        request.setTargetActivityId(targetActivityId);
        request.setTargetActivityName(targetActivityName);
        return request;
    }
}
