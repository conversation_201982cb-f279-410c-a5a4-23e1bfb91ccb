package com.hzwangda.edu.workflow.service.impl;

import com.hzwangda.edu.common.exception.BusinessException;
import com.hzwangda.edu.common.result.ResultCode;
import com.hzwangda.edu.workflow.service.ProcessDiagramService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.engine.HistoryService;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.image.ProcessDiagramGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 流程图服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class ProcessDiagramServiceImpl implements ProcessDiagramService {

    @Autowired
    private ProcessEngine processEngine;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private HistoryService historyService;

    @Override
    @Cacheable(value = "process_diagram", key = "#processDefinitionId")
    public byte[] generateProcessDefinitionDiagram(String processDefinitionId) {
        log.debug("生成流程定义图: {}", processDefinitionId);

        try {
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
            ProcessDiagramGenerator diagramGenerator = processEngine.getProcessEngineConfiguration()
                    .getProcessDiagramGenerator();

            InputStream inputStream = diagramGenerator.generateDiagram(
                    bpmnModel, "png", Collections.emptyList(), Collections.emptyList(),
                    "宋体", "宋体", "宋体", null, 1.0, true);

            return inputStream.readAllBytes();
        } catch (Exception e) {
            log.error("生成流程定义图失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "生成流程定义图失败: " + e.getMessage());
        }
    }

    @Override
    @Cacheable(value = "process_instance_diagram", key = "#processInstanceId")
    public byte[] generateProcessInstanceDiagram(String processInstanceId) {
        log.debug("生成流程实例图: {}", processInstanceId);

        try {
            // 获取流程实例
            var processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();

            if (processInstance == null) {
                // 查询历史流程实例
                var historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                        .processInstanceId(processInstanceId)
                        .singleResult();

                if (historicProcessInstance == null) {
                    throw new BusinessException(ResultCode.NOT_FOUND, "流程实例不存在: " + processInstanceId);
                }

                // 获取已完成的活动节点
                List<String> highlightedActivities = getCompletedActivities(processInstanceId);
                List<String> highlightedFlows = getCompletedFlows(processInstanceId);

                return generateProcessInstanceDiagram(processInstanceId, highlightedActivities, highlightedFlows);
            }

            // 获取当前活动节点
            List<String> activeActivityIds = runtimeService.getActiveActivityIds(processInstanceId);

            // 获取已完成的活动节点
            List<String> completedActivities = getCompletedActivities(processInstanceId);

            // 合并当前活动和已完成活动
            List<String> highlightedActivities = new ArrayList<>(completedActivities);
            highlightedActivities.addAll(activeActivityIds);

            // 获取已完成的流程线
            List<String> highlightedFlows = getCompletedFlows(processInstanceId);

            return generateProcessInstanceDiagram(processInstanceId, highlightedActivities, highlightedFlows);

        } catch (Exception e) {
            log.error("生成流程实例图失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "生成流程实例图失败: " + e.getMessage());
        }
    }

    @Override
    public byte[] generateProcessInstanceDiagram(String processInstanceId,
                                               List<String> highlightedActivities,
                                               List<String> highlightedFlows) {
        log.debug("生成流程实例图: processInstanceId={}, highlightedActivities={}, highlightedFlows={}",
                processInstanceId, highlightedActivities, highlightedFlows);

        try {
            // 获取流程定义ID
            String processDefinitionId = getProcessDefinitionId(processInstanceId);

            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
            ProcessDiagramGenerator diagramGenerator = processEngine.getProcessEngineConfiguration()
                    .getProcessDiagramGenerator();

            InputStream inputStream = diagramGenerator.generateDiagram(
                    bpmnModel, "png",
                    highlightedActivities != null ? highlightedActivities : Collections.emptyList(),
                    highlightedFlows != null ? highlightedFlows : Collections.emptyList(),
                    "宋体", "宋体", "宋体", null, 1.0, true);

            return inputStream.readAllBytes();
        } catch (Exception e) {
            log.error("生成流程实例图失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "生成流程实例图失败: " + e.getMessage());
        }
    }

    @Override
    @Cacheable(value = "process_diagram_svg", key = "#processDefinitionId")
    public String generateProcessDefinitionSvg(String processDefinitionId) {
        log.debug("生成流程定义SVG图: {}", processDefinitionId);

        try {
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
            ProcessDiagramGenerator diagramGenerator = processEngine.getProcessEngineConfiguration()
                    .getProcessDiagramGenerator();

            InputStream inputStream = diagramGenerator.generateDiagram(
                    bpmnModel, "svg", Collections.emptyList(), Collections.emptyList(),
                    "宋体", "宋体", "宋体", null, 1.0, true);

            return new String(inputStream.readAllBytes());
        } catch (Exception e) {
            log.error("生成流程定义SVG图失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "生成流程定义SVG图失败: " + e.getMessage());
        }
    }

    @Override
    @Cacheable(value = "process_instance_diagram_svg", key = "#processInstanceId")
    public String generateProcessInstanceSvg(String processInstanceId) {
        log.debug("生成流程实例SVG图: {}", processInstanceId);

        try {
            // 获取当前活动节点
            List<String> activeActivityIds = runtimeService.getActiveActivityIds(processInstanceId);

            // 获取已完成的活动节点
            List<String> completedActivities = getCompletedActivities(processInstanceId);

            // 合并当前活动和已完成活动
            List<String> highlightedActivities = new ArrayList<>(completedActivities);
            highlightedActivities.addAll(activeActivityIds);

            // 获取已完成的流程线
            List<String> highlightedFlows = getCompletedFlows(processInstanceId);

            // 获取流程定义ID
            String processDefinitionId = getProcessDefinitionId(processInstanceId);

            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinitionId);
            ProcessDiagramGenerator diagramGenerator = processEngine.getProcessEngineConfiguration()
                    .getProcessDiagramGenerator();

            InputStream inputStream = diagramGenerator.generateDiagram(
                    bpmnModel, "svg", highlightedActivities, highlightedFlows,
                    "宋体", "宋体", "宋体", null, 1.0, true);

            return new String(inputStream.readAllBytes());
        } catch (Exception e) {
            log.error("生成流程实例SVG图失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "生成流程实例SVG图失败: " + e.getMessage());
        }
    }

    @Override
    @CacheEvict(value = {"process_diagram", "process_diagram_svg"}, key = "#processDefinitionId")
    public void clearDiagramCache(String processDefinitionId) {
        log.info("清理流程图缓存: {}", processDefinitionId);
    }

    @Override
    @CacheEvict(value = {"process_diagram", "process_instance_diagram", "process_diagram_svg", "process_instance_diagram_svg"}, allEntries = true)
    public void clearAllDiagramCache() {
        log.info("清理所有流程图缓存");
    }

    @Override
    public Object getDiagramCacheStats() {
        // TODO: 实现缓存统计信息获取
        return "缓存统计功能待实现";
    }

    /**
     * 获取已完成的活动节点
     */
    private List<String> getCompletedActivities(String processInstanceId) {
        List<HistoricActivityInstance> historicActivityInstances = historyService
                .createHistoricActivityInstanceQuery()
                .processInstanceId(processInstanceId)
                .finished()
                .list();

        return historicActivityInstances.stream()
                .map(HistoricActivityInstance::getActivityId)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取已完成的流程线
     */
    private List<String> getCompletedFlows(String processInstanceId) {
        // TODO: 实现获取已完成流程线的逻辑
        // 这需要分析历史活动实例之间的连接关系
        return Collections.emptyList();
    }

    /**
     * 获取流程定义ID
     */
    private String getProcessDefinitionId(String processInstanceId) {
        // 先查询运行中的流程实例
        var processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();

        if (processInstance != null) {
            return processInstance.getProcessDefinitionId();
        }

        // 查询历史流程实例
        var historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();

        if (historicProcessInstance != null) {
            return historicProcessInstance.getProcessDefinitionId();
        }

        throw new BusinessException(ResultCode.NOT_FOUND, "流程实例不存在: " + processInstanceId);
    }
}
