package com.hzwangda.edu.workflow.controller;

import com.hzwangda.edu.common.dto.PageResult;
import com.hzwangda.edu.common.result.Result;
import com.hzwangda.edu.workflow.dto.ProcessInstanceResponse;
import com.hzwangda.edu.workflow.dto.ProcessStartRequest;
import com.hzwangda.edu.workflow.dto.TaskCompleteRequest;
import com.hzwangda.edu.workflow.dto.TaskResponse;
import com.hzwangda.edu.workflow.engine.WorkflowEngine;
import com.hzwangda.edu.workflow.service.WorkflowService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 工作流控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/v1/workflow")
@Tag(name = "工作流管理", description = "工作流引擎相关功能，包括流程启动、任务处理、流程监控等")
public class WorkflowController {

    private static final Logger logger = LoggerFactory.getLogger(WorkflowController.class);

    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private WorkflowEngine workflowEngine;

    @PostMapping("/processes/start")
    @Operation(summary = "启动流程实例", description = "根据流程键启动新的流程实例")
    @PreAuthorize("hasAuthority('workflow:start')")
    public Result<ProcessInstanceResponse> startProcess(@Valid @RequestBody ProcessStartRequest request) {
        logger.info("启动流程: 流程键={}, 发起人={}", request.getProcessKey(), request.getInitiatorUsername());
        ProcessInstanceResponse response = workflowService.startProcess(request);
        return Result.success(response);
    }

    @PostMapping("/tasks/{taskId}/complete")
    @Operation(summary = "完成任务", description = "完成指定的工作流任务")
    @PreAuthorize("hasAuthority('workflow:task:complete')")
    public Result<TaskResponse> completeTask(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Valid @RequestBody TaskCompleteRequest request) {
        logger.info("完成任务: 任务ID={}, 处理人={}", taskId, request.getAssigneeUsername());
        request.setTaskId(taskId);
        TaskResponse response = workflowService.completeTask(request);
        return Result.success(response);
    }

    @PostMapping("/tasks/{taskId}/claim")
    @Operation(summary = "认领任务", description = "认领指定的工作流任务")
    @PreAuthorize("hasAuthority('workflow:task:claim')")
    public Result<TaskResponse> claimTask(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Parameter(description = "指派人ID") @RequestParam Long assigneeId,
            @Parameter(description = "指派人用户名") @RequestParam String assigneeUsername) {
        logger.info("认领任务: 任务ID={}, 认领人={}", taskId, assigneeUsername);
        TaskResponse response = workflowService.claimTask(taskId, assigneeId, assigneeUsername);
        return Result.success(response);
    }

    @PostMapping("/tasks/{taskId}/delegate")
    @Operation(summary = "委派任务", description = "将任务委派给其他用户")
    @PreAuthorize("hasAuthority('workflow:task:delegate')")
    public Result<TaskResponse> delegateTask(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Parameter(description = "目标用户") @RequestParam String targetUser,
            @Parameter(description = "委派原因") @RequestParam(required = false) String reason) {
        logger.info("委派任务: 任务ID={}, 目标用户={}", taskId, targetUser);
        TaskResponse response = workflowService.delegateTask(taskId, targetUser, reason);
        return Result.success(response);
    }

    @PostMapping("/tasks/{taskId}/transfer")
    @Operation(summary = "转办任务", description = "将任务转办给其他用户")
    @PreAuthorize("hasAuthority('workflow:task:transfer')")
    public Result<TaskResponse> transferTask(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Parameter(description = "目标用户") @RequestParam String targetUser,
            @Parameter(description = "转办原因") @RequestParam(required = false) String reason) {
        logger.info("转办任务: 任务ID={}, 目标用户={}", taskId, targetUser);
        TaskResponse response = workflowService.transferTask(taskId, targetUser, reason);
        return Result.success(response);
    }

    @DeleteMapping("/processes/{processInstanceId}")
    @Operation(summary = "终止流程实例", description = "强制终止指定的流程实例")
    @PreAuthorize("hasAuthority('workflow:process:terminate')")
    public Result<ProcessInstanceResponse> terminateProcess(
            @Parameter(description = "流程实例ID") @PathVariable String processInstanceId,
            @Parameter(description = "终止原因") @RequestParam String reason) {
        logger.info("终止流程实例: 流程实例ID={}, 原因={}", processInstanceId, reason);
        ProcessInstanceResponse response = workflowService.terminateProcess(processInstanceId, reason);
        return Result.success(response);
    }

    @PostMapping("/processes/{processInstanceId}/suspend")
    @Operation(summary = "挂起流程实例", description = "挂起指定的流程实例")
    @PreAuthorize("hasAuthority('workflow:process:suspend')")
    public Result<ProcessInstanceResponse> suspendProcess(
            @Parameter(description = "流程实例ID") @PathVariable String processInstanceId) {
        logger.info("挂起流程实例: 流程实例ID={}", processInstanceId);
        ProcessInstanceResponse response = workflowService.suspendProcess(processInstanceId);
        return Result.success(response);
    }

    @PostMapping("/processes/{processInstanceId}/activate")
    @Operation(summary = "激活流程实例", description = "激活被挂起的流程实例")
    @PreAuthorize("hasAuthority('workflow:process:activate')")
    public Result<ProcessInstanceResponse> activateProcess(
            @Parameter(description = "流程实例ID") @PathVariable String processInstanceId) {
        logger.info("激活流程实例: 流程实例ID={}", processInstanceId);
        ProcessInstanceResponse response = workflowService.activateProcess(processInstanceId);
        return Result.success(response);
    }

    @GetMapping("/processes/{processInstanceId}")
    @Operation(summary = "查询流程实例", description = "根据ID查询流程实例详情")
    @PreAuthorize("hasAuthority('workflow:process:view')")
    public Result<ProcessInstanceResponse> getProcessInstance(
            @Parameter(description = "流程实例ID") @PathVariable String processInstanceId) {
        logger.debug("查询流程实例: 流程实例ID={}", processInstanceId);
        ProcessInstanceResponse response = workflowService.getProcessInstance(processInstanceId);
        return Result.success(response);
    }

    @GetMapping("/processes/business/{businessKey}")
    @Operation(summary = "根据业务键查询流程实例", description = "根据业务键查询流程实例详情")
    @PreAuthorize("hasAuthority('workflow:process:view')")
    public Result<ProcessInstanceResponse> getProcessInstanceByBusinessKey(
            @Parameter(description = "业务键") @PathVariable String businessKey) {
        logger.debug("根据业务键查询流程实例: 业务键={}", businessKey);
        ProcessInstanceResponse response = workflowService.getProcessInstanceByBusinessKey(businessKey);
        return Result.success(response);
    }

    @GetMapping("/tasks/{taskId}")
    @Operation(summary = "查询任务详情", description = "根据ID查询任务详情")
    @PreAuthorize("hasAuthority('workflow:task:view')")
    public Result<TaskResponse> getTask(
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        logger.debug("查询任务: 任务ID={}", taskId);
        TaskResponse response = workflowService.getTask(taskId);
        return Result.success(response);
    }

    @GetMapping("/tasks/pending")
    @Operation(summary = "查询待办任务", description = "查询用户的待办任务列表")
    @PreAuthorize("hasAuthority('workflow:task:view')")
    public Result<PageResult<TaskResponse>> getPendingTasks(
            @Parameter(description = "用户名") @RequestParam String username,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        logger.debug("查询待办任务: 用户名={}, 页码={}, 大小={}", username, page, size);
        PageResult<TaskResponse> result = workflowService.getPendingTasks(username, page, size);
        return Result.success(result);
    }

    @GetMapping("/tasks/completed")
    @Operation(summary = "查询已办任务", description = "查询用户的已办任务列表")
    @PreAuthorize("hasAuthority('workflow:task:view')")
    public Result<PageResult<TaskResponse>> getCompletedTasks(
            @Parameter(description = "用户名") @RequestParam String username,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        logger.debug("查询已办任务: 用户名={}, 页码={}, 大小={}", username, page, size);
        PageResult<TaskResponse> result = workflowService.getCompletedTasks(username, page, size);
        return Result.success(result);
    }

    @GetMapping("/processes/{processInstanceId}/tasks/history")
    @Operation(summary = "查询流程任务历史", description = "查询指定流程实例的任务历史")
    @PreAuthorize("hasAuthority('workflow:process:view')")
    public Result<List<TaskResponse>> getProcessTaskHistory(
            @Parameter(description = "流程实例ID") @PathVariable String processInstanceId) {
        logger.debug("查询流程任务历史: 流程实例ID={}", processInstanceId);
        List<TaskResponse> history = workflowService.getProcessTaskHistory(processInstanceId);
        return Result.success(history);
    }

    // ==================== 复杂审批流程增强功能 ====================

    @PostMapping("/tasks/{taskId}/return")
    @Operation(summary = "退回任务", description = "将任务退回到指定节点")
    @PreAuthorize("hasAuthority('workflow:task:return')")
    public Result<TaskResponse> returnTask(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Parameter(description = "退回到的节点ID") @RequestParam String targetNodeId,
            @Parameter(description = "退回原因") @RequestParam String reason) {
        logger.info("退回任务: 任务ID={}, 目标节点={}, 原因={}", taskId, targetNodeId, reason);
        TaskResponse response = workflowService.returnTask(taskId, targetNodeId, reason);
        return Result.success(response);
    }

    @PostMapping("/tasks/{taskId}/jump")
    @Operation(summary = "跳转任务", description = "将任务跳转到指定节点")
    @PreAuthorize("hasAuthority('workflow:task:jump')")
    public Result<TaskResponse> jumpTask(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Parameter(description = "跳转到的节点ID") @RequestParam String targetNodeId,
            @Parameter(description = "跳转原因") @RequestParam String reason) {
        logger.info("跳转任务: 任务ID={}, 目标节点={}, 原因={}", taskId, targetNodeId, reason);
        TaskResponse response = workflowService.jumpTask(taskId, targetNodeId, reason);
        return Result.success(response);
    }

    @PostMapping("/tasks/{taskId}/batch-assign")
    @Operation(summary = "批量分配任务", description = "将任务批量分配给多个用户")
    @PreAuthorize("hasAuthority('workflow:task:assign')")
    public Result<List<TaskResponse>> batchAssignTask(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Parameter(description = "分配的用户列表") @RequestBody List<String> assignees,
            @Parameter(description = "分配原因") @RequestParam(required = false) String reason) {
        logger.info("批量分配任务: 任务ID={}, 分配用户数={}", taskId, assignees.size());
        List<TaskResponse> responses = workflowService.batchAssignTask(taskId, assignees, reason);
        return Result.success(responses);
    }

    @PostMapping("/tasks/{taskId}/add-sign")
    @Operation(summary = "加签任务", description = "为任务添加额外的审批人")
    @PreAuthorize("hasAuthority('workflow:task:add-sign')")
    public Result<TaskResponse> addSignTask(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Parameter(description = "加签用户") @RequestParam String signUser,
            @Parameter(description = "加签类型：before(前加签)、after(后加签)、parallel(并行加签)")
            @RequestParam String signType,
            @Parameter(description = "加签原因") @RequestParam(required = false) String reason) {
        logger.info("加签任务: 任务ID={}, 加签用户={}, 加签类型={}", taskId, signUser, signType);
        TaskResponse response = workflowService.addSignTask(taskId, signUser, signType, reason);
        return Result.success(response);
    }

    @PostMapping("/tasks/{taskId}/reduce-sign")
    @Operation(summary = "减签任务", description = "减少任务的审批人")
    @PreAuthorize("hasAuthority('workflow:task:reduce-sign')")
    public Result<TaskResponse> reduceSignTask(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Parameter(description = "减签用户") @RequestParam String signUser,
            @Parameter(description = "减签原因") @RequestParam(required = false) String reason) {
        logger.info("减签任务: 任务ID={}, 减签用户={}, 原因={}", taskId, signUser, reason);
        TaskResponse response = workflowService.reduceSignTask(taskId, signUser, reason);
        return Result.success(response);
    }

    @GetMapping("/tasks/{taskId}/available-nodes")
    @Operation(summary = "获取可跳转节点", description = "获取任务可以跳转到的节点列表")
    @PreAuthorize("hasAuthority('workflow:task:view')")
    public Result<List<Map<String, Object>>> getAvailableNodes(
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        logger.debug("获取可跳转节点: 任务ID={}", taskId);
        List<Map<String, Object>> nodes = workflowService.getAvailableNodes(taskId);
        return Result.success(nodes);
    }

    @PostMapping("/processes/{processInstanceId}/rollback")
    @Operation(summary = "流程回滚", description = "将流程回滚到指定节点")
    @PreAuthorize("hasAuthority('workflow:process:rollback')")
    public Result<ProcessInstanceResponse> rollbackProcess(
            @Parameter(description = "流程实例ID") @PathVariable String processInstanceId,
            @Parameter(description = "回滚到的节点ID") @RequestParam String targetNodeId,
            @Parameter(description = "回滚原因") @RequestParam String reason) {
        logger.info("流程回滚: 流程实例ID={}, 目标节点={}, 原因={}", processInstanceId, targetNodeId, reason);
        ProcessInstanceResponse response = workflowService.rollbackProcess(processInstanceId, targetNodeId, reason);
        return Result.success(response);
    }

    @GetMapping("/processes/{processInstanceId}/activities/history")
    @Operation(summary = "查询流程活动历史", description = "查询指定流程实例的活动历史")
    @PreAuthorize("hasAuthority('workflow:process:view')")
    public Result<List<Map<String, Object>>> getProcessActivityHistory(
            @Parameter(description = "流程实例ID") @PathVariable String processInstanceId) {
        logger.debug("查询流程活动历史: 流程实例ID={}", processInstanceId);
        List<Map<String, Object>> history = workflowService.getProcessActivityHistory(processInstanceId);
        return Result.success(history);
    }

    @GetMapping("/processes/{processInstanceId}/diagram")
    @Operation(summary = "获取流程图", description = "获取指定流程实例的流程图")
    @PreAuthorize("hasAuthority('workflow:process:view')")
    public ResponseEntity<byte[]> getProcessDiagram(
            @Parameter(description = "流程实例ID") @PathVariable String processInstanceId) {
        logger.debug("获取流程图: 流程实例ID={}", processInstanceId);

        byte[] diagramData = workflowService.getProcessDiagram(processInstanceId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.IMAGE_PNG);
        headers.setContentDispositionFormData("inline", "process_diagram.png");

        return ResponseEntity.ok()
                .headers(headers)
                .body(diagramData);
    }

    @GetMapping("/processes/initiator/{initiatorId}")
    @Operation(summary = "查询用户发起的流程", description = "查询指定用户发起的流程实例列表")
    @PreAuthorize("hasAuthority('workflow:process:view')")
    public Result<PageResult<ProcessInstanceResponse>> getProcessInstancesByInitiator(
            @Parameter(description = "发起人ID") @PathVariable Long initiatorId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        logger.debug("查询用户发起的流程: 发起人ID={}, 页码={}, 大小={}", initiatorId, page, size);
        PageResult<ProcessInstanceResponse> result = workflowService.getProcessInstancesByInitiator(initiatorId, page, size);
        return Result.success(result);
    }

    @GetMapping("/processes/key/{processKey}")
    @Operation(summary = "查询指定流程键的实例", description = "查询指定流程键的流程实例列表")
    @PreAuthorize("hasAuthority('workflow:process:view')")
    public Result<PageResult<ProcessInstanceResponse>> getProcessInstancesByKey(
            @Parameter(description = "流程键") @PathVariable String processKey,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        logger.debug("查询指定流程键的实例: 流程键={}, 页码={}, 大小={}", processKey, page, size);
        PageResult<ProcessInstanceResponse> result = workflowService.getProcessInstancesByKey(processKey, page, size);
        return Result.success(result);
    }

    @PostMapping("/processes/{processInstanceId}/variables")
    @Operation(summary = "设置流程变量", description = "设置指定流程实例的变量")
    @PreAuthorize("hasAuthority('workflow:process:manage')")
    public Result<Void> setProcessVariables(
            @Parameter(description = "流程实例ID") @PathVariable String processInstanceId,
            @RequestBody Map<String, Object> variables) {
        logger.info("设置流程变量: 流程实例ID={}", processInstanceId);
        workflowService.setProcessVariables(processInstanceId, variables);
        return Result.success();
    }

    @GetMapping("/processes/{processInstanceId}/variables")
    @Operation(summary = "获取流程变量", description = "获取指定流程实例的变量")
    @PreAuthorize("hasAuthority('workflow:process:view')")
    public Result<Map<String, Object>> getProcessVariables(
            @Parameter(description = "流程实例ID") @PathVariable String processInstanceId) {
        logger.debug("获取流程变量: 流程实例ID={}", processInstanceId);
        Map<String, Object> variables = workflowService.getProcessVariables(processInstanceId);
        return Result.success(variables);
    }

    @PostMapping("/tasks/{taskId}/variables")
    @Operation(summary = "设置任务变量", description = "设置指定任务的变量")
    @PreAuthorize("hasAuthority('workflow:task:manage')")
    public Result<Void> setTaskVariables(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @RequestBody Map<String, Object> variables) {
        logger.info("设置任务变量: 任务ID={}", taskId);
        workflowService.setTaskVariables(taskId, variables);
        return Result.success();
    }

    @GetMapping("/tasks/{taskId}/variables")
    @Operation(summary = "获取任务变量", description = "获取指定任务的变量")
    @PreAuthorize("hasAuthority('workflow:task:view')")
    public Result<Map<String, Object>> getTaskVariables(
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        logger.debug("获取任务变量: 任务ID={}", taskId);
        Map<String, Object> variables = workflowService.getTaskVariables(taskId);
        return Result.success(variables);
    }

    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "工作流服务健康检查")
    public Result<String> health() {
        return Result.success("工作流服务运行正常");
    }
}
