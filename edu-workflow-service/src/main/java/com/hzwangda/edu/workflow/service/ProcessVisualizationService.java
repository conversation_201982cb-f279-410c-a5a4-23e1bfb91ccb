package com.hzwangda.edu.workflow.service;

import java.util.Map;

/**
 * 流程可视化服务接口
 * 提供流程图渲染、在线编辑、状态展示等可视化功能
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public interface ProcessVisualizationService {

    // ==================== 流程图渲染功能 ====================

    /**
     * 获取流程图HTML页面
     *
     * @param processDefinitionId 流程定义ID
     * @param showGrid 是否显示网格
     * @param readonly 是否只读模式
     * @return HTML页面内容
     */
    String getProcessDiagramHtml(String processDefinitionId, boolean showGrid, boolean readonly);

    /**
     * 获取流程图JSON数据
     *
     * @param processDefinitionId 流程定义ID
     * @return 流程图JSON数据
     */
    Map<String, Object> getProcessDiagramJson(String processDefinitionId);

    /**
     * 获取流程实例状态图
     *
     * @param processInstanceId 流程实例ID
     * @return 带状态的流程图数据
     */
    Map<String, Object> getProcessInstanceDiagram(String processInstanceId);

    // ==================== 在线编辑功能 ====================

    /**
     * 获取流程编辑器页面
     *
     * @param processDefinitionId 流程定义ID，为空时创建新流程
     * @param template 模板类型
     * @return 编辑器HTML页面
     */
    String getProcessEditor(String processDefinitionId, String template);

    /**
     * 保存编辑的流程
     *
     * @param bpmnXml BPMN XML内容
     * @param processName 流程名称
     * @param category 流程分类
     * @param description 描述
     * @return 保存结果
     */
    Map<String, Object> saveEditedProcess(String bpmnXml, String processName, String category, String description);

    /**
     * 验证流程定义
     *
     * @param bpmnXml BPMN XML内容
     * @return 验证结果
     */
    Map<String, Object> validateProcess(String bpmnXml);

    // ==================== 流程图导出功能 ====================

    /**
     * 导出流程图为PNG
     *
     * @param processDefinitionId 流程定义ID
     * @param width 图片宽度
     * @param height 图片高度
     * @return PNG图片字节数组
     */
    byte[] exportProcessDiagramAsPng(String processDefinitionId, int width, int height);

    /**
     * 导出流程图为SVG
     *
     * @param processDefinitionId 流程定义ID
     * @return SVG内容
     */
    String exportProcessDiagramAsSvg(String processDefinitionId);

    // ==================== 流程统计可视化 ====================

    /**
     * 获取流程统计图表数据
     *
     * @param processDefinitionId 流程定义ID
     * @param days 统计时间范围（天）
     * @return 图表数据
     */
    Map<String, Object> getProcessStatisticsChart(String processDefinitionId, int days);

    /**
     * 获取流程热力图数据
     *
     * @param processDefinitionId 流程定义ID
     * @param days 统计时间范围（天）
     * @return 热力图数据
     */
    Map<String, Object> getProcessHeatmap(String processDefinitionId, int days);
}
