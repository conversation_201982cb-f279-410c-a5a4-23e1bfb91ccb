package com.hzwangda.edu.workflow.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 流程定义查询请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessDefinitionQueryRequest {

    /**
     * 流程定义Key
     */
    private String key;

    /**
     * 流程定义名称（模糊查询）
     */
    private String name;

    /**
     * 流程分类
     */
    private String category;

    /**
     * 是否挂起
     */
    private Boolean suspended;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    private String sortBy = "deploymentTime";

    /**
     * 排序方向（asc/desc）
     */
    private String sortDirection = "desc";
}
