package com.hzwangda.edu.workflow.service.impl;

import com.hzwangda.edu.common.exception.BusinessException;
import com.hzwangda.edu.common.result.ResultCode;
import com.hzwangda.edu.workflow.service.ProcessMonitoringService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.*;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.runtime.ProcessInstanceQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.stream.Collectors;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * 流程监控服务实现
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProcessMonitoringServiceImpl implements ProcessMonitoringService {

    private final RuntimeService runtimeService;
    private final HistoryService historyService;
    private final TaskService taskService;
    private final RepositoryService repositoryService;
    private final ManagementService managementService;

    @Override
    public Map<String, Object> getMonitoringDashboard(String processDefinitionKey, int timeRange) {
        log.info("获取实时监控面板数据: processDefinitionKey={}, timeRange={}", processDefinitionKey, timeRange);

        try {
            Map<String, Object> dashboard = new HashMap<>();

            // 计算时间范围
            Date startTime = Date.from(LocalDateTime.now().minusHours(timeRange).atZone(ZoneId.systemDefault()).toInstant());

            // 活跃实例统计
            ProcessInstanceQuery activeQuery = runtimeService.createProcessInstanceQuery();
            if (StringUtils.hasText(processDefinitionKey)) {
                activeQuery.processDefinitionKey(processDefinitionKey);
            }
            long activeCount = activeQuery.count();

            // 已完成实例统计
            HistoricProcessInstanceQuery completedQuery = historyService.createHistoricProcessInstanceQuery()
                    .finished()
                    .startedAfter(startTime);
            if (StringUtils.hasText(processDefinitionKey)) {
                completedQuery.processDefinitionKey(processDefinitionKey);
            }
            long completedCount = completedQuery.count();

            // 总启动实例统计
            HistoricProcessInstanceQuery totalQuery = historyService.createHistoricProcessInstanceQuery()
                    .startedAfter(startTime);
            if (StringUtils.hasText(processDefinitionKey)) {
                totalQuery.processDefinitionKey(processDefinitionKey);
            }
            long totalCount = totalQuery.count();

            // 异常实例统计（这里简化处理）
            long errorCount = 0;

            dashboard.put("activeInstances", activeCount);
            dashboard.put("completedInstances", completedCount);
            dashboard.put("totalInstances", totalCount);
            dashboard.put("errorInstances", errorCount);
            dashboard.put("completionRate", totalCount > 0 ? (double) completedCount / totalCount * 100 : 0);
            dashboard.put("timeRange", timeRange);
            dashboard.put("updateTime", LocalDateTime.now());

            // 最近活动
            List<Map<String, Object>> recentActivities = getRecentActivities(processDefinitionKey, 10);
            dashboard.put("recentActivities", recentActivities);

            return dashboard;

        } catch (Exception e) {
            log.error("获取实时监控面板数据失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取实时监控面板数据失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getActiveProcessInstances(String processDefinitionKey, int page, int size) {
        log.info("获取活跃流程实例列表: processDefinitionKey={}, page={}, size={}", processDefinitionKey, page, size);

        try {
            ProcessInstanceQuery query = runtimeService.createProcessInstanceQuery();
            if (StringUtils.hasText(processDefinitionKey)) {
                query.processDefinitionKey(processDefinitionKey);
            }

            List<ProcessInstance> instances = query
                    .orderByStartTime()
                    .desc()
                    .listPage((page - 1) * size, size);

            List<Map<String, Object>> result = new ArrayList<>();
            for (ProcessInstance instance : instances) {
                Map<String, Object> instanceInfo = new HashMap<>();
                instanceInfo.put("processInstanceId", instance.getId());
                instanceInfo.put("processDefinitionId", instance.getProcessDefinitionId());
                instanceInfo.put("processDefinitionKey", instance.getProcessDefinitionKey());
                instanceInfo.put("processDefinitionName", instance.getProcessDefinitionName());
                instanceInfo.put("startTime", instance.getStartTime());
                instanceInfo.put("startUserId", instance.getStartUserId());
                instanceInfo.put("suspended", instance.isSuspended());
                instanceInfo.put("businessKey", instance.getBusinessKey());

                // 获取当前活动节点
                List<String> activeActivityIds = runtimeService.getActiveActivityIds(instance.getId());
                instanceInfo.put("activeActivityIds", activeActivityIds);

                result.add(instanceInfo);
            }

            return result;

        } catch (Exception e) {
            log.error("获取活跃流程实例列表失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取活跃流程实例列表失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getProcessInstanceStatus(String processInstanceId) {
        log.info("获取流程实例详细状态: {}", processInstanceId);

        try {
            // 先查询运行时实例
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();

            Map<String, Object> status = new HashMap<>();

            if (processInstance != null) {
                // 运行中的实例
                status.put("processInstanceId", processInstance.getId());
                status.put("processDefinitionId", processInstance.getProcessDefinitionId());
                status.put("processDefinitionKey", processInstance.getProcessDefinitionKey());
                status.put("processDefinitionName", processInstance.getProcessDefinitionName());
                status.put("startTime", processInstance.getStartTime());
                status.put("startUserId", processInstance.getStartUserId());
                status.put("suspended", processInstance.isSuspended());
                status.put("businessKey", processInstance.getBusinessKey());
                status.put("status", "ACTIVE");

                // 获取当前活动节点
                List<String> activeActivityIds = runtimeService.getActiveActivityIds(processInstanceId);
                status.put("activeActivityIds", activeActivityIds);

                // 计算运行时长
                long duration = System.currentTimeMillis() - processInstance.getStartTime().getTime();
                status.put("duration", duration);

            } else {
                // 查询历史实例
                HistoricProcessInstance historicInstance = historyService.createHistoricProcessInstanceQuery()
                        .processInstanceId(processInstanceId)
                        .singleResult();

                if (historicInstance != null) {
                    status.put("processInstanceId", historicInstance.getId());
                    status.put("processDefinitionId", historicInstance.getProcessDefinitionId());
                    status.put("processDefinitionKey", historicInstance.getProcessDefinitionKey());
                    status.put("processDefinitionName", historicInstance.getProcessDefinitionName());
                    status.put("startTime", historicInstance.getStartTime());
                    status.put("endTime", historicInstance.getEndTime());
                    status.put("startUserId", historicInstance.getStartUserId());
                    status.put("businessKey", historicInstance.getBusinessKey());
                    status.put("deleteReason", historicInstance.getDeleteReason());
                    status.put("status", "COMPLETED");

                    if (historicInstance.getEndTime() != null) {
                        long duration = historicInstance.getEndTime().getTime() - historicInstance.getStartTime().getTime();
                        status.put("duration", duration);
                    }
                } else {
                    throw new BusinessException(ResultCode.NOT_FOUND, "流程实例不存在: " + processInstanceId);
                }
            }

            return status;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取流程实例详细状态失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取流程实例详细状态失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getProcessStatistics(String processDefinitionKey, int days) {
        log.info("获取流程执行统计: processDefinitionKey={}, days={}", processDefinitionKey, days);

        try {
            Map<String, Object> statistics = new HashMap<>();

            // 计算时间范围
            Date startTime = Date.from(LocalDateTime.now().minusDays(days).atZone(ZoneId.systemDefault()).toInstant());

            // 基础统计
            HistoricProcessInstanceQuery baseQuery = historyService.createHistoricProcessInstanceQuery()
                    .startedAfter(startTime);
            if (StringUtils.hasText(processDefinitionKey)) {
                baseQuery.processDefinitionKey(processDefinitionKey);
            }

            long totalInstances = baseQuery.count();
            long completedInstances = baseQuery.finished().count();
            long activeInstances = runtimeService.createProcessInstanceQuery()
                    .processDefinitionKey(processDefinitionKey)
                    .count();

            statistics.put("totalInstances", totalInstances);
            statistics.put("completedInstances", completedInstances);
            statistics.put("activeInstances", activeInstances);
            statistics.put("completionRate", totalInstances > 0 ? (double) completedInstances / totalInstances * 100 : 0);

            // 平均执行时间
            List<HistoricProcessInstance> completedList = baseQuery.finished().list();
            if (!completedList.isEmpty()) {
                long totalDuration = completedList.stream()
                        .mapToLong(instance -> instance.getEndTime().getTime() - instance.getStartTime().getTime())
                        .sum();
                statistics.put("averageDuration", totalDuration / completedList.size());
            } else {
                statistics.put("averageDuration", 0);
            }

            statistics.put("days", days);
            statistics.put("statisticsTime", LocalDateTime.now());

            return statistics;

        } catch (Exception e) {
            log.error("获取流程执行统计失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取流程执行统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getProcessTrends(String processDefinitionKey, int days, String granularity) {
        log.info("获取流程趋势分析: processDefinitionKey={}, days={}, granularity={}",
                processDefinitionKey, days, granularity);

        try {
            Map<String, Object> trends = new HashMap<>();

            // 这里应该实现具体的趋势分析逻辑
            // 由于篇幅限制，提供基本结构
            trends.put("processDefinitionKey", processDefinitionKey);
            trends.put("days", days);
            trends.put("granularity", granularity);
            trends.put("trendData", new ArrayList<>());
            trends.put("analysisTime", LocalDateTime.now());

            return trends;

        } catch (Exception e) {
            log.error("获取流程趋势分析失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取流程趋势分析失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getNodeStatistics(String processDefinitionKey, int days) {
        log.info("获取节点执行统计: processDefinitionKey={}, days={}", processDefinitionKey, days);

        try {
            Map<String, Object> nodeStatistics = new HashMap<>();

            // 这里应该实现具体的节点统计逻辑
            nodeStatistics.put("processDefinitionKey", processDefinitionKey);
            nodeStatistics.put("days", days);
            nodeStatistics.put("nodeData", new HashMap<>());
            nodeStatistics.put("statisticsTime", LocalDateTime.now());

            return nodeStatistics;

        } catch (Exception e) {
            log.error("获取节点执行统计失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取节点执行统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getProcessPerformance(String processDefinitionKey, int days) {
        log.info("获取流程性能分析: processDefinitionKey={}, days={}", processDefinitionKey, days);

        try {
            Map<String, Object> performance = new HashMap<>();

            // 计算时间范围
            Date endDate = new Date();
            Date startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000L);

            // 获取指定时间范围内的历史流程实例
            List<HistoricProcessInstance> instances = historyService
                    .createHistoricProcessInstanceQuery()
                    .processDefinitionKey(processDefinitionKey)
                    .startedAfter(startDate)
                    .startedBefore(endDate)
                    .list();

            // 性能指标计算
            Map<String, Object> performanceMetrics = calculatePerformanceMetrics(instances);

            // 时间趋势分析
            Map<String, Object> timeTrends = calculateTimeTrends(instances, days);

            // 节点性能分析
            Map<String, Object> nodePerformance = calculateNodePerformance(processDefinitionKey, startDate, endDate);

            performance.put("processDefinitionKey", processDefinitionKey);
            performance.put("days", days);
            performance.put("totalInstances", instances.size());
            performance.put("performanceMetrics", performanceMetrics);
            performance.put("timeTrends", timeTrends);
            performance.put("nodePerformance", nodePerformance);
            performance.put("analysisTime", LocalDateTime.now());

            return performance;

        } catch (Exception e) {
            log.error("获取流程性能分析失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取流程性能分析失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getProcessBottlenecks(String processDefinitionKey, int days) {
        log.info("获取瓶颈分析: processDefinitionKey={}, days={}", processDefinitionKey, days);

        try {
            List<Map<String, Object>> bottlenecks = new ArrayList<>();

            // 计算时间范围
            Date endDate = new Date();
            Date startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000L);

            // 获取历史任务实例
            List<HistoricTaskInstance> tasks = historyService
                    .createHistoricTaskInstanceQuery()
                    .processDefinitionKey(processDefinitionKey)
                    .taskCreatedAfter(startDate)
                    .taskCreatedBefore(endDate)
                    .finished()
                    .list();

            // 按任务定义Key分组统计
            Map<String, List<HistoricTaskInstance>> taskGroups = tasks.stream()
                    .collect(Collectors.groupingBy(HistoricTaskInstance::getTaskDefinitionKey));

            // 分析每个任务节点的性能
            for (Map.Entry<String, List<HistoricTaskInstance>> entry : taskGroups.entrySet()) {
                String taskKey = entry.getKey();
                List<HistoricTaskInstance> taskList = entry.getValue();

                if (taskList.isEmpty()) continue;

                // 计算平均处理时长
                double avgDuration = taskList.stream()
                        .filter(t -> t.getDurationInMillis() != null)
                        .mapToLong(HistoricTaskInstance::getDurationInMillis)
                        .average()
                        .orElse(0.0);

                // 计算最大处理时长
                long maxDuration = taskList.stream()
                        .filter(t -> t.getDurationInMillis() != null)
                        .mapToLong(HistoricTaskInstance::getDurationInMillis)
                        .max()
                        .orElse(0L);

                // 计算超时任务数量
                long overdueCount = taskList.stream()
                        .filter(t -> t.getDueDate() != null && t.getEndTime() != null)
                        .filter(t -> t.getEndTime().after(t.getDueDate()))
                        .count();

                // 判断是否为瓶颈节点（平均时长超过阈值或超时率高）
                double avgDurationHours = avgDuration / (1000 * 60 * 60);
                double overdueRate = taskList.size() > 0 ? (double) overdueCount / taskList.size() * 100 : 0;

                boolean isBottleneck = avgDurationHours > 24 || overdueRate > 20; // 平均超过24小时或超时率超过20%

                if (isBottleneck) {
                    Map<String, Object> bottleneck = new HashMap<>();
                    bottleneck.put("taskKey", taskKey);
                    bottleneck.put("taskName", taskList.get(0).getName());
                    bottleneck.put("totalTasks", taskList.size());
                    bottleneck.put("avgDurationHours", avgDurationHours);
                    bottleneck.put("maxDurationHours", maxDuration / (1000.0 * 60 * 60));
                    bottleneck.put("overdueCount", overdueCount);
                    bottleneck.put("overdueRate", overdueRate);
                    bottleneck.put("severity", calculateBottleneckSeverity(avgDurationHours, overdueRate));
                    bottleneck.put("recommendations", generateBottleneckRecommendations(avgDurationHours, overdueRate));

                    bottlenecks.add(bottleneck);
                }
            }

            // 按严重程度排序
            bottlenecks.sort((a, b) -> {
                String severityA = (String) a.get("severity");
                String severityB = (String) b.get("severity");
                return getSeverityOrder(severityB) - getSeverityOrder(severityA);
            });

            return bottlenecks;

        } catch (Exception e) {
            log.error("获取瓶颈分析失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取瓶颈分析失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getProcessAlerts(String level, String status, int page, int size) {
        log.info("获取流程告警列表: level={}, status={}, page={}, size={}", level, status, page, size);

        try {
            List<Map<String, Object>> alerts = new ArrayList<>();

            // 这里应该从告警存储中查询告警信息
            // 由于篇幅限制，返回模拟数据

            return alerts;

        } catch (Exception e) {
            log.error("获取流程告警列表失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取流程告警列表失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> createAlertRule(Map<String, Object> alertRuleConfig) {
        log.info("创建告警规则: {}", alertRuleConfig);

        try {
            Map<String, Object> result = new HashMap<>();

            // 这里应该实现告警规则的创建逻辑
            String ruleId = "rule_" + System.currentTimeMillis();
            result.put("ruleId", ruleId);
            result.put("status", "CREATED");
            result.put("createTime", LocalDateTime.now());

            return result;

        } catch (Exception e) {
            log.error("创建告警规则失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "创建告警规则失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> updateAlertRule(String ruleId, Map<String, Object> alertRuleConfig) {
        log.info("更新告警规则: ruleId={}, config={}", ruleId, alertRuleConfig);

        try {
            Map<String, Object> result = new HashMap<>();

            // 这里应该实现告警规则的更新逻辑
            result.put("ruleId", ruleId);
            result.put("status", "UPDATED");
            result.put("updateTime", LocalDateTime.now());

            return result;

        } catch (Exception e) {
            log.error("更新告警规则失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "更新告警规则失败: " + e.getMessage());
        }
    }

    @Override
    public boolean deleteAlertRule(String ruleId) {
        log.info("删除告警规则: {}", ruleId);

        try {
            // 这里应该实现告警规则的删除逻辑
            return true;

        } catch (Exception e) {
            log.error("删除告警规则失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "删除告警规则失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getSystemHealth() {
        log.info("获取系统健康状态");

        try {
            Map<String, Object> health = new HashMap<>();

            // 检查数据库连接
            try {
                long processDefinitionCount = repositoryService.createProcessDefinitionQuery().count();
                health.put("database", "UP");
                health.put("processDefinitionCount", processDefinitionCount);
            } catch (Exception e) {
                health.put("database", "DOWN");
                health.put("databaseError", e.getMessage());
            }

            // 检查Flowable引擎状态
            try {
                String engineVersion = managementService.getProperties().get("schema.version");
                health.put("flowableEngine", "UP");
                health.put("engineVersion", engineVersion);
            } catch (Exception e) {
                health.put("flowableEngine", "DOWN");
                health.put("engineError", e.getMessage());
            }

            // 系统资源检查
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;

            health.put("memory", Map.of(
                "total", totalMemory,
                "used", usedMemory,
                "free", freeMemory,
                "usagePercent", (double) usedMemory / totalMemory * 100
            ));

            health.put("checkTime", LocalDateTime.now());
            health.put("status", "UP");

            return health;

        } catch (Exception e) {
            log.error("获取系统健康状态失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取系统健康状态失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getSystemMetrics() {
        log.info("获取系统指标");

        try {
            Map<String, Object> metrics = new HashMap<>();

            // 流程定义指标
            long totalProcessDefinitions = repositoryService.createProcessDefinitionQuery().count();
            long activeProcessDefinitions = repositoryService.createProcessDefinitionQuery().active().count();

            metrics.put("processDefinitions", Map.of(
                "total", totalProcessDefinitions,
                "active", activeProcessDefinitions,
                "suspended", totalProcessDefinitions - activeProcessDefinitions
            ));

            // 流程实例指标
            long totalActiveInstances = runtimeService.createProcessInstanceQuery().count();
            long totalHistoricInstances = historyService.createHistoricProcessInstanceQuery().count();

            metrics.put("processInstances", Map.of(
                "active", totalActiveInstances,
                "historic", totalHistoricInstances,
                "total", totalHistoricInstances
            ));

            // 任务指标 - 使用替代方法获取任务数量
            long activeTasks = taskService.createTaskQuery().count();
            long historicTasks = historyService.createHistoricTaskInstanceQuery().count();

            metrics.put("tasks", Map.of(
                "active", activeTasks,
                "historic", historicTasks
            ));

            // 系统指标
            Runtime runtime = Runtime.getRuntime();
            metrics.put("system", Map.of(
                "processors", runtime.availableProcessors(),
                "totalMemory", runtime.totalMemory(),
                "freeMemory", runtime.freeMemory(),
                "maxMemory", runtime.maxMemory()
            ));

            metrics.put("metricsTime", LocalDateTime.now());

            return metrics;

        } catch (Exception e) {
            log.error("获取系统指标失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取系统指标失败: " + e.getMessage());
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取最近活动
     */
    private List<Map<String, Object>> getRecentActivities(String processDefinitionKey, int limit) {
        List<Map<String, Object>> activities = new ArrayList<>();

        try {
            HistoricProcessInstanceQuery query = historyService.createHistoricProcessInstanceQuery()
                    .orderByProcessInstanceStartTime()
                    .desc();

            if (StringUtils.hasText(processDefinitionKey)) {
                query.processDefinitionKey(processDefinitionKey);
            }

            List<HistoricProcessInstance> instances = query.listPage(0, limit);

            for (HistoricProcessInstance instance : instances) {
                Map<String, Object> activity = new HashMap<>();
                activity.put("processInstanceId", instance.getId());
                activity.put("processDefinitionKey", instance.getProcessDefinitionKey());
                activity.put("processDefinitionName", instance.getProcessDefinitionName());
                activity.put("startTime", instance.getStartTime());
                activity.put("endTime", instance.getEndTime());
                activity.put("startUserId", instance.getStartUserId());
                activity.put("status", instance.getEndTime() != null ? "COMPLETED" : "ACTIVE");

                activities.add(activity);
            }

        } catch (Exception e) {
            log.warn("获取最近活动失败: {}", e.getMessage());
        }

        return activities;
    }

    /**
     * 计算性能指标
     */
    private Map<String, Object> calculatePerformanceMetrics(List<HistoricProcessInstance> instances) {
        Map<String, Object> metrics = new HashMap<>();

        if (instances.isEmpty()) {
            metrics.put("avgDurationHours", 0.0);
            metrics.put("minDurationHours", 0.0);
            metrics.put("maxDurationHours", 0.0);
            metrics.put("completionRate", 0.0);
            return metrics;
        }

        // 已完成的实例
        List<HistoricProcessInstance> completedInstances = instances.stream()
                .filter(i -> i.getEndTime() != null)
                .collect(Collectors.toList());

        if (!completedInstances.isEmpty()) {
            // 平均执行时长
            double avgDuration = completedInstances.stream()
                    .filter(i -> i.getDurationInMillis() != null)
                    .mapToLong(HistoricProcessInstance::getDurationInMillis)
                    .average()
                    .orElse(0.0);

            // 最小执行时长
            long minDuration = completedInstances.stream()
                    .filter(i -> i.getDurationInMillis() != null)
                    .mapToLong(HistoricProcessInstance::getDurationInMillis)
                    .min()
                    .orElse(0L);

            // 最大执行时长
            long maxDuration = completedInstances.stream()
                    .filter(i -> i.getDurationInMillis() != null)
                    .mapToLong(HistoricProcessInstance::getDurationInMillis)
                    .max()
                    .orElse(0L);

            metrics.put("avgDurationHours", avgDuration / (1000.0 * 60 * 60));
            metrics.put("minDurationHours", minDuration / (1000.0 * 60 * 60));
            metrics.put("maxDurationHours", maxDuration / (1000.0 * 60 * 60));
        }

        // 完成率
        double completionRate = instances.size() > 0 ?
                (double) completedInstances.size() / instances.size() * 100 : 0;
        metrics.put("completionRate", completionRate);

        return metrics;
    }

    /**
     * 计算时间趋势
     */
    private Map<String, Object> calculateTimeTrends(List<HistoricProcessInstance> instances, int days) {
        Map<String, Object> trends = new HashMap<>();

        // 按天分组统计
        Map<String, Long> dailyStarts = new HashMap<>();
        Map<String, Long> dailyCompletions = new HashMap<>();

        for (HistoricProcessInstance instance : instances) {
            if (instance.getStartTime() != null) {
                String startDate = instance.getStartTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate()
                        .toString();
                dailyStarts.merge(startDate, 1L, Long::sum);
            }

            if (instance.getEndTime() != null) {
                String endDate = instance.getEndTime().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate()
                        .toString();
                dailyCompletions.merge(endDate, 1L, Long::sum);
            }
        }

        trends.put("dailyStarts", dailyStarts);
        trends.put("dailyCompletions", dailyCompletions);

        return trends;
    }

    /**
     * 计算节点性能
     */
    private Map<String, Object> calculateNodePerformance(String processDefinitionKey, Date startDate, Date endDate) {
        Map<String, Object> nodePerformance = new HashMap<>();

        // 获取历史任务实例
        List<HistoricTaskInstance> tasks = historyService
                .createHistoricTaskInstanceQuery()
                .processDefinitionKey(processDefinitionKey)
                .taskCreatedAfter(startDate)
                .taskCreatedBefore(endDate)
                .finished()
                .list();

        // 按任务定义Key分组
        Map<String, List<HistoricTaskInstance>> taskGroups = tasks.stream()
                .collect(Collectors.groupingBy(HistoricTaskInstance::getTaskDefinitionKey));

        Map<String, Map<String, Object>> nodeStats = new HashMap<>();

        for (Map.Entry<String, List<HistoricTaskInstance>> entry : taskGroups.entrySet()) {
            String taskKey = entry.getKey();
            List<HistoricTaskInstance> taskList = entry.getValue();

            Map<String, Object> stats = new HashMap<>();

            if (!taskList.isEmpty()) {
                // 平均处理时长
                double avgDuration = taskList.stream()
                        .filter(t -> t.getDurationInMillis() != null)
                        .mapToLong(HistoricTaskInstance::getDurationInMillis)
                        .average()
                        .orElse(0.0);

                // 任务数量
                stats.put("taskCount", taskList.size());
                stats.put("avgDurationMinutes", avgDuration / (1000.0 * 60));
                stats.put("taskName", taskList.get(0).getName());

                nodeStats.put(taskKey, stats);
            }
        }

        nodePerformance.put("nodeStats", nodeStats);
        return nodePerformance;
    }

    /**
     * 计算瓶颈严重程度
     */
    private String calculateBottleneckSeverity(double avgDurationHours, double overdueRate) {
        if (avgDurationHours > 72 || overdueRate > 50) {
            return "严重";
        } else if (avgDurationHours > 48 || overdueRate > 30) {
            return "中等";
        } else {
            return "轻微";
        }
    }

    /**
     * 生成瓶颈改进建议
     */
    private List<String> generateBottleneckRecommendations(double avgDurationHours, double overdueRate) {
        List<String> recommendations = new ArrayList<>();

        if (avgDurationHours > 48) {
            recommendations.add("考虑增加处理人员或优化任务处理流程");
            recommendations.add("检查任务是否存在不必要的等待时间");
        }

        if (overdueRate > 30) {
            recommendations.add("调整任务截止时间设置");
            recommendations.add("加强任务提醒和催办机制");
        }

        if (avgDurationHours > 24) {
            recommendations.add("分析任务复杂度，考虑拆分复杂任务");
        }

        if (recommendations.isEmpty()) {
            recommendations.add("持续监控任务执行情况");
        }

        return recommendations;
    }

    /**
     * 获取严重程度排序值
     */
    private int getSeverityOrder(String severity) {
        switch (severity) {
            case "严重": return 3;
            case "中等": return 2;
            case "轻微": return 1;
            default: return 0;
        }
    }
}
