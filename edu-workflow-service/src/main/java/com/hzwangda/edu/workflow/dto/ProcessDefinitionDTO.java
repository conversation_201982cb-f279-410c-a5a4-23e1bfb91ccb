package com.hzwangda.edu.workflow.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 流程定义DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessDefinitionDTO {

    /**
     * 流程定义ID
     */
    private String id;

    /**
     * 流程定义Key
     */
    private String key;

    /**
     * 流程定义名称
     */
    private String name;

    /**
     * 流程定义描述
     */
    private String description;

    /**
     * 流程定义版本
     */
    private Integer version;

    /**
     * 流程分类
     */
    private String category;

    /**
     * 部署ID
     */
    private String deploymentId;

    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 图片资源名称
     */
    private String diagramResourceName;

    /**
     * 是否挂起
     */
    private Boolean suspended;

    /**
     * 是否启动表单
     */
    private Boolean hasStartFormKey;

    /**
     * 是否图形化
     */
    private Boolean hasGraphicalNotation;

    /**
     * 部署时间
     */
    private LocalDateTime deploymentTime;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 流程实例数量
     */
    private Long instanceCount;

    /**
     * 活跃实例数量
     */
    private Long activeInstanceCount;

    /**
     * 完成实例数量
     */
    private Long completedInstanceCount;
}
