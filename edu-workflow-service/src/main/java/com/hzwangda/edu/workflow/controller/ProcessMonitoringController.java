package com.hzwangda.edu.workflow.controller;

import com.hzwangda.edu.common.result.Result;
import com.hzwangda.edu.workflow.service.ProcessMonitoringService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;
import java.util.List;
import java.util.Map;

/**
 * 流程监控控制器
 * 提供流程实例监控、统计分析、性能分析、告警管理等功能
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/v1/process-monitoring")
@RequiredArgsConstructor
@Slf4j
@Validated
@Tag(name = "流程监控", description = "流程实例监控、统计分析、性能分析、告警管理等功能")
public class ProcessMonitoringController {

    private final ProcessMonitoringService processMonitoringService;

    // ==================== 实时监控功能 ====================

    @Operation(summary = "获取实时监控面板数据", description = "获取流程实例的实时监控面板数据")
    @GetMapping("/dashboard")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_READ', 'WORKFLOW_MANAGE', 'ADMIN')")
    public Result<Map<String, Object>> getMonitoringDashboard(
            @Parameter(description = "流程定义Key，为空时显示所有流程")
            @RequestParam(value = "processDefinitionKey", required = false) String processDefinitionKey,
            @Parameter(description = "时间范围（小时）")
            @RequestParam(value = "timeRange", defaultValue = "24") @Positive int timeRange) {

        log.info("获取实时监控面板数据: processDefinitionKey={}, timeRange={}", processDefinitionKey, timeRange);

        Map<String, Object> dashboardData = processMonitoringService.getMonitoringDashboard(processDefinitionKey, timeRange);
        return Result.success(dashboardData);
    }

    @Operation(summary = "获取活跃流程实例列表", description = "获取当前活跃的流程实例列表")
    @GetMapping("/active-instances")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_READ', 'WORKFLOW_MANAGE', 'ADMIN')")
    public Result<List<Map<String, Object>>> getActiveProcessInstances(
            @Parameter(description = "流程定义Key")
            @RequestParam(value = "processDefinitionKey", required = false) String processDefinitionKey,
            @Parameter(description = "页码")
            @RequestParam(value = "page", defaultValue = "1") @Positive int page,
            @Parameter(description = "页大小")
            @RequestParam(value = "size", defaultValue = "20") @Positive int size) {

        log.info("获取活跃流程实例列表: processDefinitionKey={}, page={}, size={}",
                processDefinitionKey, page, size);

        List<Map<String, Object>> activeInstances = processMonitoringService.getActiveProcessInstances(
                processDefinitionKey, page, size);
        return Result.success(activeInstances);
    }

    @Operation(summary = "获取流程实例详细状态", description = "获取指定流程实例的详细状态信息")
    @GetMapping("/instance/{processInstanceId}/status")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_READ', 'WORKFLOW_MANAGE', 'ADMIN')")
    public Result<Map<String, Object>> getProcessInstanceStatus(
            @Parameter(description = "流程实例ID", required = true)
            @PathVariable @NotBlank(message = "流程实例ID不能为空") String processInstanceId) {

        log.info("获取流程实例详细状态: {}", processInstanceId);

        Map<String, Object> instanceStatus = processMonitoringService.getProcessInstanceStatus(processInstanceId);
        return Result.success(instanceStatus);
    }

    // ==================== 统计分析功能 ====================

    @Operation(summary = "获取流程执行统计", description = "获取流程定义的执行统计信息")
    @GetMapping("/statistics/{processDefinitionKey}")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_READ', 'WORKFLOW_MANAGE', 'ADMIN')")
    public Result<Map<String, Object>> getProcessStatistics(
            @Parameter(description = "流程定义Key", required = true)
            @PathVariable @NotBlank(message = "流程定义Key不能为空") String processDefinitionKey,
            @Parameter(description = "统计时间范围（天）")
            @RequestParam(value = "days", defaultValue = "30") @Positive int days) {

        log.info("获取流程执行统计: processDefinitionKey={}, days={}", processDefinitionKey, days);

        Map<String, Object> statistics = processMonitoringService.getProcessStatistics(processDefinitionKey, days);
        return Result.success(statistics);
    }

    @Operation(summary = "获取流程趋势分析", description = "获取流程执行的趋势分析数据")
    @GetMapping("/trends/{processDefinitionKey}")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_READ', 'WORKFLOW_MANAGE', 'ADMIN')")
    public Result<Map<String, Object>> getProcessTrends(
            @Parameter(description = "流程定义Key", required = true)
            @PathVariable @NotBlank(message = "流程定义Key不能为空") String processDefinitionKey,
            @Parameter(description = "统计时间范围（天）")
            @RequestParam(value = "days", defaultValue = "30") @Positive int days,
            @Parameter(description = "数据粒度：hour/day/week")
            @RequestParam(value = "granularity", defaultValue = "day") String granularity) {

        log.info("获取流程趋势分析: processDefinitionKey={}, days={}, granularity={}",
                processDefinitionKey, days, granularity);

        Map<String, Object> trends = processMonitoringService.getProcessTrends(processDefinitionKey, days, granularity);
        return Result.success(trends);
    }

    @Operation(summary = "获取节点执行统计", description = "获取流程节点的执行统计信息")
    @GetMapping("/node-statistics/{processDefinitionKey}")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_READ', 'WORKFLOW_MANAGE', 'ADMIN')")
    public Result<Map<String, Object>> getNodeStatistics(
            @Parameter(description = "流程定义Key", required = true)
            @PathVariable @NotBlank(message = "流程定义Key不能为空") String processDefinitionKey,
            @Parameter(description = "统计时间范围（天）")
            @RequestParam(value = "days", defaultValue = "30") @Positive int days) {

        log.info("获取节点执行统计: processDefinitionKey={}, days={}", processDefinitionKey, days);

        Map<String, Object> nodeStatistics = processMonitoringService.getNodeStatistics(processDefinitionKey, days);
        return Result.success(nodeStatistics);
    }

    // ==================== 性能分析功能 ====================

    @Operation(summary = "获取流程性能分析", description = "获取流程执行的性能分析报告")
    @GetMapping("/performance/{processDefinitionKey}")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_READ', 'WORKFLOW_MANAGE', 'ADMIN')")
    public Result<Map<String, Object>> getProcessPerformance(
            @Parameter(description = "流程定义Key", required = true)
            @PathVariable @NotBlank(message = "流程定义Key不能为空") String processDefinitionKey,
            @Parameter(description = "统计时间范围（天）")
            @RequestParam(value = "days", defaultValue = "30") @Positive int days) {

        log.info("获取流程性能分析: processDefinitionKey={}, days={}", processDefinitionKey, days);

        Map<String, Object> performance = processMonitoringService.getProcessPerformance(processDefinitionKey, days);
        return Result.success(performance);
    }

    @Operation(summary = "获取瓶颈分析", description = "分析流程执行中的瓶颈节点")
    @GetMapping("/bottlenecks/{processDefinitionKey}")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_READ', 'WORKFLOW_MANAGE', 'ADMIN')")
    public Result<List<Map<String, Object>>> getProcessBottlenecks(
            @Parameter(description = "流程定义Key", required = true)
            @PathVariable @NotBlank(message = "流程定义Key不能为空") String processDefinitionKey,
            @Parameter(description = "统计时间范围（天）")
            @RequestParam(value = "days", defaultValue = "30") @Positive int days) {

        log.info("获取瓶颈分析: processDefinitionKey={}, days={}", processDefinitionKey, days);

        List<Map<String, Object>> bottlenecks = processMonitoringService.getProcessBottlenecks(processDefinitionKey, days);
        return Result.success(bottlenecks);
    }

    // ==================== 告警管理功能 ====================

    @Operation(summary = "获取流程告警列表", description = "获取流程执行的告警信息列表")
    @GetMapping("/alerts")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_READ', 'WORKFLOW_MANAGE', 'ADMIN')")
    public Result<List<Map<String, Object>>> getProcessAlerts(
            @Parameter(description = "告警级别：LOW/MEDIUM/HIGH/CRITICAL")
            @RequestParam(value = "level", required = false) String level,
            @Parameter(description = "告警状态：ACTIVE/RESOLVED")
            @RequestParam(value = "status", required = false) String status,
            @Parameter(description = "页码")
            @RequestParam(value = "page", defaultValue = "1") @Positive int page,
            @Parameter(description = "页大小")
            @RequestParam(value = "size", defaultValue = "20") @Positive int size) {

        log.info("获取流程告警列表: level={}, status={}, page={}, size={}", level, status, page, size);

        List<Map<String, Object>> alerts = processMonitoringService.getProcessAlerts(level, status, page, size);
        return Result.success(alerts);
    }

    @Operation(summary = "创建告警规则", description = "创建流程监控的告警规则")
    @PostMapping("/alert-rules")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_MANAGE', 'ADMIN')")
    public Result<Map<String, Object>> createAlertRule(
            @Parameter(description = "告警规则配置", required = true)
            @RequestBody Map<String, Object> alertRuleConfig) {

        log.info("创建告警规则: {}", alertRuleConfig);

        Map<String, Object> result = processMonitoringService.createAlertRule(alertRuleConfig);
        return Result.success(result);
    }

    @Operation(summary = "更新告警规则", description = "更新流程监控的告警规则")
    @PutMapping("/alert-rules/{ruleId}")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_MANAGE', 'ADMIN')")
    public Result<Map<String, Object>> updateAlertRule(
            @Parameter(description = "告警规则ID", required = true)
            @PathVariable @NotBlank(message = "告警规则ID不能为空") String ruleId,
            @Parameter(description = "告警规则配置", required = true)
            @RequestBody Map<String, Object> alertRuleConfig) {

        log.info("更新告警规则: ruleId={}, config={}", ruleId, alertRuleConfig);

        Map<String, Object> result = processMonitoringService.updateAlertRule(ruleId, alertRuleConfig);
        return Result.success(result);
    }

    @Operation(summary = "删除告警规则", description = "删除流程监控的告警规则")
    @DeleteMapping("/alert-rules/{ruleId}")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_MANAGE', 'ADMIN')")
    public Result<Boolean> deleteAlertRule(
            @Parameter(description = "告警规则ID", required = true)
            @PathVariable @NotBlank(message = "告警规则ID不能为空") String ruleId) {

        log.info("删除告警规则: {}", ruleId);

        boolean result = processMonitoringService.deleteAlertRule(ruleId);
        return Result.success(result);
    }

    // ==================== 系统健康检查 ====================

    @Operation(summary = "获取系统健康状态", description = "获取工作流引擎的健康状态")
    @GetMapping("/health")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_READ', 'WORKFLOW_MANAGE', 'ADMIN')")
    public Result<Map<String, Object>> getSystemHealth() {
        log.info("获取系统健康状态");

        Map<String, Object> health = processMonitoringService.getSystemHealth();
        return Result.success(health);
    }

    @Operation(summary = "获取系统指标", description = "获取工作流引擎的系统指标")
    @GetMapping("/metrics")
    @PreAuthorize("hasAnyAuthority('WORKFLOW_READ', 'WORKFLOW_MANAGE', 'ADMIN')")
    public Result<Map<String, Object>> getSystemMetrics() {
        log.info("获取系统指标");

        Map<String, Object> metrics = processMonitoringService.getSystemMetrics();
        return Result.success(metrics);
    }
}
