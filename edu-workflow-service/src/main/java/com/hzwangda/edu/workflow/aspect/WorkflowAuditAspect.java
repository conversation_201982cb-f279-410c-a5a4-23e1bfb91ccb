package com.hzwangda.edu.workflow.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 工作流审计日志切面
 * 记录重要的工作流操作，包括部署、删除、版本管理等
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class WorkflowAuditAspect {

    private final ObjectMapper objectMapper;

    /**
     * 定义切点：所有Controller中的重要操作
     */
    @Pointcut("@annotation(org.springframework.security.access.prepost.PreAuthorize)")
    public void securedMethods() {}

    /**
     * 定义切点：工作流Controller中的所有方法
     */
    @Pointcut("execution(* com.hzwangda.edu.workflow.controller.ProcessDefinitionController.*(..))")
    public void workflowControllerMethods() {}

    /**
     * 在方法执行前记录审计日志
     */
    @Before("securedMethods() && workflowControllerMethods()")
    public void logBefore(JoinPoint joinPoint) {
        try {
            Map<String, Object> auditLog = createBaseAuditLog(joinPoint);
            auditLog.put("event", "OPERATION_START");
            auditLog.put("arguments", Arrays.toString(joinPoint.getArgs()));

            log.info("工作流审计日志 - 操作开始: {}", objectMapper.writeValueAsString(auditLog));
        } catch (Exception e) {
            log.error("记录审计日志失败", e);
        }
    }

    /**
     * 在方法成功执行后记录审计日志
     */
    @AfterReturning(pointcut = "securedMethods() && workflowControllerMethods()", returning = "result")
    public void logAfterReturning(JoinPoint joinPoint, Object result) {
        try {
            Map<String, Object> auditLog = createBaseAuditLog(joinPoint);
            auditLog.put("event", "OPERATION_SUCCESS");
            auditLog.put("result", result != null ? result.getClass().getSimpleName() : "null");

            log.info("工作流审计日志 - 操作成功: {}", objectMapper.writeValueAsString(auditLog));
        } catch (Exception e) {
            log.error("记录审计日志失败", e);
        }
    }

    /**
     * 在方法抛出异常后记录审计日志
     */
    @AfterThrowing(pointcut = "securedMethods() && workflowControllerMethods()", throwing = "exception")
    public void logAfterThrowing(JoinPoint joinPoint, Throwable exception) {
        try {
            Map<String, Object> auditLog = createBaseAuditLog(joinPoint);
            auditLog.put("event", "OPERATION_FAILED");
            auditLog.put("error", exception.getMessage());
            auditLog.put("exceptionType", exception.getClass().getSimpleName());

            log.error("工作流审计日志 - 操作失败: {}", objectMapper.writeValueAsString(auditLog));
        } catch (Exception e) {
            log.error("记录审计日志失败", e);
        }
    }

    /**
     * 创建基础审计日志信息
     */
    private Map<String, Object> createBaseAuditLog(JoinPoint joinPoint) {
        Map<String, Object> auditLog = new HashMap<>();

        // 基本信息
        auditLog.put("timestamp", LocalDateTime.now());
        auditLog.put("method", joinPoint.getSignature().getName());
        auditLog.put("className", joinPoint.getTarget().getClass().getSimpleName());

        // 用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            auditLog.put("username", authentication.getName());
            auditLog.put("authorities", authentication.getAuthorities());
        } else {
            auditLog.put("username", "anonymous");
            auditLog.put("authorities", "none");
        }

        // 操作分类
        String methodName = joinPoint.getSignature().getName();
        auditLog.put("operationType", categorizeOperation(methodName));

        // 请求参数（简化版，避免敏感信息）
        Object[] args = joinPoint.getArgs();
        if (args != null && args.length > 0) {
            auditLog.put("parameterCount", args.length);
            // 只记录简单类型的参数
            for (int i = 0; i < args.length; i++) {
                Object arg = args[i];
                if (arg instanceof String || arg instanceof Number || arg instanceof Boolean) {
                    auditLog.put("param" + i, arg);
                }
            }
        }

        return auditLog;
    }

    /**
     * 根据方法名称分类操作类型
     */
    private String categorizeOperation(String methodName) {
        if (methodName.contains("deploy")) {
            return "DEPLOY";
        } else if (methodName.contains("delete")) {
            return "DELETE";
        } else if (methodName.contains("import")) {
            return "IMPORT";
        } else if (methodName.contains("export")) {
            return "EXPORT";
        } else if (methodName.contains("rollback")) {
            return "ROLLBACK";
        } else if (methodName.contains("setDefault")) {
            return "VERSION_MANAGE";
        } else if (methodName.contains("update")) {
            return "UPDATE";
        } else if (methodName.contains("activate") || methodName.contains("suspend")) {
            return "STATUS_CHANGE";
        } else if (methodName.contains("query") || methodName.contains("get")) {
            return "QUERY";
        } else {
            return "OTHER";
        }
    }
}
