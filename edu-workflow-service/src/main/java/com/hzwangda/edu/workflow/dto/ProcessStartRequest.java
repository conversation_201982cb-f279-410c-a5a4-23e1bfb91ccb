package com.hzwangda.edu.workflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.Map;

/**
 * 流程启动请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "流程启动请求")
public class ProcessStartRequest {

    @NotBlank(message = "流程键不能为空")
    @Size(max = 64, message = "流程键长度不能超过64个字符")
    @Schema(description = "流程键", example = "leave_approval")
    private String processKey;

    @Size(max = 64, message = "业务键长度不能超过64个字符")
    @Schema(description = "业务键", example = "LEAVE_20241219_001")
    private String businessKey;

    @Size(max = 64, message = "业务类型长度不能超过64个字符")
    @Schema(description = "业务类型", example = "LEAVE_APPLICATION")
    private String businessType;

    @Size(max = 64, message = "业务ID长度不能超过64个字符")
    @Schema(description = "业务ID", example = "123")
    private String businessId;

    @NotNull(message = "发起人ID不能为空")
    @Schema(description = "发起人ID", example = "1")
    private Long initiatorId;

    @NotBlank(message = "发起人用户名不能为空")
    @Size(max = 64, message = "发起人用户名长度不能超过64个字符")
    @Schema(description = "发起人用户名", example = "zhangsan")
    private String initiatorUsername;

    @Size(max = 64, message = "发起人姓名长度不能超过64个字符")
    @Schema(description = "发起人姓名", example = "张三")
    private String initiatorName;

    @Size(max = 255, message = "标题长度不能超过255个字符")
    @Schema(description = "流程标题", example = "张三的请假申请")
    private String title;

    @Size(max = 500, message = "描述长度不能超过500个字符")
    @Schema(description = "流程描述")
    private String description;

    @Schema(description = "优先级", example = "50")
    private Integer priority = 50;

    @Schema(description = "流程变量")
    private Map<String, Object> variables;

    @Schema(description = "表单数据")
    private Map<String, Object> formData;

    @Schema(description = "是否跳过第一个用户任务", example = "false")
    private Boolean skipFirstUserTask = false;

    // 构造函数
    public ProcessStartRequest() {
    }

    // Getter and Setter methods
    public String getProcessKey() {
        return processKey;
    }

    public void setProcessKey(String processKey) {
        this.processKey = processKey;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public Long getInitiatorId() {
        return initiatorId;
    }

    public void setInitiatorId(Long initiatorId) {
        this.initiatorId = initiatorId;
    }

    public String getInitiatorUsername() {
        return initiatorUsername;
    }

    public void setInitiatorUsername(String initiatorUsername) {
        this.initiatorUsername = initiatorUsername;
    }

    public String getInitiatorName() {
        return initiatorName;
    }

    public void setInitiatorName(String initiatorName) {
        this.initiatorName = initiatorName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Map<String, Object> getVariables() {
        return variables;
    }

    public void setVariables(Map<String, Object> variables) {
        this.variables = variables;
    }

    public Map<String, Object> getFormData() {
        return formData;
    }

    public void setFormData(Map<String, Object> formData) {
        this.formData = formData;
    }

    public Boolean getSkipFirstUserTask() {
        return skipFirstUserTask;
    }

    public void setSkipFirstUserTask(Boolean skipFirstUserTask) {
        this.skipFirstUserTask = skipFirstUserTask;
    }
}
