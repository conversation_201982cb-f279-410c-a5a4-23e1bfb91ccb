package com.hzwangda.edu.workflow.service.impl;

import com.hzwangda.edu.workflow.service.ProcessMonitorService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程监控服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class ProcessMonitorServiceImpl implements ProcessMonitorService {

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private RepositoryService repositoryService;

    @Override
    public Map<String, Object> getProcessInstanceStatistics() {
        log.debug("获取流程实例统计信息");

        Map<String, Object> statistics = new HashMap<>();

        // 总流程实例数
        long totalInstances = historyService.createHistoricProcessInstanceQuery().count();

        // 运行中的流程实例数
        long runningInstances = runtimeService.createProcessInstanceQuery().count();

        // 已完成的流程实例数
        long completedInstances = historyService.createHistoricProcessInstanceQuery().finished().count();

        // 挂起的流程实例数
        long suspendedInstances = runtimeService.createProcessInstanceQuery().suspended().count();

        // 今日新增流程实例数
        Date today = Date.from(LocalDateTime.now().toLocalDate().atStartOfDay(ZoneId.systemDefault()).toInstant());
        long todayInstances = historyService.createHistoricProcessInstanceQuery()
                .startedAfter(today)
                .count();

        statistics.put("totalInstances", totalInstances);
        statistics.put("runningInstances", runningInstances);
        statistics.put("completedInstances", completedInstances);
        statistics.put("suspendedInstances", suspendedInstances);
        statistics.put("todayInstances", todayInstances);
        statistics.put("completionRate", totalInstances > 0 ? (double) completedInstances / totalInstances * 100 : 0);

        return statistics;
    }

    @Override
    public Map<String, Object> getTaskStatistics() {
        log.debug("获取任务统计信息");

        Map<String, Object> statistics = new HashMap<>();

        // 总任务数
        long totalTasks = historyService.createHistoricTaskInstanceQuery().count();

        // 待办任务数
        long pendingTasks = taskService.createTaskQuery().count();

        // 已完成任务数
        long completedTasks = historyService.createHistoricTaskInstanceQuery().finished().count();

        // 今日新增任务数
        Date today = Date.from(LocalDateTime.now().toLocalDate().atStartOfDay(ZoneId.systemDefault()).toInstant());
        long todayTasks = historyService.createHistoricTaskInstanceQuery()
                .taskCreatedAfter(today)
                .count();

        // 今日完成任务数
        long todayCompletedTasks = historyService.createHistoricTaskInstanceQuery()
                .taskCompletedAfter(today)
                .count();

        // 超时任务数
        long overdueTasks = taskService.createTaskQuery()
                .taskDueBefore(new Date())
                .count();

        statistics.put("totalTasks", totalTasks);
        statistics.put("pendingTasks", pendingTasks);
        statistics.put("completedTasks", completedTasks);
        statistics.put("todayTasks", todayTasks);
        statistics.put("todayCompletedTasks", todayCompletedTasks);
        statistics.put("overdueTasks", overdueTasks);
        statistics.put("completionRate", totalTasks > 0 ? (double) completedTasks / totalTasks * 100 : 0);

        return statistics;
    }

    @Override
    public Map<String, Object> getProcessDefinitionStatistics(String processDefinitionId) {
        log.debug("获取流程定义统计信息: {}", processDefinitionId);

        Map<String, Object> statistics = new HashMap<>();

        ProcessDefinition processDefinition = repositoryService.getProcessDefinition(processDefinitionId);

        // 流程实例统计
        long totalInstances = historyService.createHistoricProcessInstanceQuery()
                .processDefinitionId(processDefinitionId)
                .count();

        long runningInstances = runtimeService.createProcessInstanceQuery()
                .processDefinitionId(processDefinitionId)
                .count();

        long completedInstances = historyService.createHistoricProcessInstanceQuery()
                .processDefinitionId(processDefinitionId)
                .finished()
                .count();

        // 平均执行时长
        List<HistoricProcessInstance> completedProcesses = historyService
                .createHistoricProcessInstanceQuery()
                .processDefinitionId(processDefinitionId)
                .finished()
                .list();

        double avgDuration = completedProcesses.stream()
                .filter(p -> p.getDurationInMillis() != null)
                .mapToLong(HistoricProcessInstance::getDurationInMillis)
                .average()
                .orElse(0.0);

        statistics.put("processDefinitionId", processDefinitionId);
        statistics.put("processDefinitionKey", processDefinition.getKey());
        statistics.put("processDefinitionName", processDefinition.getName());
        statistics.put("version", processDefinition.getVersion());
        statistics.put("totalInstances", totalInstances);
        statistics.put("runningInstances", runningInstances);
        statistics.put("completedInstances", completedInstances);
        statistics.put("completionRate", totalInstances > 0 ? (double) completedInstances / totalInstances * 100 : 0);
        statistics.put("avgDurationMinutes", avgDuration / (1000 * 60)); // 转换为分钟

        return statistics;
    }

    @Override
    public Map<String, Object> getUserTaskStatistics(String userId) {
        log.debug("获取用户任务统计信息: {}", userId);

        Map<String, Object> statistics = new HashMap<>();

        // 待办任务数
        long pendingTasks = taskService.createTaskQuery()
                .taskAssignee(userId)
                .count();

        // 已完成任务数
        long completedTasks = historyService.createHistoricTaskInstanceQuery()
                .taskAssignee(userId)
                .finished()
                .count();

        // 今日完成任务数
        Date today = Date.from(LocalDateTime.now().toLocalDate().atStartOfDay(ZoneId.systemDefault()).toInstant());
        long todayCompletedTasks = historyService.createHistoricTaskInstanceQuery()
                .taskAssignee(userId)
                .taskCompletedAfter(today)
                .count();

        // 超时任务数
        long overdueTasks = taskService.createTaskQuery()
                .taskAssignee(userId)
                .taskDueBefore(new Date())
                .count();

        // 平均任务处理时长
        List<HistoricTaskInstance> userCompletedTasks = historyService
                .createHistoricTaskInstanceQuery()
                .taskAssignee(userId)
                .finished()
                .list();

        double avgTaskDuration = userCompletedTasks.stream()
                .filter(t -> t.getDurationInMillis() != null)
                .mapToLong(HistoricTaskInstance::getDurationInMillis)
                .average()
                .orElse(0.0);

        statistics.put("userId", userId);
        statistics.put("pendingTasks", pendingTasks);
        statistics.put("completedTasks", completedTasks);
        statistics.put("todayCompletedTasks", todayCompletedTasks);
        statistics.put("overdueTasks", overdueTasks);
        statistics.put("avgTaskDurationMinutes", avgTaskDuration / (1000 * 60));

        return statistics;
    }

    @Override
    public Map<String, Object> getProcessDurationStatistics(String processDefinitionKey,
                                                           LocalDateTime startTime,
                                                           LocalDateTime endTime) {
        log.debug("获取流程执行时长统计: key={}, startTime={}, endTime={}", processDefinitionKey, startTime, endTime);

        Map<String, Object> statistics = new HashMap<>();

        Date start = Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant());
        Date end = Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant());

        List<HistoricProcessInstance> processes = historyService
                .createHistoricProcessInstanceQuery()
                .processDefinitionKey(processDefinitionKey)
                .startedAfter(start)
                .startedBefore(end)
                .finished()
                .list();

        if (processes.isEmpty()) {
            statistics.put("count", 0);
            statistics.put("avgDuration", 0);
            statistics.put("minDuration", 0);
            statistics.put("maxDuration", 0);
            return statistics;
        }

        List<Long> durations = processes.stream()
                .filter(p -> p.getDurationInMillis() != null)
                .map(HistoricProcessInstance::getDurationInMillis)
                .collect(Collectors.toList());

        if (durations.isEmpty()) {
            statistics.put("count", processes.size());
            statistics.put("avgDuration", 0);
            statistics.put("minDuration", 0);
            statistics.put("maxDuration", 0);
            return statistics;
        }

        double avgDuration = durations.stream().mapToLong(Long::longValue).average().orElse(0.0);
        long minDuration = durations.stream().mapToLong(Long::longValue).min().orElse(0L);
        long maxDuration = durations.stream().mapToLong(Long::longValue).max().orElse(0L);

        statistics.put("processDefinitionKey", processDefinitionKey);
        statistics.put("count", processes.size());
        statistics.put("avgDurationMinutes", avgDuration / (1000 * 60));
        statistics.put("minDurationMinutes", minDuration / (1000 * 60));
        statistics.put("maxDurationMinutes", maxDuration / (1000 * 60));

        return statistics;
    }

    @Override
    public Map<String, Object> getProcessCompletionRateStatistics(String processDefinitionKey,
                                                                LocalDateTime startTime,
                                                                LocalDateTime endTime) {
        log.debug("获取流程完成率统计: key={}, startTime={}, endTime={}", processDefinitionKey, startTime, endTime);

        Map<String, Object> statistics = new HashMap<>();

        Date start = Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant());
        Date end = Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant());

        // 总启动数
        long totalStarted = historyService.createHistoricProcessInstanceQuery()
                .processDefinitionKey(processDefinitionKey)
                .startedAfter(start)
                .startedBefore(end)
                .count();

        // 已完成数
        long completed = historyService.createHistoricProcessInstanceQuery()
                .processDefinitionKey(processDefinitionKey)
                .startedAfter(start)
                .startedBefore(end)
                .finished()
                .count();

        // 运行中数
        long running = totalStarted - completed;

        double completionRate = totalStarted > 0 ? (double) completed / totalStarted * 100 : 0;

        statistics.put("processDefinitionKey", processDefinitionKey);
        statistics.put("totalStarted", totalStarted);
        statistics.put("completed", completed);
        statistics.put("running", running);
        statistics.put("completionRate", completionRate);

        return statistics;
    }

    @Override
    public Map<String, Object> getTaskEfficiencyStatistics(String assignee,
                                                          LocalDateTime startTime,
                                                          LocalDateTime endTime) {
        log.debug("获取任务处理效率统计: assignee={}, startTime={}, endTime={}", assignee, startTime, endTime);

        Map<String, Object> statistics = new HashMap<>();

        Date start = Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant());
        Date end = Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant());

        List<HistoricTaskInstance> tasks = historyService.createHistoricTaskInstanceQuery()
                .taskAssignee(assignee)
                .taskCompletedAfter(start)
                .taskCompletedBefore(end)
                .list();

        if (tasks.isEmpty()) {
            statistics.put("assignee", assignee);
            statistics.put("completedTasks", 0);
            statistics.put("avgProcessingTime", 0);
            return statistics;
        }

        double avgProcessingTime = tasks.stream()
                .filter(t -> t.getDurationInMillis() != null)
                .mapToLong(HistoricTaskInstance::getDurationInMillis)
                .average()
                .orElse(0.0);

        statistics.put("assignee", assignee);
        statistics.put("completedTasks", tasks.size());
        statistics.put("avgProcessingTimeMinutes", avgProcessingTime / (1000 * 60));

        return statistics;
    }

    @Override
    public Map<String, Object> getProcessBottleneckAnalysis(String processDefinitionKey) {
        log.debug("获取流程瓶颈分析: {}", processDefinitionKey);

        Map<String, Object> analysis = new HashMap<>();

        // TODO: 实现流程瓶颈分析逻辑
        // 分析各个节点的平均停留时间，找出瓶颈节点

        analysis.put("processDefinitionKey", processDefinitionKey);
        analysis.put("bottleneckNodes", Collections.emptyList());
        analysis.put("analysis", "瓶颈分析功能待实现");

        return analysis;
    }

    @Override
    public Map<String, Object> getRealTimeMonitoringData() {
        log.debug("获取实时流程监控数据");

        Map<String, Object> data = new HashMap<>();

        // 实时统计数据
        data.put("currentRunningInstances", runtimeService.createProcessInstanceQuery().count());
        data.put("currentPendingTasks", taskService.createTaskQuery().count());
        data.put("currentOverdueTasks", taskService.createTaskQuery().taskDueBefore(new Date()).count());
        data.put("timestamp", LocalDateTime.now());

        return data;
    }

    @Override
    public Map<String, Object> getProcessHealthAssessment() {
        log.debug("获取流程健康度评估");

        Map<String, Object> assessment = new HashMap<>();

        // TODO: 实现流程健康度评估逻辑
        // 基于完成率、平均时长、异常率等指标评估健康度

        assessment.put("overallHealth", "良好");
        assessment.put("score", 85);
        assessment.put("assessment", "流程健康度评估功能待实现");

        return assessment;
    }

    @Override
    public Map<String, Object> getAbnormalProcessInstances() {
        log.debug("获取异常流程实例");

        Map<String, Object> result = new HashMap<>();

        // TODO: 实现异常流程实例检测逻辑
        // 检测长时间运行、异常终止等情况

        result.put("abnormalInstances", Collections.emptyList());
        result.put("count", 0);

        return result;
    }

    @Override
    public Map<String, Object> getOverdueTaskStatistics() {
        log.debug("获取超时任务统计");

        Map<String, Object> statistics = new HashMap<>();

        // 超时任务列表
        List<Task> overdueTasks = taskService.createTaskQuery()
                .taskDueBefore(new Date())
                .list();

        // 按流程定义分组统计
        Map<String, Long> overdueByProcess = overdueTasks.stream()
                .collect(Collectors.groupingBy(
                    Task::getProcessDefinitionId,
                    Collectors.counting()
                ));

        // 按处理人分组统计
        Map<String, Long> overdueByAssignee = overdueTasks.stream()
                .filter(task -> task.getAssignee() != null)
                .collect(Collectors.groupingBy(
                    Task::getAssignee,
                    Collectors.counting()
                ));

        statistics.put("totalOverdueTasks", overdueTasks.size());
        statistics.put("overdueByProcess", overdueByProcess);
        statistics.put("overdueByAssignee", overdueByAssignee);

        return statistics;
    }
}
