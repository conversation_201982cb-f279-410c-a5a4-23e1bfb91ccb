package com.hzwangda.edu.workflow.controller;

import com.hzwangda.edu.common.result.Result;
import com.hzwangda.edu.workflow.service.ProcessMonitorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 流程监控控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/process-monitor")
@Tag(name = "流程监控", description = "流程监控和统计分析功能")
@Validated
public class ProcessMonitorController {

    @Autowired
    private ProcessMonitorService processMonitorService;

    @Operation(summary = "获取流程实例统计", description = "获取流程实例的整体统计信息")
    @GetMapping("/process-instances/statistics")
    public Result<Map<String, Object>> getProcessInstanceStatistics() {
        log.info("获取流程实例统计信息");

        Map<String, Object> statistics = processMonitorService.getProcessInstanceStatistics();
        return Result.success(statistics);
    }

    @Operation(summary = "获取任务统计", description = "获取任务的整体统计信息")
    @GetMapping("/tasks/statistics")
    public Result<Map<String, Object>> getTaskStatistics() {
        log.info("获取任务统计信息");

        Map<String, Object> statistics = processMonitorService.getTaskStatistics();
        return Result.success(statistics);
    }

    @Operation(summary = "获取流程定义统计", description = "获取指定流程定义的统计信息")
    @GetMapping("/process-definitions/{processDefinitionId}/statistics")
    public Result<Map<String, Object>> getProcessDefinitionStatistics(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String processDefinitionId) {
        log.info("获取流程定义统计信息: {}", processDefinitionId);

        Map<String, Object> statistics = processMonitorService.getProcessDefinitionStatistics(processDefinitionId);
        return Result.success(statistics);
    }

    @Operation(summary = "获取用户任务统计", description = "获取指定用户的任务统计信息")
    @GetMapping("/users/{userId}/task-statistics")
    public Result<Map<String, Object>> getUserTaskStatistics(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotBlank(message = "用户ID不能为空") String userId) {
        log.info("获取用户任务统计信息: {}", userId);

        Map<String, Object> statistics = processMonitorService.getUserTaskStatistics(userId);
        return Result.success(statistics);
    }

    @Operation(summary = "获取流程执行时长统计", description = "获取指定时间段内流程的执行时长统计")
    @GetMapping("/process-duration-statistics")
    public Result<Map<String, Object>> getProcessDurationStatistics(
            @Parameter(description = "流程定义Key", required = true)
            @RequestParam @NotBlank(message = "流程定义Key不能为空") String processDefinitionKey,
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        log.info("获取流程执行时长统计: key={}, startTime={}, endTime={}", processDefinitionKey, startTime, endTime);

        Map<String, Object> statistics = processMonitorService.getProcessDurationStatistics(
                processDefinitionKey, startTime, endTime);
        return Result.success(statistics);
    }

    @Operation(summary = "获取流程完成率统计", description = "获取指定时间段内流程的完成率统计")
    @GetMapping("/process-completion-rate-statistics")
    public Result<Map<String, Object>> getProcessCompletionRateStatistics(
            @Parameter(description = "流程定义Key", required = true)
            @RequestParam @NotBlank(message = "流程定义Key不能为空") String processDefinitionKey,
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        log.info("获取流程完成率统计: key={}, startTime={}, endTime={}", processDefinitionKey, startTime, endTime);

        Map<String, Object> statistics = processMonitorService.getProcessCompletionRateStatistics(
                processDefinitionKey, startTime, endTime);
        return Result.success(statistics);
    }

    @Operation(summary = "获取任务处理效率统计", description = "获取指定用户在指定时间段内的任务处理效率统计")
    @GetMapping("/task-efficiency-statistics")
    public Result<Map<String, Object>> getTaskEfficiencyStatistics(
            @Parameter(description = "处理人", required = true)
            @RequestParam @NotBlank(message = "处理人不能为空") String assignee,
            @Parameter(description = "开始时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        log.info("获取任务处理效率统计: assignee={}, startTime={}, endTime={}", assignee, startTime, endTime);

        Map<String, Object> statistics = processMonitorService.getTaskEfficiencyStatistics(
                assignee, startTime, endTime);
        return Result.success(statistics);
    }

    @Operation(summary = "获取流程瓶颈分析", description = "分析指定流程的瓶颈节点")
    @GetMapping("/process-bottleneck-analysis")
    public Result<Map<String, Object>> getProcessBottleneckAnalysis(
            @Parameter(description = "流程定义Key", required = true)
            @RequestParam @NotBlank(message = "流程定义Key不能为空") String processDefinitionKey) {
        log.info("获取流程瓶颈分析: {}", processDefinitionKey);

        Map<String, Object> analysis = processMonitorService.getProcessBottleneckAnalysis(processDefinitionKey);
        return Result.success(analysis);
    }

    @Operation(summary = "获取实时监控数据", description = "获取流程的实时监控数据")
    @GetMapping("/real-time-data")
    public Result<Map<String, Object>> getRealTimeMonitoringData() {
        log.info("获取实时流程监控数据");

        Map<String, Object> data = processMonitorService.getRealTimeMonitoringData();
        return Result.success(data);
    }

    @Operation(summary = "获取流程健康度评估", description = "获取流程系统的健康度评估结果")
    @GetMapping("/health-assessment")
    public Result<Map<String, Object>> getProcessHealthAssessment() {
        log.info("获取流程健康度评估");

        Map<String, Object> assessment = processMonitorService.getProcessHealthAssessment();
        return Result.success(assessment);
    }

    @Operation(summary = "获取异常流程实例", description = "获取系统中的异常流程实例")
    @GetMapping("/abnormal-instances")
    public Result<Map<String, Object>> getAbnormalProcessInstances() {
        log.info("获取异常流程实例");

        Map<String, Object> result = processMonitorService.getAbnormalProcessInstances();
        return Result.success(result);
    }

    @Operation(summary = "获取超时任务统计", description = "获取系统中超时任务的统计信息")
    @GetMapping("/overdue-tasks/statistics")
    public Result<Map<String, Object>> getOverdueTaskStatistics() {
        log.info("获取超时任务统计");

        Map<String, Object> statistics = processMonitorService.getOverdueTaskStatistics();
        return Result.success(statistics);
    }
}
