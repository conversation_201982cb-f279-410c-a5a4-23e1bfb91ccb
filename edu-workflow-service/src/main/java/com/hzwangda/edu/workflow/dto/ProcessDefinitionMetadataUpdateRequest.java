package com.hzwangda.edu.workflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Size;

/**
 * 流程定义元数据更新请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "流程定义元数据更新请求")
public class ProcessDefinitionMetadataUpdateRequest {

    @Size(max = 100, message = "流程名称长度不能超过100个字符")
    @Schema(description = "流程名称", example = "员工请假审批流程")
    private String name;

    @Size(max = 500, message = "流程描述长度不能超过500个字符")
    @Schema(description = "流程描述", example = "用于处理员工请假申请的审批流程")
    private String description;

    @Size(max = 50, message = "流程分类长度不能超过50个字符")
    @Schema(description = "流程分类", example = "人事管理")
    private String category;

    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    @Size(max = 200, message = "更新原因长度不能超过200个字符")
    @Schema(description = "更新原因", example = "优化审批流程")
    private String updateReason;
}
