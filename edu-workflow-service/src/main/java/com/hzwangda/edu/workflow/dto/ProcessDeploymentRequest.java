package com.hzwangda.edu.workflow.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 流程部署请求
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessDeploymentRequest {

    /**
     * 部署名称
     */
    private String name;

    /**
     * 流程分类
     */
    private String category;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 是否激活
     */
    private Boolean activate = true;

    /**
     * 描述
     */
    private String description;
}
