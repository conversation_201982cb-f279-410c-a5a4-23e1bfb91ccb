package com.hzwangda.edu.workflow.controller;

import com.hzwangda.edu.common.result.Result;
import com.hzwangda.edu.workflow.service.ProcessDiagramService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotBlank;

/**
 * 流程图控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/process-diagrams")
@Tag(name = "流程图管理", description = "流程图生成和展示功能")
@Validated
public class ProcessDiagramController {

    @Autowired
    private ProcessDiagramService processDiagramService;

    @Operation(summary = "生成流程定义图", description = "生成流程定义的PNG格式流程图")
    @GetMapping("/definition/{processDefinitionId}")
    public ResponseEntity<byte[]> generateProcessDefinitionDiagram(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String processDefinitionId) {
        log.info("生成流程定义图: {}", processDefinitionId);

        byte[] diagram = processDiagramService.generateProcessDefinitionDiagram(processDefinitionId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.IMAGE_PNG);
        headers.setContentLength(diagram.length);
        headers.set("Content-Disposition", "inline; filename=process-definition-" + processDefinitionId + ".png");
        headers.set("Cache-Control", "public, max-age=3600"); // 缓存1小时

        return ResponseEntity.ok()
                .headers(headers)
                .body(diagram);
    }

    @Operation(summary = "生成流程实例图", description = "生成流程实例的PNG格式流程图（高亮当前节点）")
    @GetMapping("/instance/{processInstanceId}")
    public ResponseEntity<byte[]> generateProcessInstanceDiagram(
            @Parameter(description = "流程实例ID", required = true)
            @PathVariable @NotBlank(message = "流程实例ID不能为空") String processInstanceId) {
        log.info("生成流程实例图: {}", processInstanceId);

        byte[] diagram = processDiagramService.generateProcessInstanceDiagram(processInstanceId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.IMAGE_PNG);
        headers.setContentLength(diagram.length);
        headers.set("Content-Disposition", "inline; filename=process-instance-" + processInstanceId + ".png");
        headers.set("Cache-Control", "public, max-age=300"); // 缓存5分钟

        return ResponseEntity.ok()
                .headers(headers)
                .body(diagram);
    }

    @Operation(summary = "生成流程定义SVG图", description = "生成流程定义的SVG格式流程图")
    @GetMapping("/definition/{processDefinitionId}/svg")
    public ResponseEntity<String> generateProcessDefinitionSvg(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String processDefinitionId) {
        log.info("生成流程定义SVG图: {}", processDefinitionId);

        String svg = processDiagramService.generateProcessDefinitionSvg(processDefinitionId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.valueOf("image/svg+xml"));
        headers.set("Content-Disposition", "inline; filename=process-definition-" + processDefinitionId + ".svg");
        headers.set("Cache-Control", "public, max-age=3600"); // 缓存1小时

        return ResponseEntity.ok()
                .headers(headers)
                .body(svg);
    }

    @Operation(summary = "生成流程实例SVG图", description = "生成流程实例的SVG格式流程图（高亮当前节点）")
    @GetMapping("/instance/{processInstanceId}/svg")
    public ResponseEntity<String> generateProcessInstanceSvg(
            @Parameter(description = "流程实例ID", required = true)
            @PathVariable @NotBlank(message = "流程实例ID不能为空") String processInstanceId) {
        log.info("生成流程实例SVG图: {}", processInstanceId);

        String svg = processDiagramService.generateProcessInstanceSvg(processInstanceId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.valueOf("image/svg+xml"));
        headers.set("Content-Disposition", "inline; filename=process-instance-" + processInstanceId + ".svg");
        headers.set("Cache-Control", "public, max-age=300"); // 缓存5分钟

        return ResponseEntity.ok()
                .headers(headers)
                .body(svg);
    }

    @Operation(summary = "清理流程图缓存", description = "清理指定流程定义的流程图缓存")
    @DeleteMapping("/cache/definition/{processDefinitionId}")
    public Result<Void> clearDiagramCache(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String processDefinitionId) {
        log.info("清理流程图缓存: {}", processDefinitionId);

        processDiagramService.clearDiagramCache(processDefinitionId);
        return Result.success();
    }

    @Operation(summary = "清理所有流程图缓存", description = "清理所有流程图缓存")
    @DeleteMapping("/cache/all")
    public Result<Void> clearAllDiagramCache() {
        log.info("清理所有流程图缓存");

        processDiagramService.clearAllDiagramCache();
        return Result.success();
    }

    @Operation(summary = "获取缓存统计信息", description = "获取流程图缓存的统计信息")
    @GetMapping("/cache/stats")
    public Result<Object> getDiagramCacheStats() {
        log.info("获取流程图缓存统计信息");

        Object stats = processDiagramService.getDiagramCacheStats();
        return Result.success(stats);
    }
}
