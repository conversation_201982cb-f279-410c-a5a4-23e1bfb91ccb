package com.hzwangda.edu.workflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 流程实例响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "流程实例响应")
public class ProcessInstanceResponse {

    @Schema(description = "实例ID", example = "1")
    private Long id;

    @Schema(description = "Flowable流程实例ID")
    private String flowableInstanceId;

    @Schema(description = "流程键", example = "leave_approval")
    private String processKey;

    @Schema(description = "流程名称", example = "请假审批流程")
    private String processName;

    @Schema(description = "业务键", example = "LEAVE_20241219_001")
    private String businessKey;

    @Schema(description = "业务类型", example = "LEAVE_APPLICATION")
    private String businessType;

    @Schema(description = "业务ID", example = "123")
    private String businessId;

    @Schema(description = "发起人ID", example = "1")
    private Long initiatorId;

    @Schema(description = "发起人用户名", example = "zhangsan")
    private String initiatorUsername;

    @Schema(description = "发起人姓名", example = "张三")
    private String initiatorName;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "状态", example = "RUNNING")
    private String status;

    @Schema(description = "当前节点", example = "manager_approval")
    private String currentActivity;

    @Schema(description = "当前节点名称", example = "部门经理审批")
    private String currentActivityName;

    @Schema(description = "流程标题", example = "张三的请假申请")
    private String title;

    @Schema(description = "流程描述")
    private String description;

    @Schema(description = "优先级", example = "50")
    private Integer priority;

    @Schema(description = "持续时间(毫秒)")
    private Long duration;

    @Schema(description = "流程变量")
    private Map<String, Object> variables;

    @Schema(description = "结束原因")
    private String endReason;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    // 构造函数
    public ProcessInstanceResponse() {
    }

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFlowableInstanceId() {
        return flowableInstanceId;
    }

    public void setFlowableInstanceId(String flowableInstanceId) {
        this.flowableInstanceId = flowableInstanceId;
    }

    public String getProcessKey() {
        return processKey;
    }

    public void setProcessKey(String processKey) {
        this.processKey = processKey;
    }

    public String getProcessName() {
        return processName;
    }

    public void setProcessName(String processName) {
        this.processName = processName;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public Long getInitiatorId() {
        return initiatorId;
    }

    public void setInitiatorId(Long initiatorId) {
        this.initiatorId = initiatorId;
    }

    public String getInitiatorUsername() {
        return initiatorUsername;
    }

    public void setInitiatorUsername(String initiatorUsername) {
        this.initiatorUsername = initiatorUsername;
    }

    public String getInitiatorName() {
        return initiatorName;
    }

    public void setInitiatorName(String initiatorName) {
        this.initiatorName = initiatorName;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCurrentActivity() {
        return currentActivity;
    }

    public void setCurrentActivity(String currentActivity) {
        this.currentActivity = currentActivity;
    }

    public String getCurrentActivityName() {
        return currentActivityName;
    }

    public void setCurrentActivityName(String currentActivityName) {
        this.currentActivityName = currentActivityName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public Map<String, Object> getVariables() {
        return variables;
    }

    public void setVariables(Map<String, Object> variables) {
        this.variables = variables;
    }

    public String getEndReason() {
        return endReason;
    }

    public void setEndReason(String endReason) {
        this.endReason = endReason;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
