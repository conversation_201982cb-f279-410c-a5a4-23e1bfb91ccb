package com.hzwangda.edu.workflow.config;

import org.flowable.common.engine.impl.history.HistoryLevel;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.ProcessEngineConfiguration;
import org.flowable.image.ProcessDiagramGenerator;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * Flowable工作流引擎配置
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class FlowableConfig {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Bean
    public SpringProcessEngineConfiguration processEngineConfiguration() {
        SpringProcessEngineConfiguration config = new SpringProcessEngineConfiguration();

        // 数据源配置
        config.setDataSource(dataSource);
        config.setTransactionManager(transactionManager);

        // 数据库配置
        config.setDatabaseSchemaUpdate(ProcessEngineConfiguration.DB_SCHEMA_UPDATE_TRUE);
        config.setDatabaseType("postgres");

        // 异步执行器配置
        config.setAsyncExecutorActivate(true);
        config.setAsyncExecutorDefaultAsyncJobAcquireWaitTime(10000);
        config.setAsyncExecutorDefaultTimerJobAcquireWaitTime(10000);

        // 历史级别配置
        config.setHistoryLevel(HistoryLevel.FULL);

        // 邮件服务器配置（如果需要）
        config.setMailServerHost("localhost");
        config.setMailServerPort(25);
        config.setMailServerDefaultFrom("<EMAIL>");

        // 字体配置（用于流程图生成）
        config.setActivityFontName("宋体");
        config.setLabelFontName("宋体");
        config.setAnnotationFontName("宋体");

        // 启用安全脚本
        config.setEnableSafeBpmnXml(true);

        return config;
    }

    @Bean
    public ProcessEngine processEngine() {
        return processEngineConfiguration().buildProcessEngine();
    }

    /**
     * 配置流程图生成器Bean
     * 用于流程可视化服务
     */
    @Bean
    public ProcessDiagramGenerator processDiagramGenerator() {
        return processEngine().getProcessEngineConfiguration().getProcessDiagramGenerator();
    }
}
