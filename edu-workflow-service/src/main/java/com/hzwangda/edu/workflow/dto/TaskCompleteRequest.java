package com.hzwangda.edu.workflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.Map;

/**
 * 任务完成请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "任务完成请求")
public class TaskCompleteRequest {

    @NotBlank(message = "任务ID不能为空")
    @Size(max = 64, message = "任务ID长度不能超过64个字符")
    @Schema(description = "任务ID")
    private String taskId;

    @NotNull(message = "处理人ID不能为空")
    @Schema(description = "处理人ID", example = "1")
    private Long assigneeId;

    @NotBlank(message = "处理人用户名不能为空")
    @Size(max = 64, message = "处理人用户名长度不能超过64个字符")
    @Schema(description = "处理人用户名", example = "manager")
    private String assigneeUsername;

    @Size(max = 64, message = "处理人姓名长度不能超过64个字符")
    @Schema(description = "处理人姓名", example = "李经理")
    private String assigneeName;

    @NotBlank(message = "审批结果不能为空")
    @Size(max = 20, message = "审批结果长度不能超过20个字符")
    @Schema(description = "审批结果", example = "APPROVED")
    private String approvalResult;

    @Size(max = 500, message = "审批意见长度不能超过500个字符")
    @Schema(description = "审批意见")
    private String approvalComment;

    @Schema(description = "任务变量")
    private Map<String, Object> variables;

    @Schema(description = "表单数据")
    private Map<String, Object> formData;

    @Schema(description = "是否强制完成", example = "false")
    private Boolean forceComplete = false;

    @Size(max = 64, message = "委派/转办目标用户长度不能超过64个字符")
    @Schema(description = "委派/转办目标用户", example = "targetUser")
    private String targetUser;

    @Size(max = 500, message = "委派/转办原因长度不能超过500个字符")
    @Schema(description = "委派/转办原因")
    private String delegateReason;

    // 构造函数
    public TaskCompleteRequest() {
    }

    // Getter and Setter methods
    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public Long getAssigneeId() {
        return assigneeId;
    }

    public void setAssigneeId(Long assigneeId) {
        this.assigneeId = assigneeId;
    }

    public String getAssigneeUsername() {
        return assigneeUsername;
    }

    public void setAssigneeUsername(String assigneeUsername) {
        this.assigneeUsername = assigneeUsername;
    }

    public String getAssigneeName() {
        return assigneeName;
    }

    public void setAssigneeName(String assigneeName) {
        this.assigneeName = assigneeName;
    }

    public String getApprovalResult() {
        return approvalResult;
    }

    public void setApprovalResult(String approvalResult) {
        this.approvalResult = approvalResult;
    }

    public String getApprovalComment() {
        return approvalComment;
    }

    public void setApprovalComment(String approvalComment) {
        this.approvalComment = approvalComment;
    }

    public Map<String, Object> getVariables() {
        return variables;
    }

    public void setVariables(Map<String, Object> variables) {
        this.variables = variables;
    }

    public Map<String, Object> getFormData() {
        return formData;
    }

    public void setFormData(Map<String, Object> formData) {
        this.formData = formData;
    }

    public Boolean getForceComplete() {
        return forceComplete;
    }

    public void setForceComplete(Boolean forceComplete) {
        this.forceComplete = forceComplete;
    }

    public String getTargetUser() {
        return targetUser;
    }

    public void setTargetUser(String targetUser) {
        this.targetUser = targetUser;
    }

    public String getDelegateReason() {
        return delegateReason;
    }

    public void setDelegateReason(String delegateReason) {
        this.delegateReason = delegateReason;
    }

    // 别名方法，用于兼容WorkflowEngine
    public String getComment() {
        return getApprovalComment();
    }

    public String getResult() {
        return getApprovalResult();
    }
}
