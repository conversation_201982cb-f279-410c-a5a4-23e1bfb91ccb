package com.hzwangda.edu.workflow.repository;

import com.hzwangda.edu.workflow.entity.ProcessDefinitionInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 流程定义信息Repository接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface ProcessDefinitionInfoRepository extends JpaRepository<ProcessDefinitionInfo, Long>, JpaSpecificationExecutor<ProcessDefinitionInfo> {

    /**
     * 根据流程键查询流程定义
     */
    Optional<ProcessDefinitionInfo> findByProcessKeyAndDeletedFalse(String processKey);

    /**
     * 根据流程键查询激活的流程定义
     */
    @Query("SELECT p FROM ProcessDefinitionInfo p WHERE p.processKey = :processKey AND p.status = 'ACTIVE' AND p.deleted = false")
    Optional<ProcessDefinitionInfo> findByProcessKeyAndActiveTrue(@Param("processKey") String processKey);

    /**
     * 根据流程键和版本查询流程定义
     */
    Optional<ProcessDefinitionInfo> findByProcessKeyAndVersionAndDeletedFalse(String processKey, Integer version);

    /**
     * 根据流程键查询默认版本的流程定义
     */
    Optional<ProcessDefinitionInfo> findByProcessKeyAndIsDefaultTrueAndDeletedFalse(String processKey);

    /**
     * 根据流程分类查询流程定义
     */
    List<ProcessDefinitionInfo> findByProcessCategoryAndDeletedFalseOrderBySortOrderAsc(String processCategory);

    /**
     * 根据状态查询流程定义
     */
    Page<ProcessDefinitionInfo> findByStatusAndDeletedFalse(String status, Pageable pageable);

    /**
     * 根据Flowable流程定义ID查询
     */
    Optional<ProcessDefinitionInfo> findByFlowableDefinitionIdAndDeletedFalse(String flowableDefinitionId);

    /**
     * 查询所有激活的流程定义
     */
    @Query("SELECT p FROM ProcessDefinitionInfo p WHERE p.status = 'ACTIVE' AND p.deleted = false ORDER BY p.sortOrder ASC")
    List<ProcessDefinitionInfo> findAllActiveProcessDefinitions();

    /**
     * 查询指定分类下的激活流程定义
     */
    @Query("SELECT p FROM ProcessDefinitionInfo p WHERE p.processCategory = :category AND p.status = 'ACTIVE' " +
           "AND p.deleted = false ORDER BY p.sortOrder ASC")
    List<ProcessDefinitionInfo> findActiveProcessDefinitionsByCategory(@Param("category") String category);

    /**
     * 根据流程键查询所有版本
     */
    @Query("SELECT p FROM ProcessDefinitionInfo p WHERE p.processKey = :processKey AND p.deleted = false ORDER BY p.version DESC")
    List<ProcessDefinitionInfo> findAllVersionsByProcessKey(@Param("processKey") String processKey);

    /**
     * 查询最新版本号
     */
    @Query("SELECT MAX(p.version) FROM ProcessDefinitionInfo p WHERE p.processKey = :processKey AND p.deleted = false")
    Integer findMaxVersionByProcessKey(@Param("processKey") String processKey);

    /**
     * 检查流程键是否存在
     */
    boolean existsByProcessKeyAndDeletedFalse(String processKey);

    /**
     * 根据流程名称模糊查询
     */
    @Query("SELECT p FROM ProcessDefinitionInfo p WHERE p.processName LIKE %:processName% AND p.deleted = false")
    Page<ProcessDefinitionInfo> findByProcessNameContaining(@Param("processName") String processName, Pageable pageable);

    /**
     * 统计各分类下的流程定义数量
     */
    @Query("SELECT p.processCategory, COUNT(p) FROM ProcessDefinitionInfo p WHERE p.deleted = false GROUP BY p.processCategory")
    List<Object[]> countByProcessCategory();

    /**
     * 统计各状态下的流程定义数量
     */
    @Query("SELECT p.status, COUNT(p) FROM ProcessDefinitionInfo p WHERE p.deleted = false GROUP BY p.status")
    List<Object[]> countByStatus();

    /**
     * 查询需要更新默认版本的流程定义
     */
    @Query("SELECT p FROM ProcessDefinitionInfo p WHERE p.processKey = :processKey AND p.isDefault = true AND p.deleted = false")
    List<ProcessDefinitionInfo> findDefaultVersionsByProcessKey(@Param("processKey") String processKey);

    /**
     * 批量更新默认版本标记
     */
    @Query("UPDATE ProcessDefinitionInfo p SET p.isDefault = false WHERE p.processKey = :processKey AND p.deleted = false")
    void clearDefaultVersionByProcessKey(@Param("processKey") String processKey);
}
