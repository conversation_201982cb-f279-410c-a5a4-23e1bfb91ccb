package com.hzwangda.edu.workflow.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hzwangda.edu.workflow.dto.ProcessInstanceResponse;
import com.hzwangda.edu.workflow.dto.TaskResponse;
import com.hzwangda.edu.workflow.entity.ProcessInstanceInfo;
import com.hzwangda.edu.workflow.entity.TaskInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 工作流映射工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class WorkflowMapper {

    private static final Logger logger = LoggerFactory.getLogger(WorkflowMapper.class);

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 流程实例实体转响应DTO
     */
    public ProcessInstanceResponse toProcessInstanceResponse(ProcessInstanceInfo instanceInfo) {
        if (instanceInfo == null) {
            return null;
        }

        ProcessInstanceResponse response = new ProcessInstanceResponse();

        // 基本信息
        response.setId(instanceInfo.getId());
        response.setFlowableInstanceId(instanceInfo.getFlowableInstanceId());
        response.setProcessKey(instanceInfo.getProcessKey());
        response.setProcessName(instanceInfo.getProcessName());
        response.setBusinessKey(instanceInfo.getBusinessKey());
        response.setBusinessType(instanceInfo.getBusinessType());
        response.setBusinessId(instanceInfo.getBusinessId());

        // 发起人信息
        response.setInitiatorId(instanceInfo.getInitiatorId());
        response.setInitiatorUsername(instanceInfo.getInitiatorUsername());
        response.setInitiatorName(instanceInfo.getInitiatorName());

        // 时间信息
        response.setStartTime(instanceInfo.getStartTime());
        response.setEndTime(instanceInfo.getEndTime());

        // 状态信息
        response.setStatus(instanceInfo.getStatus());
        response.setCurrentActivity(instanceInfo.getCurrentActivity());
        response.setCurrentActivityName(instanceInfo.getCurrentActivityName());

        // 其他信息
        response.setTitle(instanceInfo.getTitle());
        response.setDescription(instanceInfo.getDescription());
        response.setPriority(instanceInfo.getPriority());
        response.setDuration(instanceInfo.getDuration());
        response.setEndReason(instanceInfo.getEndReason());

        // 审计信息
        response.setCreateTime(instanceInfo.getCreateTime());
        response.setUpdateTime(instanceInfo.getUpdateTime());

        // 反序列化变量
        if (instanceInfo.getVariables() != null) {
            try {
                Map<String, Object> variables = objectMapper.readValue(
                    instanceInfo.getVariables(),
                    new TypeReference<Map<String, Object>>() {}
                );
                response.setVariables(variables);
            } catch (JsonProcessingException e) {
                logger.warn("反序列化流程变量失败: {}", e.getMessage());
                response.setVariables(new HashMap<>());
            }
        }

        return response;
    }

    /**
     * 任务实体转响应DTO
     */
    public TaskResponse toTaskResponse(TaskInfo taskInfo) {
        if (taskInfo == null) {
            return null;
        }

        TaskResponse response = new TaskResponse();

        // 基本信息
        response.setId(taskInfo.getId());
        response.setFlowableTaskId(taskInfo.getFlowableTaskId());
        response.setProcessInstanceId(taskInfo.getProcessInstanceId());
        response.setProcessKey(taskInfo.getProcessKey());
        response.setTaskKey(taskInfo.getTaskKey());
        response.setTaskName(taskInfo.getTaskName());
        response.setTaskDescription(taskInfo.getTaskDescription());

        // 指派人信息
        response.setAssigneeId(taskInfo.getAssigneeId());
        response.setAssigneeUsername(taskInfo.getAssigneeUsername());
        response.setAssigneeName(taskInfo.getAssigneeName());
        response.setCandidateGroups(taskInfo.getCandidateGroups());
        response.setCandidateUsers(taskInfo.getCandidateUsers());

        // 时间信息
        response.setTaskCreateTime(taskInfo.getTaskCreateTime());
        response.setClaimTime(taskInfo.getClaimTime());
        response.setCompleteTime(taskInfo.getCompleteTime());
        response.setDueDate(taskInfo.getDueDate());

        // 状态信息
        response.setStatus(taskInfo.getStatus());
        response.setPriority(taskInfo.getPriority());
        response.setDuration(taskInfo.getDuration());

        // 审批信息
        response.setApprovalResult(taskInfo.getApprovalResult());
        response.setApprovalComment(taskInfo.getApprovalComment());

        // 业务信息
        response.setBusinessKey(taskInfo.getBusinessKey());
        response.setBusinessType(taskInfo.getBusinessType());

        // 审计信息
        response.setCreateTime(taskInfo.getCreateTime());
        response.setUpdateTime(taskInfo.getUpdateTime());

        // 反序列化表单数据
        if (taskInfo.getFormData() != null) {
            try {
                Map<String, Object> formData = objectMapper.readValue(
                    taskInfo.getFormData(),
                    new TypeReference<Map<String, Object>>() {}
                );
                response.setFormData(formData);
            } catch (JsonProcessingException e) {
                logger.warn("反序列化表单数据失败: {}", e.getMessage());
                response.setFormData(new HashMap<>());
            }
        }

        // 反序列化任务变量
        if (taskInfo.getVariables() != null) {
            try {
                Map<String, Object> variables = objectMapper.readValue(
                    taskInfo.getVariables(),
                    new TypeReference<Map<String, Object>>() {}
                );
                response.setVariables(variables);
            } catch (JsonProcessingException e) {
                logger.warn("反序列化任务变量失败: {}", e.getMessage());
                response.setVariables(new HashMap<>());
            }
        }

        return response;
    }
}
