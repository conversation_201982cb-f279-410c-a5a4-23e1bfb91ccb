package com.hzwangda.edu.workflow.entity;

import com.hzwangda.edu.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

/**
 * 流程定义信息实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@Entity
@Table(name = "wf_process_definition_info", indexes = {
    @Index(name = "idx_process_key", columnList = "process_key"),
    @Index(name = "idx_process_category", columnList = "process_category"),
    @Index(name = "idx_process_status", columnList = "status")
})
@Schema(description = "流程定义信息")
public class ProcessDefinitionInfo extends BaseEntity {

    @NotBlank(message = "流程键不能为空")
    @Size(max = 64, message = "流程键长度不能超过64个字符")
    @Column(name = "process_key", length = 64, nullable = false, unique = true)
    @Schema(description = "流程键", example = "leave_approval")
    private String processKey;

    @NotBlank(message = "流程名称不能为空")
    @Size(max = 128, message = "流程名称长度不能超过128个字符")
    @Column(name = "process_name", length = 128, nullable = false)
    @Schema(description = "流程名称", example = "请假审批流程")
    private String processName;

    @Size(max = 64, message = "流程分类长度不能超过64个字符")
    @Column(name = "process_category", length = 64)
    @Schema(description = "流程分类", example = "ATTENDANCE")
    private String processCategory;

    @Size(max = 500, message = "流程描述长度不能超过500个字符")
    @Column(name = "description", length = 500)
    @Schema(description = "流程描述", example = "教职工请假审批流程")
    private String description;

    @Column(name = "version", nullable = false)
    @Schema(description = "版本号", example = "1")
    private Integer version = 1;

    @Size(max = 20, message = "状态长度不能超过20个字符")
    @Column(name = "status", length = 20, nullable = false)
    @Schema(description = "状态", example = "ACTIVE")
    private String status = "ACTIVE";

    @Column(name = "flowable_definition_id", length = 64)
    @Schema(description = "Flowable流程定义ID")
    private String flowableDefinitionId;

    @Column(name = "flowable_deployment_id", length = 64)
    @Schema(description = "Flowable部署ID")
    private String flowableDeploymentId;

    @Column(name = "bpmn_xml", columnDefinition = "TEXT")
    @Schema(description = "BPMN XML内容")
    private String bpmnXml;

    @Column(name = "form_config", columnDefinition = "TEXT")
    @Schema(description = "表单配置JSON")
    private String formConfig;

    @Column(name = "approval_config", columnDefinition = "TEXT")
    @Schema(description = "审批配置JSON")
    private String approvalConfig;

    @Column(name = "is_default", nullable = false)
    @Schema(description = "是否默认版本", example = "true")
    private Boolean isDefault = false;

    @Column(name = "sort_order")
    @Schema(description = "排序顺序", example = "1")
    private Integer sortOrder = 0;

    // 构造函数
    public ProcessDefinitionInfo() {
    }

    public ProcessDefinitionInfo(String processKey, String processName, String processCategory) {
        this.processKey = processKey;
        this.processName = processName;
        this.processCategory = processCategory;
    }

}
