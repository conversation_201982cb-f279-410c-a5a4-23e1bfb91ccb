package com.hzwangda.edu.workflow.service;

import java.util.List;
import java.util.Map;

/**
 * 流程监控服务接口
 * 提供流程实例监控、统计分析、性能分析、告警管理等功能
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public interface ProcessMonitoringService {

    // ==================== 实时监控功能 ====================

    /**
     * 获取实时监控面板数据
     *
     * @param processDefinitionKey 流程定义Key，为空时显示所有流程
     * @param timeRange 时间范围（小时）
     * @return 监控面板数据
     */
    Map<String, Object> getMonitoringDashboard(String processDefinitionKey, int timeRange);

    /**
     * 获取活跃流程实例列表
     *
     * @param processDefinitionKey 流程定义Key
     * @param page 页码
     * @param size 页大小
     * @return 活跃流程实例列表
     */
    List<Map<String, Object>> getActiveProcessInstances(String processDefinitionKey, int page, int size);

    /**
     * 获取流程实例详细状态
     *
     * @param processInstanceId 流程实例ID
     * @return 流程实例状态信息
     */
    Map<String, Object> getProcessInstanceStatus(String processInstanceId);

    // ==================== 统计分析功能 ====================

    /**
     * 获取流程执行统计
     *
     * @param processDefinitionKey 流程定义Key
     * @param days 统计时间范围（天）
     * @return 流程执行统计信息
     */
    Map<String, Object> getProcessStatistics(String processDefinitionKey, int days);

    /**
     * 获取流程趋势分析
     *
     * @param processDefinitionKey 流程定义Key
     * @param days 统计时间范围（天）
     * @param granularity 数据粒度：hour/day/week
     * @return 流程趋势分析数据
     */
    Map<String, Object> getProcessTrends(String processDefinitionKey, int days, String granularity);

    /**
     * 获取节点执行统计
     *
     * @param processDefinitionKey 流程定义Key
     * @param days 统计时间范围（天）
     * @return 节点执行统计信息
     */
    Map<String, Object> getNodeStatistics(String processDefinitionKey, int days);

    // ==================== 性能分析功能 ====================

    /**
     * 获取流程性能分析
     *
     * @param processDefinitionKey 流程定义Key
     * @param days 统计时间范围（天）
     * @return 流程性能分析报告
     */
    Map<String, Object> getProcessPerformance(String processDefinitionKey, int days);

    /**
     * 获取瓶颈分析
     *
     * @param processDefinitionKey 流程定义Key
     * @param days 统计时间范围（天）
     * @return 瓶颈分析结果
     */
    List<Map<String, Object>> getProcessBottlenecks(String processDefinitionKey, int days);

    // ==================== 告警管理功能 ====================

    /**
     * 获取流程告警列表
     *
     * @param level 告警级别
     * @param status 告警状态
     * @param page 页码
     * @param size 页大小
     * @return 告警信息列表
     */
    List<Map<String, Object>> getProcessAlerts(String level, String status, int page, int size);

    /**
     * 创建告警规则
     *
     * @param alertRuleConfig 告警规则配置
     * @return 创建结果
     */
    Map<String, Object> createAlertRule(Map<String, Object> alertRuleConfig);

    /**
     * 更新告警规则
     *
     * @param ruleId 告警规则ID
     * @param alertRuleConfig 告警规则配置
     * @return 更新结果
     */
    Map<String, Object> updateAlertRule(String ruleId, Map<String, Object> alertRuleConfig);

    /**
     * 删除告警规则
     *
     * @param ruleId 告警规则ID
     * @return 是否删除成功
     */
    boolean deleteAlertRule(String ruleId);

    // ==================== 系统健康检查 ====================

    /**
     * 获取系统健康状态
     *
     * @return 系统健康状态
     */
    Map<String, Object> getSystemHealth();

    /**
     * 获取系统指标
     *
     * @return 系统指标
     */
    Map<String, Object> getSystemMetrics();
}
