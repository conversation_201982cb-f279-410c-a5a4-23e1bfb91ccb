package com.hzwangda.edu.workflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 任务响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "任务响应")
public class TaskResponse {

    @Schema(description = "任务ID", example = "1")
    private Long id;

    @Schema(description = "Flowable任务ID")
    private String flowableTaskId;

    @Schema(description = "流程实例ID")
    private String processInstanceId;

    @Schema(description = "流程键", example = "leave_approval")
    private String processKey;

    @Schema(description = "任务键", example = "manager_approval")
    private String taskKey;

    @Schema(description = "任务名称", example = "部门经理审批")
    private String taskName;

    @Schema(description = "任务描述")
    private String taskDescription;

    @Schema(description = "指派人ID", example = "1")
    private Long assigneeId;

    @Schema(description = "指派人用户名", example = "manager")
    private String assigneeUsername;

    @Schema(description = "指派人姓名", example = "李经理")
    private String assigneeName;

    @Schema(description = "候选组", example = "dept_manager")
    private String candidateGroups;

    @Schema(description = "候选用户", example = "user1,user2,user3")
    private String candidateUsers;

    @Schema(description = "任务创建时间")
    private LocalDateTime taskCreateTime;

    @Schema(description = "认领时间")
    private LocalDateTime claimTime;

    @Schema(description = "完成时间")
    private LocalDateTime completeTime;

    @Schema(description = "到期时间")
    private LocalDateTime dueDate;

    @Schema(description = "状态", example = "PENDING")
    private String status;

    @Schema(description = "优先级", example = "50")
    private Integer priority;

    @Schema(description = "持续时间(毫秒)")
    private Long duration;

    @Schema(description = "审批结果", example = "APPROVED")
    private String approvalResult;

    @Schema(description = "审批意见")
    private String approvalComment;

    @Schema(description = "表单数据")
    private Map<String, Object> formData;

    @Schema(description = "任务变量")
    private Map<String, Object> variables;

    @Schema(description = "业务键", example = "LEAVE_20241219_001")
    private String businessKey;

    @Schema(description = "业务类型", example = "LEAVE_APPLICATION")
    private String businessType;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    // 构造函数
    public TaskResponse() {
    }

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFlowableTaskId() {
        return flowableTaskId;
    }

    public void setFlowableTaskId(String flowableTaskId) {
        this.flowableTaskId = flowableTaskId;
    }

    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    public String getProcessKey() {
        return processKey;
    }

    public void setProcessKey(String processKey) {
        this.processKey = processKey;
    }

    public String getTaskKey() {
        return taskKey;
    }

    public void setTaskKey(String taskKey) {
        this.taskKey = taskKey;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskDescription() {
        return taskDescription;
    }

    public void setTaskDescription(String taskDescription) {
        this.taskDescription = taskDescription;
    }

    public Long getAssigneeId() {
        return assigneeId;
    }

    public void setAssigneeId(Long assigneeId) {
        this.assigneeId = assigneeId;
    }

    public String getAssigneeUsername() {
        return assigneeUsername;
    }

    public void setAssigneeUsername(String assigneeUsername) {
        this.assigneeUsername = assigneeUsername;
    }

    public String getAssigneeName() {
        return assigneeName;
    }

    public void setAssigneeName(String assigneeName) {
        this.assigneeName = assigneeName;
    }

    public String getCandidateGroups() {
        return candidateGroups;
    }

    public void setCandidateGroups(String candidateGroups) {
        this.candidateGroups = candidateGroups;
    }

    public String getCandidateUsers() {
        return candidateUsers;
    }

    public void setCandidateUsers(String candidateUsers) {
        this.candidateUsers = candidateUsers;
    }

    public LocalDateTime getTaskCreateTime() {
        return taskCreateTime;
    }

    public void setTaskCreateTime(LocalDateTime taskCreateTime) {
        this.taskCreateTime = taskCreateTime;
    }

    public LocalDateTime getClaimTime() {
        return claimTime;
    }

    public void setClaimTime(LocalDateTime claimTime) {
        this.claimTime = claimTime;
    }

    public LocalDateTime getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(LocalDateTime completeTime) {
        this.completeTime = completeTime;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public String getApprovalResult() {
        return approvalResult;
    }

    public void setApprovalResult(String approvalResult) {
        this.approvalResult = approvalResult;
    }

    public String getApprovalComment() {
        return approvalComment;
    }

    public void setApprovalComment(String approvalComment) {
        this.approvalComment = approvalComment;
    }

    public Map<String, Object> getFormData() {
        return formData;
    }

    public void setFormData(Map<String, Object> formData) {
        this.formData = formData;
    }

    public Map<String, Object> getVariables() {
        return variables;
    }

    public void setVariables(Map<String, Object> variables) {
        this.variables = variables;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
