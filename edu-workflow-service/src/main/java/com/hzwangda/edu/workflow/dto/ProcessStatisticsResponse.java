package com.hzwangda.edu.workflow.dto;

import com.hzwangda.edu.workflow.enums.ProcessStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 流程统计响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "流程统计响应")
public class ProcessStatisticsResponse {

    @Schema(description = "统计时间范围")
    private TimeRange timeRange;

    @Schema(description = "总体统计")
    private OverallStatistics overall;

    @Schema(description = "按状态统计")
    private Map<ProcessStatus, Long> statusStatistics;

    @Schema(description = "按流程类型统计")
    private Map<String, Long> processKeyStatistics;

    @Schema(description = "按部门统计")
    private Map<String, Long> departmentStatistics;

    @Schema(description = "按发起人统计")
    private Map<String, Long> initiatorStatistics;

    @Schema(description = "时间趋势统计")
    private List<TrendData> trendStatistics;

    @Schema(description = "效率统计")
    private EfficiencyStatistics efficiency;

    @Schema(description = "待办任务数量")
    private Long pendingTaskCount;

    @Schema(description = "平均流程处理时长(毫秒)")
    private Double averageProcessDuration;

    @Schema(description = "详细数据")
    private List<ProcessDetailData> details;

    @Schema(description = "扩展数据")
    private Object extData;

    /**
     * 时间范围
     */
    @Data
    @Schema(description = "时间范围")
    public static class TimeRange {
        @Schema(description = "开始时间")
        private LocalDateTime startTime;

        @Schema(description = "结束时间")
        private LocalDateTime endTime;

        @Schema(description = "统计维度")
        private String dimension;
    }

    /**
     * 总体统计
     */
    @Data
    @Schema(description = "总体统计")
    public static class OverallStatistics {
        @Schema(description = "总流程数")
        private Long totalCount;

        @Schema(description = "运行中流程数")
        private Long runningCount;

        @Schema(description = "已完成流程数")
        private Long completedCount;

        @Schema(description = "已取消流程数")
        private Long cancelledCount;

        @Schema(description = "完成率")
        private Double completionRate;

        @Schema(description = "平均处理时长")
        private Double avgDuration;
    }

    /**
     * 趋势数据
     */
    @Data
    @Schema(description = "趋势数据")
    public static class TrendData {
        @Schema(description = "时间点")
        private String timePoint;

        @Schema(description = "数量")
        private Long count;

        @Schema(description = "完成数量")
        private Long completedCount;

        @Schema(description = "平均时长")
        private Double avgDuration;
    }

    /**
     * 效率统计
     */
    @Data
    @Schema(description = "效率统计")
    public static class EfficiencyStatistics {
        @Schema(description = "最快处理时长")
        private Long minDuration;

        @Schema(description = "最慢处理时长")
        private Long maxDuration;

        @Schema(description = "平均处理时长")
        private Double avgDuration;

        @Schema(description = "中位数处理时长")
        private Double medianDuration;

        @Schema(description = "超时流程数")
        private Long timeoutCount;

        @Schema(description = "超时率")
        private Double timeoutRate;
    }

    /**
     * 流程详细数据
     */
    @Data
    @Schema(description = "流程详细数据")
    public static class ProcessDetailData {
        @Schema(description = "流程实例ID")
        private String processInstanceId;

        @Schema(description = "流程名称")
        private String processName;

        @Schema(description = "业务键")
        private String businessKey;

        @Schema(description = "发起人")
        private String initiator;

        @Schema(description = "状态")
        private String status;

        @Schema(description = "开始时间")
        private LocalDateTime startTime;

        @Schema(description = "结束时间")
        private LocalDateTime endTime;

        @Schema(description = "持续时间")
        private Long duration;
    }
}
