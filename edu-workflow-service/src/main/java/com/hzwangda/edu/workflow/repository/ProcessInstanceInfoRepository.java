package com.hzwangda.edu.workflow.repository;

import com.hzwangda.edu.workflow.entity.ProcessInstanceInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 流程实例信息Repository接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface ProcessInstanceInfoRepository extends JpaRepository<ProcessInstanceInfo, Long>, JpaSpecificationExecutor<ProcessInstanceInfo> {

    /**
     * 根据Flowable流程实例ID查询
     */
    Optional<ProcessInstanceInfo> findByFlowableInstanceIdAndDeletedFalse(String flowableInstanceId);

    /**
     * 根据业务键查询流程实例
     */
    Optional<ProcessInstanceInfo> findByBusinessKeyAndDeletedFalse(String businessKey);

    /**
     * 根据业务类型和业务ID查询流程实例
     */
    List<ProcessInstanceInfo> findByBusinessTypeAndBusinessIdAndDeletedFalse(String businessType, String businessId);

    /**
     * 根据发起人查询流程实例
     */
    Page<ProcessInstanceInfo> findByInitiatorIdAndDeletedFalse(Long initiatorId, Pageable pageable);

    /**
     * 根据流程键查询流程实例
     */
    Page<ProcessInstanceInfo> findByProcessKeyAndDeletedFalse(String processKey, Pageable pageable);

    /**
     * 根据状态查询流程实例
     */
    Page<ProcessInstanceInfo> findByStatusAndDeletedFalse(String status, Pageable pageable);

    /**
     * 根据时间范围查询流程实例
     */
    Page<ProcessInstanceInfo> findByStartTimeBetweenAndDeletedFalse(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 查询运行中的流程实例
     */
    @Query("SELECT p FROM ProcessInstanceInfo p WHERE p.status = 'RUNNING' AND p.deleted = false")
    List<ProcessInstanceInfo> findRunningProcessInstances();

    /**
     * 查询指定用户发起的运行中流程实例
     */
    @Query("SELECT p FROM ProcessInstanceInfo p WHERE p.initiatorId = :initiatorId AND p.status = 'RUNNING' AND p.deleted = false")
    List<ProcessInstanceInfo> findRunningProcessInstancesByInitiator(@Param("initiatorId") Long initiatorId);

    /**
     * 查询超时的流程实例
     */
    @Query("SELECT p FROM ProcessInstanceInfo p WHERE p.status = 'RUNNING' AND p.startTime < :timeoutTime AND p.deleted = false")
    List<ProcessInstanceInfo> findTimeoutProcessInstances(@Param("timeoutTime") LocalDateTime timeoutTime);

    /**
     * 统计各状态下的流程实例数量
     */
    @Query("SELECT p.status, COUNT(p) FROM ProcessInstanceInfo p WHERE p.deleted = false GROUP BY p.status")
    List<Object[]> countByStatus();

    /**
     * 统计各流程键下的流程实例数量
     */
    @Query("SELECT p.processKey, COUNT(p) FROM ProcessInstanceInfo p WHERE p.deleted = false GROUP BY p.processKey")
    List<Object[]> countByProcessKey();

    /**
     * 统计指定时间范围内的流程实例数量
     */
    @Query("SELECT COUNT(p) FROM ProcessInstanceInfo p WHERE p.startTime BETWEEN :startTime AND :endTime AND p.deleted = false")
    Long countByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内已完成的流程实例数量
     */
    @Query("SELECT COUNT(p) FROM ProcessInstanceInfo p WHERE p.endTime BETWEEN :startTime AND :endTime " +
           "AND p.status = 'COMPLETED' AND p.deleted = false")
    Long countCompletedByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询平均处理时长
     */
    @Query("SELECT AVG(p.duration) FROM ProcessInstanceInfo p WHERE p.status = 'COMPLETED' AND p.duration IS NOT NULL AND p.deleted = false")
    Double findAverageProcessDuration();

    /**
     * 查询指定流程键的平均处理时长
     */
    @Query("SELECT AVG(p.duration) FROM ProcessInstanceInfo p WHERE p.processKey = :processKey AND p.status = 'COMPLETED' " +
           "AND p.duration IS NOT NULL AND p.deleted = false")
    Double findAverageProcessDurationByKey(@Param("processKey") String processKey);

    /**
     * 查询最活跃的发起人TOP10
     */
    @Query("SELECT p.initiatorId, p.initiatorUsername, p.initiatorName, COUNT(p) as instanceCount " +
           "FROM ProcessInstanceInfo p WHERE p.startTime BETWEEN :startTime AND :endTime AND p.deleted = false " +
           "GROUP BY p.initiatorId, p.initiatorUsername, p.initiatorName ORDER BY instanceCount DESC")
    List<Object[]> findTopInitiators(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, Pageable pageable);

    /**
     * 按日期统计流程实例数量
     */
    @Query("SELECT DATE(p.startTime), COUNT(p) FROM ProcessInstanceInfo p WHERE p.startTime BETWEEN :startTime AND :endTime " +
           "AND p.deleted = false GROUP BY DATE(p.startTime) ORDER BY DATE(p.startTime)")
    List<Object[]> countByDate(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 按小时统计流程实例数量
     */
    @Query("SELECT HOUR(p.startTime), COUNT(p) FROM ProcessInstanceInfo p WHERE p.startTime BETWEEN :startTime AND :endTime " +
           "AND p.deleted = false GROUP BY HOUR(p.startTime) ORDER BY HOUR(p.startTime)")
    List<Object[]> countByHour(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定业务类型的流程实例
     */
    @Query("SELECT p FROM ProcessInstanceInfo p WHERE p.businessType = :businessType AND p.deleted = false ORDER BY p.startTime DESC")
    List<ProcessInstanceInfo> findByBusinessType(@Param("businessType") String businessType);

    /**
     * 检查业务键是否已存在
     */
    boolean existsByBusinessKeyAndDeletedFalse(String businessKey);
}
