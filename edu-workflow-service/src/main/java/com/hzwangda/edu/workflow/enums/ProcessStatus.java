package com.hzwangda.edu.workflow.enums;

/**
 * 流程状态枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum ProcessStatus {

    // 流程定义状态
    ACTIVE("激活", "流程定义处于激活状态，可以启动新实例"),
    SUSPENDED("挂起", "流程定义被挂起，不能启动新实例"),
    INACTIVE("停用", "流程定义已停用"),

    // 流程实例状态
    RUNNING("运行中", "流程实例正在运行"),
    COMPLETED("已完成", "流程实例已正常完成"),
    TERMINATED("已终止", "流程实例被强制终止"),
    CANCELLED("已取消", "流程实例被取消"),

    // 任务状态
    PENDING("待处理", "任务等待处理"),
    CLAIMED("已认领", "任务已被认领"),
    IN_PROGRESS("处理中", "任务正在处理"),
    APPROVED("已通过", "任务审批通过"),
    REJECTED("已拒绝", "任务审批拒绝"),
    DELEGATED("已委派", "任务已委派给他人"),
    TRANSFERRED("已转办", "任务已转办给他人"),
    RETURNED("已退回", "任务已退回到上一节点"),
    JUMPED("已跳转", "任务已跳转到指定节点");

    private final String description;
    private final String detail;

    ProcessStatus(String description, String detail) {
        this.description = description;
        this.detail = detail;
    }

    public String getDescription() {
        return description;
    }

    public String getDetail() {
        return detail;
    }

    public String getCode() {
        return this.name();
    }

    /**
     * 根据代码获取枚举
     */
    public static ProcessStatus fromCode(String code) {
        for (ProcessStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return PENDING;
    }

    /**
     * 判断是否为流程定义状态
     */
    public boolean isDefinitionStatus() {
        return this == ACTIVE || this == SUSPENDED || this == INACTIVE;
    }

    /**
     * 判断是否为流程实例状态
     */
    public boolean isInstanceStatus() {
        return this == RUNNING || this == COMPLETED || this == TERMINATED || this == CANCELLED;
    }

    /**
     * 判断是否为任务状态
     */
    public boolean isTaskStatus() {
        return this == PENDING || this == CLAIMED || this == IN_PROGRESS ||
               this == APPROVED || this == REJECTED || this == DELEGATED ||
               this == TRANSFERRED || this == RETURNED || this == JUMPED;
    }

    /**
     * 判断是否为终态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == TERMINATED || this == CANCELLED ||
               this == APPROVED || this == REJECTED;
    }

    /**
     * 判断是否为可操作状态（可以进行退回、跳转等操作）
     */
    public boolean isOperableStatus() {
        return this == PENDING || this == CLAIMED || this == IN_PROGRESS;
    }
}
