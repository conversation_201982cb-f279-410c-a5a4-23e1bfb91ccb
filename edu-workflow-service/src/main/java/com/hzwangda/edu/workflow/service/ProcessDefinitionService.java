package com.hzwangda.edu.workflow.service;

import com.hzwangda.edu.common.dto.PageResult;
import com.hzwangda.edu.workflow.dto.ProcessDefinitionDTO;
import com.hzwangda.edu.workflow.dto.ProcessDefinitionQueryRequest;
import com.hzwangda.edu.workflow.dto.ProcessDeploymentRequest;
import com.hzwangda.edu.workflow.dto.ProcessDefinitionMetadataUpdateRequest;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 流程定义服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public interface ProcessDefinitionService {

    /**
     * 部署流程定义
     *
     * @param file BPMN文件
     * @param request 部署请求
     * @return 流程定义DTO
     */
    ProcessDefinitionDTO deployProcess(MultipartFile file, ProcessDeploymentRequest request);

    /**
     * 部署流程定义（通过输入流）
     *
     * @param inputStream BPMN文件输入流
     * @param fileName 文件名
     * @param request 部署请求
     * @return 流程定义DTO
     */
    ProcessDefinitionDTO deployProcess(InputStream inputStream, String fileName, ProcessDeploymentRequest request);

    /**
     * 分页查询流程定义
     *
     * @param request 查询请求
     * @return 分页结果
     */
    PageResult<ProcessDefinitionDTO> queryProcessDefinitions(ProcessDefinitionQueryRequest request);

    /**
     * 根据ID查询流程定义
     *
     * @param id 流程定义ID
     * @return 流程定义DTO
     */
    ProcessDefinitionDTO getProcessDefinitionById(String id);

    /**
     * 根据Key查询最新版本的流程定义
     *
     * @param key 流程定义Key
     * @return 流程定义DTO
     */
    ProcessDefinitionDTO getLatestProcessDefinitionByKey(String key);

    /**
     * 删除流程定义
     *
     * @param deploymentId 部署ID
     * @param cascade 是否级联删除
     * @return 是否删除成功
     */
    boolean deleteProcessDefinition(String deploymentId, boolean cascade);

    /**
     * 激活流程定义
     *
     * @param id 流程定义ID
     * @return 是否激活成功
     */
    boolean activateProcessDefinition(String id);

    /**
     * 挂起流程定义
     *
     * @param id 流程定义ID
     * @return 是否挂起成功
     */
    boolean suspendProcessDefinition(String id);

    /**
     * 获取流程定义XML
     *
     * @param id 流程定义ID
     * @return XML内容
     */
    String getProcessDefinitionXml(String id);

    /**
     * 生成流程图
     *
     * @param id 流程定义ID
     * @return 流程图字节数组
     */
    byte[] generateProcessDiagram(String id);

    /**
     * 获取流程定义统计信息
     *
     * @param id 流程定义ID
     * @return 统计信息
     */
    Map<String, Object> getProcessDefinitionStatistics(String id);

    /**
     * 获取所有流程分类
     *
     * @return 分类列表
     */
    List<String> getAllCategories();

    /**
     * 检查流程定义是否存在
     *
     * @param key 流程定义Key
     * @return 是否存在
     */
    boolean existsByKey(String key);

    /**
     * 获取流程定义的启动表单
     *
     * @param id 流程定义ID
     * @return 启动表单
     */
    Object getStartForm(String id);

    // ==================== 版本管理功能 ====================

    /**
     * 获取流程定义的所有版本
     *
     * @param key 流程定义Key
     * @return 版本列表
     */
    List<ProcessDefinitionDTO> getProcessDefinitionVersions(String key);

    /**
     * 设置默认版本
     *
     * @param id 流程定义ID
     * @return 是否设置成功
     */
    boolean setDefaultVersion(String id);

    /**
     * 版本比较
     *
     * @param id1 流程定义ID1
     * @param id2 流程定义ID2
     * @return 比较结果
     */
    Map<String, Object> compareVersions(String id1, String id2);

    /**
     * 版本回滚
     *
     * @param id 流程定义ID
     * @param reason 回滚原因
     * @return 回滚后的流程定义
     */
    ProcessDefinitionDTO rollbackVersion(String id, String reason);

    // ==================== 元数据管理功能 ====================

    /**
     * 更新流程定义元数据
     *
     * @param id 流程定义ID
     * @param request 更新请求
     * @return 更新后的流程定义
     */
    ProcessDefinitionDTO updateMetadata(String id, ProcessDefinitionMetadataUpdateRequest request);

    /**
     * 更新流程分类
     *
     * @param id 流程定义ID
     * @param category 新分类
     * @return 是否更新成功
     */
    boolean updateCategory(String id, String category);

    /**
     * 更新流程描述
     *
     * @param id 流程定义ID
     * @param description 新描述
     * @return 是否更新成功
     */
    boolean updateDescription(String id, String description);

    // ==================== 导入导出功能 ====================

    /**
     * 批量导出流程定义
     *
     * @param ids 流程定义ID列表
     * @return ZIP文件字节数组
     */
    byte[] exportProcessDefinitions(List<String> ids);

    /**
     * 批量导入流程定义
     *
     * @param file ZIP文件
     * @param overwrite 是否覆盖已存在的流程定义
     * @return 导入结果
     */
    Map<String, Object> importProcessDefinitions(MultipartFile file, boolean overwrite);

    /**
     * 验证BPMN文件
     *
     * @param file BPMN文件
     * @return 验证结果
     */
    Map<String, Object> validateBpmnFile(MultipartFile file);

    /**
     * 获取流程定义模板
     *
     * @param templateType 模板类型
     * @return 模板文件字节数组
     */
    byte[] getProcessDefinitionTemplate(String templateType);

    // ==================== 流程图增强功能 ====================

    /**
     * 生成流程图预览
     *
     * @param id 流程定义ID
     * @param format 图片格式（png, svg, jpg）
     * @param width 图片宽度
     * @param height 图片高度
     * @return 流程图字节数组
     */
    byte[] generateProcessDiagramPreview(String id, String format, int width, int height);

    /**
     * 生成带高亮节点的流程图
     *
     * @param id 流程定义ID
     * @param highlightNodes 高亮节点ID列表
     * @param highlightFlows 高亮连接线ID列表
     * @param format 图片格式
     * @return 流程图字节数组
     */
    byte[] generateProcessDiagramWithHighlight(String id, List<String> highlightNodes,
                                             List<String> highlightFlows, String format);

    /**
     * 获取流程图元数据
     *
     * @param id 流程定义ID
     * @return 流程图元数据（节点、连接线等信息）
     */
    Map<String, Object> getProcessDiagramMetadata(String id);
}
