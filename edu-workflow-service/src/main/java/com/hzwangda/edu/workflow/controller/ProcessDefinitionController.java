package com.hzwangda.edu.workflow.controller;

import com.hzwangda.edu.common.dto.PageResult;
import com.hzwangda.edu.common.result.Result;
import com.hzwangda.edu.workflow.dto.ProcessDefinitionDTO;
import com.hzwangda.edu.workflow.dto.ProcessDefinitionMetadataUpdateRequest;
import com.hzwangda.edu.workflow.dto.ProcessDefinitionQueryRequest;
import com.hzwangda.edu.workflow.dto.ProcessDeploymentRequest;
import com.hzwangda.edu.workflow.service.ProcessDefinitionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * 流程定义控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/process-definitions")
@Tag(name = "流程定义管理", description = "流程定义的部署、查询、管理等操作")
@Validated
public class ProcessDefinitionController {

    @Autowired
    private ProcessDefinitionService processDefinitionService;

    @Operation(summary = "部署流程定义", description = "通过上传BPMN文件部署流程定义")
    @PostMapping("/deploy")
    @PreAuthorize("hasRole('ADMIN') or hasAuthority('WORKFLOW_DEPLOY')")
    @Transactional
    public Result<ProcessDefinitionDTO> deployProcess(
            @Parameter(description = "BPMN文件", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "部署名称", required = true)
            @RequestParam("name") @NotBlank(message = "部署名称不能为空") String name,
            @Parameter(description = "流程分类")
            @RequestParam(value = "category", required = false) String category,
            @Parameter(description = "租户ID")
            @RequestParam(value = "tenantId", required = false) String tenantId,
            @Parameter(description = "是否激活")
            @RequestParam(value = "activate", defaultValue = "true") Boolean activate,
            @Parameter(description = "描述")
            @RequestParam(value = "description", required = false) String description) {

        log.info("部署流程定义: name={}, category={}, tenantId={}", name, category, tenantId);

        ProcessDeploymentRequest request = ProcessDeploymentRequest.builder()
                .name(name)
                .category(category)
                .tenantId(tenantId)
                .activate(activate)
                .description(description)
                .build();

        ProcessDefinitionDTO result = processDefinitionService.deployProcess(file, request);
        return Result.success(result);
    }

    @Operation(summary = "分页查询流程定义", description = "根据条件分页查询流程定义列表")
    @PostMapping("/query")
    public Result<PageResult<ProcessDefinitionDTO>> queryProcessDefinitions(
            @Valid @RequestBody ProcessDefinitionQueryRequest request) {
        log.info("分页查询流程定义: {}", request);

        PageResult<ProcessDefinitionDTO> result = processDefinitionService.queryProcessDefinitions(request);
        return Result.success(result);
    }

    @Operation(summary = "根据ID查询流程定义", description = "根据流程定义ID查询详细信息")
    @GetMapping("/{id}")
    public Result<ProcessDefinitionDTO> getProcessDefinitionById(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String id) {
        log.info("根据ID查询流程定义: {}", id);

        ProcessDefinitionDTO result = processDefinitionService.getProcessDefinitionById(id);
        return Result.success(result);
    }

    @Operation(summary = "根据Key查询最新版本流程定义", description = "根据流程定义Key查询最新版本的流程定义")
    @GetMapping("/latest/{key}")
    public Result<ProcessDefinitionDTO> getLatestProcessDefinitionByKey(
            @Parameter(description = "流程定义Key", required = true)
            @PathVariable @NotBlank(message = "流程定义Key不能为空") String key) {
        log.info("根据Key查询最新版本流程定义: {}", key);

        ProcessDefinitionDTO result = processDefinitionService.getLatestProcessDefinitionByKey(key);
        return Result.success(result);
    }

    @Operation(summary = "删除流程定义", description = "根据部署ID删除流程定义")
    @DeleteMapping("/{deploymentId}")
    @PreAuthorize("hasRole('ADMIN') or hasAuthority('WORKFLOW_DELETE')")
    @Transactional
    public Result<Boolean> deleteProcessDefinition(
            @Parameter(description = "部署ID", required = true)
            @PathVariable @NotBlank(message = "部署ID不能为空") String deploymentId,
            @Parameter(description = "是否级联删除")
            @RequestParam(value = "cascade", defaultValue = "false") Boolean cascade) {
        log.info("删除流程定义: deploymentId={}, cascade={}", deploymentId, cascade);

        boolean result = processDefinitionService.deleteProcessDefinition(deploymentId, cascade);
        return Result.success(result);
    }

    @Operation(summary = "激活流程定义", description = "激活指定的流程定义")
    @PutMapping("/{id}/activate")
    public Result<Boolean> activateProcessDefinition(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String id) {
        log.info("激活流程定义: {}", id);

        boolean result = processDefinitionService.activateProcessDefinition(id);
        return Result.success(result);
    }

    @Operation(summary = "挂起流程定义", description = "挂起指定的流程定义")
    @PutMapping("/{id}/suspend")
    public Result<Boolean> suspendProcessDefinition(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String id) {
        log.info("挂起流程定义: {}", id);

        boolean result = processDefinitionService.suspendProcessDefinition(id);
        return Result.success(result);
    }

    @Operation(summary = "获取流程定义XML", description = "获取流程定义的BPMN XML内容")
    @GetMapping("/{id}/xml")
    public Result<String> getProcessDefinitionXml(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String id) {
        log.info("获取流程定义XML: {}", id);

        String xml = processDefinitionService.getProcessDefinitionXml(id);
        return Result.success(xml);
    }

    @Operation(summary = "生成流程图", description = "生成流程定义的流程图")
    @GetMapping("/{id}/diagram")
    public ResponseEntity<byte[]> generateProcessDiagram(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String id) {
        log.info("生成流程图: {}", id);

        byte[] diagram = processDefinitionService.generateProcessDiagram(id);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.IMAGE_PNG);
        headers.setContentLength(diagram.length);
        headers.set("Content-Disposition", "inline; filename=process-diagram-" + id + ".png");

        return ResponseEntity.ok()
                .headers(headers)
                .body(diagram);
    }

    @Operation(summary = "获取流程定义统计信息", description = "获取流程定义的统计信息")
    @GetMapping("/{id}/statistics")
    public Result<Map<String, Object>> getProcessDefinitionStatistics(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String id) {
        log.info("获取流程定义统计信息: {}", id);

        Map<String, Object> statistics = processDefinitionService.getProcessDefinitionStatistics(id);
        return Result.success(statistics);
    }

    @Operation(summary = "获取所有流程分类", description = "获取系统中所有的流程分类")
    @GetMapping("/categories")
    public Result<List<String>> getAllCategories() {
        log.info("获取所有流程分类");

        List<String> categories = processDefinitionService.getAllCategories();
        return Result.success(categories);
    }

    @Operation(summary = "检查流程定义是否存在", description = "根据Key检查流程定义是否存在")
    @GetMapping("/exists/{key}")
    public Result<Boolean> existsByKey(
            @Parameter(description = "流程定义Key", required = true)
            @PathVariable @NotBlank(message = "流程定义Key不能为空") String key) {
        log.info("检查流程定义是否存在: {}", key);

        boolean exists = processDefinitionService.existsByKey(key);
        return Result.success(exists);
    }

    @Operation(summary = "获取启动表单", description = "获取流程定义的启动表单")
    @GetMapping("/{id}/start-form")
    public Result<Object> getStartForm(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String id) {
        log.info("获取启动表单: {}", id);

        Object startForm = processDefinitionService.getStartForm(id);
        return Result.success(startForm);
    }

    // ==================== 版本管理功能 ====================

    @Operation(summary = "获取流程定义所有版本", description = "根据流程定义Key获取所有版本列表")
    @GetMapping("/{key}/versions")
    public Result<List<ProcessDefinitionDTO>> getProcessDefinitionVersions(
            @Parameter(description = "流程定义Key", required = true)
            @PathVariable @NotBlank(message = "流程定义Key不能为空") String key) {
        log.info("获取流程定义所有版本: {}", key);

        List<ProcessDefinitionDTO> versions = processDefinitionService.getProcessDefinitionVersions(key);
        return Result.success(versions);
    }

    @Operation(summary = "设置默认版本", description = "设置指定版本为默认版本")
    @PostMapping("/{id}/set-default")
    @PreAuthorize("hasRole('ADMIN') or hasAuthority('WORKFLOW_MANAGE')")
    @Transactional
    public Result<Boolean> setDefaultVersion(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String id) {
        log.info("设置默认版本: {}", id);

        boolean result = processDefinitionService.setDefaultVersion(id);
        return Result.success(result);
    }

    @Operation(summary = "版本比较", description = "比较两个版本的流程定义差异")
    @GetMapping("/{id1}/compare/{id2}")
    public Result<Map<String, Object>> compareVersions(
            @Parameter(description = "流程定义ID1", required = true)
            @PathVariable @NotBlank(message = "流程定义ID1不能为空") String id1,
            @Parameter(description = "流程定义ID2", required = true)
            @PathVariable @NotBlank(message = "流程定义ID2不能为空") String id2) {
        log.info("版本比较: {} vs {}", id1, id2);

        Map<String, Object> comparison = processDefinitionService.compareVersions(id1, id2);
        return Result.success(comparison);
    }

    @Operation(summary = "版本回滚", description = "回滚到指定版本")
    @PostMapping("/{id}/rollback")
    @PreAuthorize("hasRole('ADMIN') or hasAuthority('WORKFLOW_MANAGE')")
    @Transactional
    public Result<ProcessDefinitionDTO> rollbackVersion(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String id,
            @Parameter(description = "回滚原因")
            @RequestParam(value = "reason", required = false) String reason) {
        log.info("版本回滚: {}, 原因: {}", id, reason);

        ProcessDefinitionDTO result = processDefinitionService.rollbackVersion(id, reason);
        return Result.success(result);
    }

    // ==================== 元数据管理功能 ====================

    @Operation(summary = "更新流程定义元数据", description = "更新流程定义的名称、描述等元数据")
    @PutMapping("/{id}/metadata")
    public Result<ProcessDefinitionDTO> updateMetadata(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String id,
            @Parameter(description = "元数据更新请求", required = true)
            @RequestBody @Valid ProcessDefinitionMetadataUpdateRequest request) {
        log.info("更新流程定义元数据: {}, 请求: {}", id, request);

        ProcessDefinitionDTO result = processDefinitionService.updateMetadata(id, request);
        return Result.success(result);
    }

    @Operation(summary = "更新流程分类", description = "更新流程定义的分类")
    @PutMapping("/{id}/category")
    public Result<Boolean> updateCategory(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String id,
            @Parameter(description = "新分类", required = true)
            @RequestParam @NotBlank(message = "分类不能为空") String category) {
        log.info("更新流程分类: {}, 新分类: {}", id, category);

        boolean result = processDefinitionService.updateCategory(id, category);
        return Result.success(result);
    }

    @Operation(summary = "更新流程描述", description = "更新流程定义的描述")
    @PutMapping("/{id}/description")
    public Result<Boolean> updateDescription(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String id,
            @Parameter(description = "新描述", required = true)
            @RequestParam @NotBlank(message = "描述不能为空") String description) {
        log.info("更新流程描述: {}, 新描述: {}", id, description);

        boolean result = processDefinitionService.updateDescription(id, description);
        return Result.success(result);
    }

    // ==================== 导入导出功能 ====================

    @Operation(summary = "批量导出流程定义", description = "导出指定的流程定义为ZIP文件")
    @GetMapping("/export")
    public ResponseEntity<byte[]> exportProcessDefinitions(
            @Parameter(description = "流程定义ID列表")
            @RequestParam List<String> ids) {
        log.info("批量导出流程定义: {}", ids);

        byte[] zipData = processDefinitionService.exportProcessDefinitions(ids);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "process-definitions.zip");

        return ResponseEntity.ok()
                .headers(headers)
                .body(zipData);
    }

    @Operation(summary = "批量导入流程定义", description = "从ZIP文件批量导入流程定义")
    @PostMapping("/import")
    @PreAuthorize("hasRole('ADMIN') or hasAuthority('WORKFLOW_IMPORT')")
    @Transactional
    public Result<Map<String, Object>> importProcessDefinitions(
            @Parameter(description = "ZIP文件", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "是否覆盖已存在的流程定义")
            @RequestParam(value = "overwrite", defaultValue = "false") boolean overwrite) {
        log.info("批量导入流程定义: {}, 覆盖模式: {}", file.getOriginalFilename(), overwrite);

        Map<String, Object> result = processDefinitionService.importProcessDefinitions(file, overwrite);
        return Result.success(result);
    }

    @Operation(summary = "验证BPMN文件", description = "验证BPMN文件格式和内容的正确性")
    @PostMapping("/validate")
    public Result<Map<String, Object>> validateBpmnFile(
            @Parameter(description = "BPMN文件", required = true)
            @RequestParam("file") MultipartFile file) {
        log.info("验证BPMN文件: {}", file.getOriginalFilename());

        Map<String, Object> validationResult = processDefinitionService.validateBpmnFile(file);
        return Result.success(validationResult);
    }

    @Operation(summary = "获取流程定义模板", description = "获取标准的BPMN流程定义模板")
    @GetMapping("/template")
    public ResponseEntity<byte[]> getProcessDefinitionTemplate(
            @Parameter(description = "模板类型")
            @RequestParam(value = "type", defaultValue = "basic") String templateType) {
        log.info("获取流程定义模板: {}", templateType);

        byte[] templateData = processDefinitionService.getProcessDefinitionTemplate(templateType);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_XML);
        headers.setContentDispositionFormData("attachment", templateType + "-template.bpmn");

        return ResponseEntity.ok()
                .headers(headers)
                .body(templateData);
    }

    // ==================== 流程图增强功能 ====================

    @Operation(summary = "获取流程图预览", description = "获取流程定义的流程图预览（支持多种格式）")
    @GetMapping("/{id}/diagram/preview")
    public ResponseEntity<byte[]> getProcessDiagramPreview(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String id,
            @Parameter(description = "图片格式", schema = @io.swagger.v3.oas.annotations.media.Schema(allowableValues = {"png", "svg", "jpg"}))
            @RequestParam(value = "format", defaultValue = "png") String format,
            @Parameter(description = "图片宽度")
            @RequestParam(value = "width", defaultValue = "1200") int width,
            @Parameter(description = "图片高度")
            @RequestParam(value = "height", defaultValue = "800") int height) {
        log.info("获取流程图预览: id={}, format={}, width={}, height={}", id, format, width, height);

        byte[] diagramData = processDefinitionService.generateProcessDiagramPreview(id, format, width, height);

        HttpHeaders headers = new HttpHeaders();
        switch (format.toLowerCase()) {
            case "svg":
                headers.setContentType(MediaType.valueOf("image/svg+xml"));
                break;
            case "jpg":
                headers.setContentType(MediaType.IMAGE_JPEG);
                break;
            default:
                headers.setContentType(MediaType.IMAGE_PNG);
        }
        headers.set("Content-Disposition", "inline; filename=process-preview-" + id + "." + format);
        headers.set("Cache-Control", "public, max-age=1800"); // 缓存30分钟

        return ResponseEntity.ok()
                .headers(headers)
                .body(diagramData);
    }

    @Operation(summary = "获取流程图高亮节点", description = "获取带有高亮指定节点的流程图")
    @GetMapping("/{id}/diagram/highlight")
    public ResponseEntity<byte[]> getProcessDiagramWithHighlight(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String id,
            @Parameter(description = "高亮节点ID列表")
            @RequestParam(value = "highlightNodes", required = false) List<String> highlightNodes,
            @Parameter(description = "高亮连接线ID列表")
            @RequestParam(value = "highlightFlows", required = false) List<String> highlightFlows,
            @Parameter(description = "图片格式")
            @RequestParam(value = "format", defaultValue = "png") String format) {
        log.info("获取高亮流程图: id={}, highlightNodes={}, highlightFlows={}", id, highlightNodes, highlightFlows);

        byte[] diagramData = processDefinitionService.generateProcessDiagramWithHighlight(
                id, highlightNodes, highlightFlows, format);

        HttpHeaders headers = new HttpHeaders();
        if ("svg".equalsIgnoreCase(format)) {
            headers.setContentType(MediaType.valueOf("image/svg+xml"));
        } else {
            headers.setContentType(MediaType.IMAGE_PNG);
        }
        headers.set("Content-Disposition", "inline; filename=process-highlight-" + id + "." + format);

        return ResponseEntity.ok()
                .headers(headers)
                .body(diagramData);
    }

    @Operation(summary = "获取流程图元数据", description = "获取流程图的元数据信息（节点、连接线等）")
    @GetMapping("/{id}/diagram/metadata")
    public Result<Map<String, Object>> getProcessDiagramMetadata(
            @Parameter(description = "流程定义ID", required = true)
            @PathVariable @NotBlank(message = "流程定义ID不能为空") String id) {
        log.info("获取流程图元数据: {}", id);

        Map<String, Object> metadata = processDefinitionService.getProcessDiagramMetadata(id);
        return Result.success(metadata);
    }
}
