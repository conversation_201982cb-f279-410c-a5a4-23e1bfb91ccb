package com.hzwangda.edu.workflow.service;

import java.util.List;
import java.util.Map;

/**
 * 任务超时处理服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public interface TaskTimeoutService {

    /**
     * 检查并处理超时任务
     *
     * @return 处理结果统计
     */
    Map<String, Object> checkAndHandleTimeoutTasks();

    /**
     * 处理指定任务的超时
     *
     * @param taskId 任务ID
     * @return 处理结果
     */
    boolean handleTaskTimeout(String taskId);

    /**
     * 获取超时任务列表
     *
     * @return 超时任务列表
     */
    List<Map<String, Object>> getTimeoutTasks();

    /**
     * 设置任务超时配置
     *
     * @param processDefinitionKey 流程定义Key
     * @param activityId 活动节点ID
     * @param timeoutMinutes 超时时间（分钟）
     * @param timeoutAction 超时处理动作
     * @return 设置结果
     */
    boolean setTaskTimeoutConfig(String processDefinitionKey, String activityId,
                               int timeoutMinutes, String timeoutAction);

    /**
     * 获取任务超时配置
     *
     * @param processDefinitionKey 流程定义Key
     * @param activityId 活动节点ID
     * @return 超时配置
     */
    Map<String, Object> getTaskTimeoutConfig(String processDefinitionKey, String activityId);

    /**
     * 删除任务超时配置
     *
     * @param processDefinitionKey 流程定义Key
     * @param activityId 活动节点ID
     * @return 删除结果
     */
    boolean deleteTaskTimeoutConfig(String processDefinitionKey, String activityId);

    /**
     * 获取所有超时配置
     *
     * @return 超时配置列表
     */
    List<Map<String, Object>> getAllTimeoutConfigs();

    /**
     * 发送超时通知
     *
     * @param taskId 任务ID
     * @param notificationType 通知类型（WARNING, ESCALATION）
     * @return 发送结果
     */
    boolean sendTimeoutNotification(String taskId, String notificationType);

    /**
     * 任务超时升级处理
     *
     * @param taskId 任务ID
     * @param escalateToUserId 升级到的用户ID
     * @return 升级结果
     */
    boolean escalateTask(String taskId, String escalateToUserId);

    /**
     * 任务超时自动完成
     *
     * @param taskId 任务ID
     * @param autoCompleteVariables 自动完成时的变量
     * @return 完成结果
     */
    boolean autoCompleteTask(String taskId, Map<String, Object> autoCompleteVariables);

    /**
     * 启用任务超时监控
     */
    void enableTimeoutMonitoring();

    /**
     * 禁用任务超时监控
     */
    void disableTimeoutMonitoring();

    /**
     * 获取超时监控状态
     *
     * @return 监控状态
     */
    boolean isTimeoutMonitoringEnabled();
}
