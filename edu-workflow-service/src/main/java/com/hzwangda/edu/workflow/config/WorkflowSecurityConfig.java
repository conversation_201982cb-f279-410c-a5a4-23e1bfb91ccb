package com.hzwangda.edu.workflow.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * 工作流服务安全配置
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class WorkflowSecurityConfig {

    /**
     * 配置安全过滤器链
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF，因为我们使用JWT
            .csrf().disable()

            // 设置会话管理为无状态
            .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()

            // 配置授权规则
            .authorizeHttpRequests(authz -> authz
                // 公开访问的端点
                .requestMatchers(
                    "/api/v1/process-definitions/categories",
                    "/api/v1/process-definitions/exists/**",
                    "/api/v1/process-definitions/template",
                    "/api/v1/process-definitions/validate"
                ).permitAll()

                // 查询操作 - 需要基本权限
                .requestMatchers(
                    "GET",
                    "/api/v1/process-definitions/**"
                ).hasAnyAuthority("WORKFLOW_READ", "WORKFLOW_MANAGE", "ADMIN")

                // 部署操作 - 需要部署权限
                .requestMatchers(
                    "POST",
                    "/api/v1/process-definitions/deploy"
                ).hasAnyAuthority("WORKFLOW_DEPLOY", "ADMIN")

                // 删除操作 - 需要删除权限
                .requestMatchers(
                    "DELETE",
                    "/api/v1/process-definitions/**"
                ).hasAnyAuthority("WORKFLOW_DELETE", "ADMIN")

                // 管理操作 - 需要管理权限
                .requestMatchers(
                    "PUT",
                    "/api/v1/process-definitions/**"
                ).hasAnyAuthority("WORKFLOW_MANAGE", "ADMIN")

                // 导入导出操作 - 需要特殊权限
                .requestMatchers(
                    "/api/v1/process-definitions/import",
                    "/api/v1/process-definitions/export"
                ).hasAnyAuthority("WORKFLOW_IMPORT", "WORKFLOW_EXPORT", "ADMIN")

                // 其他所有请求都需要认证
                .anyRequest().authenticated()
            );

        // 这里可以添加JWT过滤器
        // http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    /**
     * 工作流权限常量
     */
    public static class WorkflowPermissions {
        public static final String READ = "WORKFLOW_READ";
        public static final String DEPLOY = "WORKFLOW_DEPLOY";
        public static final String DELETE = "WORKFLOW_DELETE";
        public static final String MANAGE = "WORKFLOW_MANAGE";
        public static final String IMPORT = "WORKFLOW_IMPORT";
        public static final String EXPORT = "WORKFLOW_EXPORT";
        public static final String ADMIN = "ADMIN";
    }

    /**
     * 工作流角色常量
     */
    public static class WorkflowRoles {
        public static final String WORKFLOW_USER = "ROLE_WORKFLOW_USER";
        public static final String WORKFLOW_ADMIN = "ROLE_WORKFLOW_ADMIN";
        public static final String SYSTEM_ADMIN = "ROLE_ADMIN";
    }
}
