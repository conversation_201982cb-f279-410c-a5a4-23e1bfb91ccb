package com.hzwangda.edu.workflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务实例响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "任务实例响应")
public class TaskInstanceResponse {

    @Schema(description = "任务ID", example = "task_001")
    private String id;

    @Schema(description = "流程实例ID", example = "process_001")
    private String processInstanceId;

    @Schema(description = "流程定义键", example = "leave_process")
    private String processKey;

    @Schema(description = "流程名称", example = "请假流程")
    private String processName;

    @Schema(description = "任务键", example = "manager_approve")
    private String taskKey;

    @Schema(description = "任务名称", example = "部门经理审批")
    private String taskName;

    @Schema(description = "任务描述", example = "请部门经理审批员工请假申请")
    private String taskDescription;

    @Schema(description = "处理人ID", example = "user_001")
    private String assigneeId;

    @Schema(description = "处理人姓名", example = "张经理")
    private String assigneeName;

    @Schema(description = "候选人ID列表")
    private String candidateUsers;

    @Schema(description = "候选组ID列表")
    private String candidateGroups;

    @Schema(description = "任务状态", example = "PENDING")
    private String status;

    @Schema(description = "优先级", example = "50")
    private Integer priority;

    @Schema(description = "到期时间")
    private LocalDateTime dueDate;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "认领时间")
    private LocalDateTime claimTime;

    @Schema(description = "完成时间")
    private LocalDateTime completeTime;

    @Schema(description = "持续时间(毫秒)")
    private Long duration;

    @Schema(description = "审批结果", example = "APPROVED")
    private String result;

    @Schema(description = "审批意见", example = "同意请假")
    private String comment;

    @Schema(description = "任务变量JSON")
    private String variables;

    @Schema(description = "表单数据JSON")
    private String formData;

    @Schema(description = "业务键", example = "LEAVE_20241219_001")
    private String businessKey;

    @Schema(description = "业务类型", example = "LEAVE_APPLICATION")
    private String businessType;

    @Schema(description = "发起人ID", example = "user_002")
    private String initiatorId;

    @Schema(description = "发起人姓名", example = "李员工")
    private String initiatorName;

    @Schema(description = "流程标题", example = "李员工的请假申请")
    private String processTitle;

    @Schema(description = "是否可操作", example = "true")
    private Boolean operable;

    @Schema(description = "可执行操作列表")
    private String[] availableActions;

    @Schema(description = "扩展数据")
    private Object extData;
}
