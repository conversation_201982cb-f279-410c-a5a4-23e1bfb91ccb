package com.hzwangda.edu.workflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 流程统计请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "流程统计请求")
public class ProcessStatisticsRequest {

    @Schema(description = "开始时间", example = "2024-01-01T00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间", example = "2024-12-31T23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "流程定义键列表", example = "[\"leave_process\", \"expense_process\"]")
    private List<String> processKeys;

    @Schema(description = "流程分类列表", example = "[\"ATTENDANCE\", \"PERSONNEL_CHANGE\"]")
    private List<String> categories;

    @Schema(description = "发起人ID列表", example = "[\"user_001\", \"user_002\"]")
    private List<String> initiatorIds;

    @Schema(description = "部门ID列表", example = "[\"dept_001\", \"dept_002\"]")
    private List<String> departmentIds;

    @Schema(description = "流程状态列表", example = "[\"RUNNING\", \"COMPLETED\"]")
    private List<String> statuses;

    @Schema(description = "统计维度", example = "DAY")
    private StatisticsDimension dimension = StatisticsDimension.DAY;

    @Schema(description = "统计类型列表", example = "[\"COUNT\", \"DURATION\", \"EFFICIENCY\"]")
    private List<StatisticsType> types;

    @Schema(description = "是否包含详细数据", example = "false")
    private Boolean includeDetails = false;

    @Schema(description = "分组字段", example = "processKey")
    private String groupBy;

    @Schema(description = "排序字段", example = "count")
    private String orderBy = "count";

    @Schema(description = "排序方向", example = "DESC")
    private String orderDirection = "DESC";

    @Schema(description = "限制结果数量", example = "100")
    private Integer limit;

    @Schema(description = "扩展参数")
    private Object extParams;

    /**
     * 统计维度枚举
     */
    public enum StatisticsDimension {
        HOUR("小时"),
        DAY("天"),
        WEEK("周"),
        MONTH("月"),
        QUARTER("季度"),
        YEAR("年");

        private final String description;

        StatisticsDimension(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 统计类型枚举
     */
    public enum StatisticsType {
        COUNT("数量统计"),
        DURATION("时长统计"),
        EFFICIENCY("效率统计"),
        TREND("趋势统计"),
        DISTRIBUTION("分布统计"),
        COMPARISON("对比统计");

        private final String description;

        StatisticsType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 创建默认统计请求（最近30天）
     */
    public static ProcessStatisticsRequest defaultRequest() {
        ProcessStatisticsRequest request = new ProcessStatisticsRequest();
        request.setEndTime(LocalDateTime.now());
        request.setStartTime(LocalDateTime.now().minusDays(30));
        request.setDimension(StatisticsDimension.DAY);
        return request;
    }

    /**
     * 创建指定时间范围的统计请求
     */
    public static ProcessStatisticsRequest timeRange(LocalDateTime startTime, LocalDateTime endTime) {
        ProcessStatisticsRequest request = new ProcessStatisticsRequest();
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        return request;
    }
}
