package com.hzwangda.edu.workflow.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 流程实例详情响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "流程实例详情响应")
public class ProcessInstanceDetailResponse {

    @Schema(description = "流程实例信息")
    private ProcessInstanceResponse processInstance;

    @Schema(description = "任务列表")
    private List<TaskInstanceResponse> tasks;

    @Schema(description = "历史任务列表")
    private List<TaskInstanceResponse> historyTasks;

    @Schema(description = "流程图数据")
    private ProcessDiagramData diagramData;

    @Schema(description = "流程变量")
    private Object variables;

    @Schema(description = "表单数据")
    private Object formData;

    @Schema(description = "审批记录")
    private List<ApprovalRecord> approvalRecords;

    @Schema(description = "流程轨迹")
    private List<ProcessTrack> processTracks;

    @Schema(description = "扩展数据")
    private Object extData;

    /**
     * 流程图数据
     */
    @Data
    @Schema(description = "流程图数据")
    public static class ProcessDiagramData {

        @Schema(description = "流程图XML")
        private String diagramXml;

        @Schema(description = "流程图SVG")
        private String diagramSvg;

        @Schema(description = "当前活动节点")
        private List<String> activeActivityIds;

        @Schema(description = "已完成节点")
        private List<String> finishedActivityIds;

        @Schema(description = "高亮路径")
        private List<String> highlightedFlows;
    }

    /**
     * 审批记录
     */
    @Data
    @Schema(description = "审批记录")
    public static class ApprovalRecord {

        @Schema(description = "任务ID")
        private String taskId;

        @Schema(description = "任务名称")
        private String taskName;

        @Schema(description = "处理人ID")
        private String assigneeId;

        @Schema(description = "处理人姓名")
        private String assigneeName;

        @Schema(description = "审批结果")
        private String result;

        @Schema(description = "审批意见")
        private String comment;

        @Schema(description = "处理时间")
        private String processTime;

        @Schema(description = "持续时间")
        private String duration;
    }

    /**
     * 流程轨迹
     */
    @Data
    @Schema(description = "流程轨迹")
    public static class ProcessTrack {

        @Schema(description = "节点ID")
        private String activityId;

        @Schema(description = "节点名称")
        private String activityName;

        @Schema(description = "节点类型")
        private String activityType;

        @Schema(description = "开始时间")
        private String startTime;

        @Schema(description = "结束时间")
        private String endTime;

        @Schema(description = "持续时间")
        private String duration;

        @Schema(description = "处理人")
        private String assignee;

        @Schema(description = "状态")
        private String status;
    }
}
