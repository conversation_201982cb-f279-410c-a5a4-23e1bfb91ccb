package com.hzwangda.edu.workflow.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hzwangda.edu.workflow.dto.ProcessDefinitionDTO;
import com.hzwangda.edu.workflow.dto.ProcessDefinitionMetadataUpdateRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * ProcessDefinition集成测试
 * 测试完整的API流程和数据库交互
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@DisplayName("流程定义集成测试")
class ProcessDefinitionIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @DisplayName("完整的流程定义生命周期测试")
    void testCompleteProcessDefinitionLifecycle() throws Exception {
        // 1. 部署流程定义
        String bpmnContent = createSampleBpmnContent();
        MockMultipartFile bpmnFile = new MockMultipartFile(
                "file", "test-process.bpmn", "application/xml", bpmnContent.getBytes());

        String deployResult = mockMvc.perform(multipart("/api/v1/process-definitions/deploy")
                        .file(bpmnFile)
                        .param("name", "测试流程")
                        .param("category", "测试分类")
                        .param("activate", "true"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.key").value("testProcess"))
                .andReturn()
                .getResponse()
                .getContentAsString();

        // 提取流程定义ID
        ProcessDefinitionDTO deployedProcess = objectMapper.readTree(deployResult)
                .get("data")
                .traverse(objectMapper)
                .readValueAs(ProcessDefinitionDTO.class);
        String processId = deployedProcess.getId();

        // 2. 查询流程定义
        mockMvc.perform(get("/api/v1/process-definitions/{id}", processId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(processId))
                .andExpect(jsonPath("$.data.key").value("testProcess"));

        // 3. 获取流程定义XML
        mockMvc.perform(get("/api/v1/process-definitions/{id}/xml", processId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isString());

        // 4. 生成流程图
        mockMvc.perform(get("/api/v1/process-definitions/{id}/diagram", processId))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "image/png"));

        // 5. 获取统计信息
        mockMvc.perform(get("/api/v1/process-definitions/{id}/statistics", processId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isMap());

        // 6. 更新元数据
        ProcessDefinitionMetadataUpdateRequest updateRequest = ProcessDefinitionMetadataUpdateRequest.builder()
                .name("更新后的测试流程")
                .description("更新后的描述")
                .category("更新后的分类")
                .enabled(true)
                .updateReason("集成测试更新")
                .build();

        mockMvc.perform(put("/api/v1/process-definitions/{id}/metadata", processId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 7. 挂起流程定义
        mockMvc.perform(put("/api/v1/process-definitions/{id}/suspend", processId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(true));

        // 8. 激活流程定义
        mockMvc.perform(put("/api/v1/process-definitions/{id}/activate", processId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(true));

        // 9. 删除流程定义
        mockMvc.perform(delete("/api/v1/process-definitions/{deploymentId}", deployedProcess.getDeploymentId())
                        .param("cascade", "true"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    @DisplayName("版本管理集成测试")
    void testVersionManagement() throws Exception {
        // 1. 部署第一个版本
        String bpmnContent1 = createSampleBpmnContent("testVersionProcess", "版本1");
        MockMultipartFile bpmnFile1 = new MockMultipartFile(
                "file", "test-process-v1.bpmn", "application/xml", bpmnContent1.getBytes());

        String deployResult1 = mockMvc.perform(multipart("/api/v1/process-definitions/deploy")
                        .file(bpmnFile1)
                        .param("name", "版本测试流程V1")
                        .param("category", "版本测试"))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();

        ProcessDefinitionDTO process1 = objectMapper.readTree(deployResult1)
                .get("data")
                .traverse(objectMapper)
                .readValueAs(ProcessDefinitionDTO.class);

        // 2. 部署第二个版本
        String bpmnContent2 = createSampleBpmnContent("testVersionProcess", "版本2");
        MockMultipartFile bpmnFile2 = new MockMultipartFile(
                "file", "test-process-v2.bpmn", "application/xml", bpmnContent2.getBytes());

        String deployResult2 = mockMvc.perform(multipart("/api/v1/process-definitions/deploy")
                        .file(bpmnFile2)
                        .param("name", "版本测试流程V2")
                        .param("category", "版本测试"))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();

        ProcessDefinitionDTO process2 = objectMapper.readTree(deployResult2)
                .get("data")
                .traverse(objectMapper)
                .readValueAs(ProcessDefinitionDTO.class);

        // 3. 获取所有版本
        mockMvc.perform(get("/api/v1/process-definitions/{key}/versions", "testVersionProcess"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2));

        // 4. 版本比较
        mockMvc.perform(get("/api/v1/process-definitions/{id1}/compare/{id2}",
                        process1.getId(), process2.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.version1").exists())
                .andExpect(jsonPath("$.data.version2").exists())
                .andExpect(jsonPath("$.data.differences").exists());

        // 5. 设置默认版本
        mockMvc.perform(post("/api/v1/process-definitions/{id}/set-default", process1.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(true));

        // 6. 版本回滚
        mockMvc.perform(post("/api/v1/process-definitions/{id}/rollback", process2.getId())
                        .param("reason", "集成测试回滚"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(process2.getId()));
    }

    @Test
    @DisplayName("导入导出集成测试")
    void testImportExport() throws Exception {
        // 1. 部署测试流程
        String bpmnContent = createSampleBpmnContent();
        MockMultipartFile bpmnFile = new MockMultipartFile(
                "file", "export-test.bpmn", "application/xml", bpmnContent.getBytes());

        String deployResult = mockMvc.perform(multipart("/api/v1/process-definitions/deploy")
                        .file(bpmnFile)
                        .param("name", "导出测试流程")
                        .param("category", "导出测试"))
                .andExpect(status().isOk())
                .andReturn()
                .getResponse()
                .getContentAsString();

        ProcessDefinitionDTO deployedProcess = objectMapper.readTree(deployResult)
                .get("data")
                .traverse(objectMapper)
                .readValueAs(ProcessDefinitionDTO.class);

        // 2. 导出流程定义
        byte[] exportedData = mockMvc.perform(get("/api/v1/process-definitions/export")
                        .param("ids", deployedProcess.getId()))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "application/octet-stream"))
                .andReturn()
                .getResponse()
                .getContentAsByteArray();

        // 3. 验证导出的数据不为空
        assert exportedData.length > 0;

        // 4. 获取模板
        mockMvc.perform(get("/api/v1/process-definitions/template")
                        .param("type", "basic"))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "application/xml"));

        mockMvc.perform(get("/api/v1/process-definitions/template")
                        .param("type", "approval"))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "application/xml"));
    }

    /**
     * 创建示例BPMN内容
     */
    private String createSampleBpmnContent() {
        return createSampleBpmnContent("testProcess", "测试流程");
    }

    /**
     * 创建示例BPMN内容
     */
    private String createSampleBpmnContent(String processId, String processName) {
        return String.format("""
                <?xml version="1.0" encoding="UTF-8"?>
                <definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
                           xmlns:flowable="http://flowable.org/bpmn"
                           targetNamespace="http://www.flowable.org/processdef">
                
                  <process id="%s" name="%s" isExecutable="true">
                    <startEvent id="startEvent" name="开始"/>
                    <userTask id="userTask" name="用户任务" flowable:assignee="testUser"/>
                    <endEvent id="endEvent" name="结束"/>
                    
                    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="userTask"/>
                    <sequenceFlow id="flow2" sourceRef="userTask" targetRef="endEvent"/>
                  </process>
                  
                </definitions>
                """, processId, processName);
    }
}
