package com.hzwangda.edu.workflow.service;

import com.hzwangda.edu.common.exception.BusinessException;
import com.hzwangda.edu.workflow.dto.ProcessDefinitionDTO;
import com.hzwangda.edu.workflow.dto.ProcessDefinitionMetadataUpdateRequest;
import com.hzwangda.edu.workflow.service.impl.ProcessDefinitionServiceImpl;
import org.flowable.engine.HistoryService;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * ProcessDefinitionServiceImpl单元测试
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("流程定义服务实现测试")
class ProcessDefinitionServiceImplTest {

    @Mock
    private RepositoryService repositoryService;

    @Mock
    private RuntimeService runtimeService;

    @Mock
    private HistoryService historyService;

    @Mock
    private ProcessEngine processEngine;

    @Mock
    private ProcessDefinitionQuery processDefinitionQuery;

    @Mock
    private ProcessDefinition processDefinition;

    @InjectMocks
    private ProcessDefinitionServiceImpl processDefinitionService;

    @BeforeEach
    void setUp() {
        when(repositoryService.createProcessDefinitionQuery()).thenReturn(processDefinitionQuery);
    }

    @Test
    @DisplayName("获取流程定义所有版本 - 成功")
    void testGetProcessDefinitionVersions_Success() {
        // Given
        String key = "leave_approval";
        ProcessDefinition pd1 = mock(ProcessDefinition.class);
        ProcessDefinition pd2 = mock(ProcessDefinition.class);

        when(pd1.getId()).thenReturn("pd-1");
        when(pd1.getKey()).thenReturn(key);
        when(pd1.getName()).thenReturn("请假审批流程");
        when(pd1.getVersion()).thenReturn(1);
        when(pd1.getDeploymentId()).thenReturn("dep-1");

        when(pd2.getId()).thenReturn("pd-2");
        when(pd2.getKey()).thenReturn(key);
        when(pd2.getName()).thenReturn("请假审批流程");
        when(pd2.getVersion()).thenReturn(2);
        when(pd2.getDeploymentId()).thenReturn("dep-2");

        when(processDefinitionQuery.processDefinitionKey(key)).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.orderByProcessDefinitionVersion()).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.desc()).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.list()).thenReturn(Arrays.asList(pd1, pd2));

        // Mock history service for statistics
        when(historyService.createHistoricProcessInstanceQuery()).thenReturn(mock(org.flowable.engine.history.HistoricProcessInstanceQuery.class));
        when(runtimeService.createProcessInstanceQuery()).thenReturn(mock(org.flowable.engine.runtime.ProcessInstanceQuery.class));

        // When
        List<ProcessDefinitionDTO> result = processDefinitionService.getProcessDefinitionVersions(key);

        // Then
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getKey()).isEqualTo(key);
        assertThat(result.get(0).getVersion()).isEqualTo(1);
        assertThat(result.get(1).getVersion()).isEqualTo(2);
    }

    @Test
    @DisplayName("设置默认版本 - 成功")
    void testSetDefaultVersion_Success() {
        // Given
        String id = "process-def-1";
        String key = "leave_approval";

        when(processDefinitionQuery.processDefinitionId(id)).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.singleResult()).thenReturn(processDefinition);
        when(processDefinition.getKey()).thenReturn(key);
        when(processDefinition.getId()).thenReturn(id);

        ProcessDefinitionQuery otherVersionsQuery = mock(ProcessDefinitionQuery.class);
        when(repositoryService.createProcessDefinitionQuery()).thenReturn(processDefinitionQuery, otherVersionsQuery);
        when(otherVersionsQuery.processDefinitionKey(key)).thenReturn(otherVersionsQuery);
        when(otherVersionsQuery.active()).thenReturn(otherVersionsQuery);
        when(otherVersionsQuery.list()).thenReturn(Arrays.asList());

        // When
        boolean result = processDefinitionService.setDefaultVersion(id);

        // Then
        assertThat(result).isTrue();
        verify(repositoryService).activateProcessDefinitionById(id);
    }

    @Test
    @DisplayName("设置默认版本 - 流程定义不存在")
    void testSetDefaultVersion_ProcessDefinitionNotFound() {
        // Given
        String id = "non-existent-id";
        when(processDefinitionQuery.processDefinitionId(id)).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.singleResult()).thenReturn(null);

        // When & Then
        assertThatThrownBy(() -> processDefinitionService.setDefaultVersion(id))
                .isInstanceOf(BusinessException.class)
                .hasMessageContaining("流程定义不存在");
    }

    @Test
    @DisplayName("版本比较 - 成功")
    void testCompareVersions_Success() {
        // Given
        String id1 = "process-def-1";
        String id2 = "process-def-2";

        ProcessDefinition pd1 = mock(ProcessDefinition.class);
        ProcessDefinition pd2 = mock(ProcessDefinition.class);

        when(pd1.getId()).thenReturn(id1);
        when(pd1.getName()).thenReturn("流程1");
        when(pd1.getVersion()).thenReturn(1);
        when(pd1.getDeploymentId()).thenReturn("dep-1");

        when(pd2.getId()).thenReturn(id2);
        when(pd2.getName()).thenReturn("流程2");
        when(pd2.getVersion()).thenReturn(2);
        when(pd2.getDeploymentId()).thenReturn("dep-2");

        when(processDefinitionQuery.processDefinitionId(id1)).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.processDefinitionId(id2)).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.singleResult()).thenReturn(pd1, pd2);

        // Mock history service for statistics
        when(historyService.createHistoricProcessInstanceQuery()).thenReturn(mock(org.flowable.engine.history.HistoricProcessInstanceQuery.class));
        when(runtimeService.createProcessInstanceQuery()).thenReturn(mock(org.flowable.engine.runtime.ProcessInstanceQuery.class));

        // When
        Map<String, Object> result = processDefinitionService.compareVersions(id1, id2);

        // Then
        assertThat(result).containsKeys("version1", "version2", "differences", "compareTime");
        assertThat(result.get("version1")).isInstanceOf(ProcessDefinitionDTO.class);
        assertThat(result.get("version2")).isInstanceOf(ProcessDefinitionDTO.class);
    }

    @Test
    @DisplayName("版本回滚 - 成功")
    void testRollbackVersion_Success() {
        // Given
        String id = "process-def-1";
        String reason = "修复bug";
        String key = "leave_approval";

        when(processDefinitionQuery.processDefinitionId(id)).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.singleResult()).thenReturn(processDefinition);
        when(processDefinition.getKey()).thenReturn(key);
        when(processDefinition.getId()).thenReturn(id);
        when(processDefinition.getVersion()).thenReturn(1);
        when(processDefinition.getDeploymentId()).thenReturn("dep-1");

        ProcessDefinitionQuery otherVersionsQuery = mock(ProcessDefinitionQuery.class);
        when(repositoryService.createProcessDefinitionQuery()).thenReturn(processDefinitionQuery, otherVersionsQuery);
        when(otherVersionsQuery.processDefinitionKey(key)).thenReturn(otherVersionsQuery);
        when(otherVersionsQuery.active()).thenReturn(otherVersionsQuery);
        when(otherVersionsQuery.list()).thenReturn(Arrays.asList());

        // Mock history service for statistics
        when(historyService.createHistoricProcessInstanceQuery()).thenReturn(mock(org.flowable.engine.history.HistoricProcessInstanceQuery.class));
        when(runtimeService.createProcessInstanceQuery()).thenReturn(mock(org.flowable.engine.runtime.ProcessInstanceQuery.class));

        // When
        ProcessDefinitionDTO result = processDefinitionService.rollbackVersion(id, reason);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(id);
        verify(repositoryService).activateProcessDefinitionById(id);
    }

    @Test
    @DisplayName("更新流程分类 - 成功")
    void testUpdateCategory_Success() {
        // Given
        String id = "process-def-1";
        String category = "新分类";
        String deploymentId = "deployment-1";

        when(processDefinitionQuery.processDefinitionId(id)).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.singleResult()).thenReturn(processDefinition);
        when(processDefinition.getDeploymentId()).thenReturn(deploymentId);

        // When
        boolean result = processDefinitionService.updateCategory(id, category);

        // Then
        assertThat(result).isTrue();
        verify(repositoryService).setDeploymentCategory(deploymentId, category);
    }

    @Test
    @DisplayName("更新流程描述 - 成功")
    void testUpdateDescription_Success() {
        // Given
        String id = "process-def-1";
        String description = "新描述";

        when(processDefinitionQuery.processDefinitionId(id)).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.singleResult()).thenReturn(processDefinition);
        when(processDefinition.getId()).thenReturn(id);

        // When
        boolean result = processDefinitionService.updateDescription(id, description);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("更新元数据 - 成功")
    void testUpdateMetadata_Success() {
        // Given
        String id = "process-def-1";
        ProcessDefinitionMetadataUpdateRequest request = ProcessDefinitionMetadataUpdateRequest.builder()
                .name("新名称")
                .description("新描述")
                .category("新分类")
                .enabled(true)
                .updateReason("测试更新")
                .build();

        when(processDefinitionQuery.processDefinitionId(id)).thenReturn(processDefinitionQuery);
        when(processDefinitionQuery.singleResult()).thenReturn(processDefinition);
        when(processDefinition.getId()).thenReturn(id);
        when(processDefinition.getDeploymentId()).thenReturn("dep-1");

        // Mock history service for statistics
        when(historyService.createHistoricProcessInstanceQuery()).thenReturn(mock(org.flowable.engine.history.HistoricProcessInstanceQuery.class));
        when(runtimeService.createProcessInstanceQuery()).thenReturn(mock(org.flowable.engine.runtime.ProcessInstanceQuery.class));

        // When
        ProcessDefinitionDTO result = processDefinitionService.updateMetadata(id, request);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(id);
    }
}
