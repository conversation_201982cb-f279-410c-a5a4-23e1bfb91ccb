package com.hzwangda.edu.workflow.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hzwangda.edu.workflow.dto.ProcessDefinitionDTO;
import com.hzwangda.edu.workflow.dto.ProcessDefinitionMetadataUpdateRequest;
import com.hzwangda.edu.workflow.service.ProcessDefinitionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.header;

/**
 * ProcessDefinitionController单元测试
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@WebMvcTest(ProcessDefinitionController.class)
@DisplayName("流程定义控制器测试")
class ProcessDefinitionControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ProcessDefinitionService processDefinitionService;

    @Autowired
    private ObjectMapper objectMapper;

    private ProcessDefinitionDTO sampleProcessDefinition;

    @BeforeEach
    void setUp() {
        sampleProcessDefinition = ProcessDefinitionDTO.builder()
                .id("process-def-1")
                .key("leave_approval")
                .name("请假审批流程")
                .description("员工请假审批流程")
                .version(1)
                .category("人事管理")
                .deploymentId("deployment-1")
                .suspended(false)
                .deploymentTime(LocalDateTime.now())
                .instanceCount(10L)
                .activeInstanceCount(3L)
                .completedInstanceCount(7L)
                .build();
    }

    @Test
    @DisplayName("获取流程定义所有版本")
    void testGetProcessDefinitionVersions() throws Exception {
        // Given
        String key = "leave_approval";
        List<ProcessDefinitionDTO> versions = Arrays.asList(
                sampleProcessDefinition,
                ProcessDefinitionDTO.builder()
                        .id("process-def-2")
                        .key(key)
                        .name("请假审批流程")
                        .version(2)
                        .build()
        );

        when(processDefinitionService.getProcessDefinitionVersions(key))
                .thenReturn(versions);

        // When & Then
        mockMvc.perform(get("/api/v1/process-definitions/{key}/versions", key))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].key").value(key))
                .andExpect(jsonPath("$.data[0].version").value(1))
                .andExpect(jsonPath("$.data[1].version").value(2));
    }

    @Test
    @DisplayName("设置默认版本")
    void testSetDefaultVersion() throws Exception {
        // Given
        String id = "process-def-1";
        when(processDefinitionService.setDefaultVersion(id)).thenReturn(true);

        // When & Then
        mockMvc.perform(post("/api/v1/process-definitions/{id}/set-default", id))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    @DisplayName("版本比较")
    void testCompareVersions() throws Exception {
        // Given
        String id1 = "process-def-1";
        String id2 = "process-def-2";
        Map<String, Object> comparison = new HashMap<>();
        comparison.put("version1", sampleProcessDefinition);
        comparison.put("version2", sampleProcessDefinition);
        comparison.put("differences", new HashMap<>());
        comparison.put("compareTime", LocalDateTime.now());

        when(processDefinitionService.compareVersions(id1, id2)).thenReturn(comparison);

        // When & Then
        mockMvc.perform(get("/api/v1/process-definitions/{id1}/compare/{id2}", id1, id2))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.version1").exists())
                .andExpect(jsonPath("$.data.version2").exists())
                .andExpect(jsonPath("$.data.differences").exists());
    }

    @Test
    @DisplayName("版本回滚")
    void testRollbackVersion() throws Exception {
        // Given
        String id = "process-def-1";
        String reason = "修复bug";
        when(processDefinitionService.rollbackVersion(id, reason)).thenReturn(sampleProcessDefinition);

        // When & Then
        mockMvc.perform(post("/api/v1/process-definitions/{id}/rollback", id)
                        .param("reason", reason))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(id))
                .andExpect(jsonPath("$.data.key").value("leave_approval"));
    }

    @Test
    @DisplayName("更新流程定义元数据")
    void testUpdateMetadata() throws Exception {
        // Given
        String id = "process-def-1";
        ProcessDefinitionMetadataUpdateRequest request = ProcessDefinitionMetadataUpdateRequest.builder()
                .name("更新后的请假审批流程")
                .description("更新后的描述")
                .category("人事管理")
                .enabled(true)
                .updateReason("优化流程")
                .build();

        when(processDefinitionService.updateMetadata(eq(id), any(ProcessDefinitionMetadataUpdateRequest.class)))
                .thenReturn(sampleProcessDefinition);

        // When & Then
        mockMvc.perform(put("/api/v1/process-definitions/{id}/metadata", id)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(id));
    }

    @Test
    @DisplayName("更新流程分类")
    void testUpdateCategory() throws Exception {
        // Given
        String id = "process-def-1";
        String category = "新分类";
        when(processDefinitionService.updateCategory(id, category)).thenReturn(true);

        // When & Then
        mockMvc.perform(put("/api/v1/process-definitions/{id}/category", id)
                        .param("category", category))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    @DisplayName("更新流程描述")
    void testUpdateDescription() throws Exception {
        // Given
        String id = "process-def-1";
        String description = "新描述";
        when(processDefinitionService.updateDescription(id, description)).thenReturn(true);

        // When & Then
        mockMvc.perform(put("/api/v1/process-definitions/{id}/description", id)
                        .param("description", description))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(true));
    }

    @Test
    @DisplayName("参数验证 - 空白ID")
    void testValidation_BlankId() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/process-definitions/{key}/versions", " "))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("参数验证 - 空白分类")
    void testValidation_BlankCategory() throws Exception {
        // When & Then
        mockMvc.perform(put("/api/v1/process-definitions/process-def-1/category")
                        .param("category", " "))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("批量导出流程定义")
    void testExportProcessDefinitions() throws Exception {
        // Given
        List<String> ids = Arrays.asList("process-def-1", "process-def-2");
        byte[] zipData = "mock zip data".getBytes();

        when(processDefinitionService.exportProcessDefinitions(ids)).thenReturn(zipData);

        // When & Then
        mockMvc.perform(get("/api/v1/process-definitions/export")
                        .param("ids", "process-def-1", "process-def-2"))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "application/octet-stream"))
                .andExpect(header().string("Content-Disposition", "form-data; name=\"attachment\"; filename=\"process-definitions.zip\""));
    }

    @Test
    @DisplayName("批量导入流程定义")
    void testImportProcessDefinitions() throws Exception {
        // Given
        MockMultipartFile file = new MockMultipartFile(
                "file", "processes.zip", "application/zip", "mock zip content".getBytes());

        Map<String, Object> importResult = new HashMap<>();
        importResult.put("total", 2);
        importResult.put("success", 2);
        importResult.put("failure", 0);
        importResult.put("skipped", 0);

        when(processDefinitionService.importProcessDefinitions(any(MultipartFile.class), eq(false)))
                .thenReturn(importResult);

        // When & Then
        mockMvc.perform(multipart("/api/v1/process-definitions/import")
                        .file(file)
                        .param("overwrite", "false"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.total").value(2))
                .andExpect(jsonPath("$.data.success").value(2));
    }

    @Test
    @DisplayName("验证BPMN文件")
    void testValidateBpmnFile() throws Exception {
        // Given
        MockMultipartFile file = new MockMultipartFile(
                "file", "process.bpmn", "application/xml", "mock bpmn content".getBytes());

        Map<String, Object> validationResult = new HashMap<>();
        validationResult.put("valid", true);
        validationResult.put("processId", "testProcess");

        when(processDefinitionService.validateBpmnFile(any(MultipartFile.class)))
                .thenReturn(validationResult);

        // When & Then
        mockMvc.perform(multipart("/api/v1/process-definitions/validate")
                        .file(file))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.valid").value(true))
                .andExpect(jsonPath("$.data.processId").value("testProcess"));
    }

    @Test
    @DisplayName("获取流程定义模板")
    void testGetProcessDefinitionTemplate() throws Exception {
        // Given
        String templateType = "approval";
        byte[] templateData = "mock template content".getBytes();

        when(processDefinitionService.getProcessDefinitionTemplate(templateType))
                .thenReturn(templateData);

        // When & Then
        mockMvc.perform(get("/api/v1/process-definitions/template")
                        .param("type", templateType))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "application/xml"))
                .andExpect(header().string("Content-Disposition", "form-data; name=\"attachment\"; filename=\"" + templateType + "-template.bpmn\""));
    }
}
