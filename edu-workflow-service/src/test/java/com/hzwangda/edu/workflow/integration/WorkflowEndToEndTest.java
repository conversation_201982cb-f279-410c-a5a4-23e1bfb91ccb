package com.hzwangda.edu.workflow.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hzwangda.edu.workflow.dto.ProcessDefinitionDTO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 工作流服务端到端集成测试
 * 测试完整的业务流程，包括流程定义管理、可视化、监控等功能
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("工作流服务端到端集成测试")
class WorkflowEndToEndTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    // 测试数据
    private static String deployedProcessId;
    private static String deploymentId;
    private static String processInstanceId;

    @Test
    @Order(1)
    @DisplayName("1. 完整的请假审批流程测试")
    void testCompleteLeaveApprovalWorkflow() throws Exception {
        // 1. 部署请假审批流程
        String bpmnContent = createLeaveApprovalBpmn();
        MockMultipartFile bpmnFile = new MockMultipartFile(
                "file", "leave-approval.bpmn", "application/xml", bpmnContent.getBytes());

        MvcResult deployResult = mockMvc.perform(multipart("/api/v1/process-definitions/deploy")
                        .file(bpmnFile)
                        .param("name", "请假审批流程")
                        .param("category", "人事管理")
                        .param("activate", "true")
                        .param("description", "员工请假审批流程"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.key").value("leaveApproval"))
                .andReturn();

        // 提取流程定义信息
        String responseContent = deployResult.getResponse().getContentAsString();
        ProcessDefinitionDTO deployedProcess = objectMapper.readTree(responseContent)
                .get("data")
                .traverse(objectMapper)
                .readValueAs(ProcessDefinitionDTO.class);

        deployedProcessId = deployedProcess.getId();
        deploymentId = deployedProcess.getDeploymentId();

        assertNotNull(deployedProcessId);
        assertNotNull(deploymentId);

        // 2. 验证流程定义查询
        mockMvc.perform(get("/api/v1/process-definitions/{id}", deployedProcessId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(deployedProcessId))
                .andExpect(jsonPath("$.data.key").value("leaveApproval"))
                .andExpect(jsonPath("$.data.name").value("请假审批流程"));

        // 3. 获取流程图
        mockMvc.perform(get("/api/v1/process-definitions/{id}/diagram", deployedProcessId))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "image/png"));

        // 4. 获取流程定义XML
        mockMvc.perform(get("/api/v1/process-definitions/{id}/xml", deployedProcessId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isString());

        // 5. 获取流程统计信息
        mockMvc.perform(get("/api/v1/process-definitions/{id}/statistics", deployedProcessId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isMap());
    }

    @Test
    @Order(2)
    @DisplayName("2. 流程可视化功能测试")
    void testProcessVisualizationFeatures() throws Exception {
        assertNotNull(deployedProcessId, "需要先执行流程部署测试");

        // 1. 获取流程图HTML
        mockMvc.perform(get("/api/v1/process-visualization/{processDefinitionId}/diagram-html", deployedProcessId)
                        .param("showGrid", "true")
                        .param("readonly", "true"))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "text/html"));

        // 2. 获取流程图JSON数据
        mockMvc.perform(get("/api/v1/process-visualization/{processDefinitionId}/diagram-json", deployedProcessId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.processDefinitionId").value(deployedProcessId))
                .andExpect(jsonPath("$.data.bpmnXml").isString())
                .andExpect(jsonPath("$.data.elements").exists());

        // 3. 获取流程编辑器
        mockMvc.perform(get("/api/v1/process-visualization/editor")
                        .param("processDefinitionId", deployedProcessId))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "text/html"));

        // 4. 验证BPMN文件
        String validBpmn = createLeaveApprovalBpmn();
        mockMvc.perform(post("/api/v1/process-visualization/editor/validate")
                        .param("bpmnXml", validBpmn))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.valid").value(true))
                .andExpect(jsonPath("$.data.processId").value("leaveApproval"));

        // 5. 导出流程图为PNG
        mockMvc.perform(get("/api/v1/process-visualization/{processDefinitionId}/export/png", deployedProcessId)
                        .param("width", "1200")
                        .param("height", "800"))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "image/png"));

        // 6. 导出流程图为SVG
        mockMvc.perform(get("/api/v1/process-visualization/{processDefinitionId}/export/svg", deployedProcessId))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "image/svg+xml"));
    }

    @Test
    @Order(3)
    @DisplayName("3. 流程监控功能测试")
    void testProcessMonitoringFeatures() throws Exception {
        assertNotNull(deployedProcessId, "需要先执行流程部署测试");

        // 1. 获取实时监控面板数据
        mockMvc.perform(get("/api/v1/process-monitoring/dashboard")
                        .param("processDefinitionKey", "leaveApproval")
                        .param("timeRange", "24"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.timeRange").value(24))
                .andExpect(jsonPath("$.data.activeInstances").isNumber())
                .andExpect(jsonPath("$.data.completedInstances").isNumber());

        // 2. 获取活跃流程实例列表
        mockMvc.perform(get("/api/v1/process-monitoring/active-instances")
                        .param("processDefinitionKey", "leaveApproval")
                        .param("page", "1")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray());

        // 3. 获取流程执行统计
        mockMvc.perform(get("/api/v1/process-monitoring/statistics/leaveApproval")
                        .param("days", "30"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalInstances").isNumber())
                .andExpect(jsonPath("$.data.completedInstances").isNumber())
                .andExpect(jsonPath("$.data.completionRate").isNumber());

        // 4. 获取流程趋势分析
        mockMvc.perform(get("/api/v1/process-monitoring/trends/leaveApproval")
                        .param("days", "30")
                        .param("granularity", "day"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.processDefinitionKey").value("leaveApproval"));

        // 5. 获取系统健康状态
        mockMvc.perform(get("/api/v1/process-monitoring/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.status").value("UP"))
                .andExpect(jsonPath("$.data.database").exists())
                .andExpect(jsonPath("$.data.flowableEngine").exists());

        // 6. 获取系统指标
        mockMvc.perform(get("/api/v1/process-monitoring/metrics"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.processDefinitions").exists())
                .andExpect(jsonPath("$.data.processInstances").exists())
                .andExpect(jsonPath("$.data.system").exists());
    }

    @Test
    @Order(4)
    @DisplayName("4. 流程版本管理测试")
    void testProcessVersionManagement() throws Exception {
        assertNotNull(deployedProcessId, "需要先执行流程部署测试");

        // 1. 部署第二个版本
        String bpmnContentV2 = createLeaveApprovalBpmnV2();
        MockMultipartFile bpmnFileV2 = new MockMultipartFile(
                "file", "leave-approval-v2.bpmn", "application/xml", bpmnContentV2.getBytes());

        MvcResult deployV2Result = mockMvc.perform(multipart("/api/v1/process-definitions/deploy")
                        .file(bpmnFileV2)
                        .param("name", "请假审批流程V2")
                        .param("category", "人事管理"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.key").value("leaveApproval"))
                .andExpect(jsonPath("$.data.version").value(2))
                .andReturn();

        String v2ProcessId = objectMapper.readTree(deployV2Result.getResponse().getContentAsString())
                .get("data").get("id").asText();

        // 2. 获取所有版本
        mockMvc.perform(get("/api/v1/process-definitions/leaveApproval/versions"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2));

        // 3. 版本比较
        mockMvc.perform(get("/api/v1/process-definitions/{id1}/compare/{id2}", deployedProcessId, v2ProcessId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.version1").exists())
                .andExpect(jsonPath("$.data.version2").exists());

        // 4. 设置默认版本
        mockMvc.perform(post("/api/v1/process-definitions/{id}/set-default", deployedProcessId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").value(true));

        // 5. 版本回滚
        mockMvc.perform(post("/api/v1/process-definitions/{id}/rollback", v2ProcessId)
                        .param("reason", "集成测试回滚"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @Order(5)
    @DisplayName("5. 导入导出功能测试")
    void testImportExportFeatures() throws Exception {
        assertNotNull(deployedProcessId, "需要先执行流程部署测试");

        // 1. 导出流程定义
        byte[] exportedData = mockMvc.perform(get("/api/v1/process-definitions/export")
                        .param("ids", deployedProcessId))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "application/octet-stream"))
                .andReturn()
                .getResponse()
                .getContentAsByteArray();

        assertTrue(exportedData.length > 0, "导出的数据不能为空");

        // 2. 获取流程定义模板
        mockMvc.perform(get("/api/v1/process-definitions/template")
                        .param("type", "basic"))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "application/xml"));

        mockMvc.perform(get("/api/v1/process-definitions/template")
                        .param("type", "approval"))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", "application/xml"));

        // 3. 验证BPMN文件
        String validBpmn = createLeaveApprovalBpmn();
        MockMultipartFile validationFile = new MockMultipartFile(
                "file", "test.bpmn", "application/xml", validBpmn.getBytes());

        mockMvc.perform(multipart("/api/v1/process-definitions/validate")
                        .file(validationFile))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.valid").value(true));
    }

    @Test
    @Order(6)
    @DisplayName("6. 清理测试数据")
    void testCleanupTestData() throws Exception {
        if (deploymentId != null) {
            // 删除部署的流程定义
            mockMvc.perform(delete("/api/v1/process-definitions/{deploymentId}", deploymentId)
                            .param("cascade", "true"))
                    .andExpect(status().isOk())
                    .andExpect(jsonPath("$.success").value(true))
                    .andExpect(jsonPath("$.data").value(true));
        }
    }

    /**
     * 创建请假审批BPMN流程定义
     */
    private String createLeaveApprovalBpmn() {
        return """
                <?xml version="1.0" encoding="UTF-8"?>
                <definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
                           xmlns:flowable="http://flowable.org/bpmn"
                           targetNamespace="http://www.flowable.org/processdef">
                
                  <process id="leaveApproval" name="请假审批流程" isExecutable="true">
                    <startEvent id="startEvent" name="提交请假申请"/>
                    <userTask id="managerApproval" name="经理审批" flowable:assignee="${manager}"/>
                    <exclusiveGateway id="decision" name="审批决策"/>
                    <userTask id="hrApproval" name="HR审批" flowable:assignee="${hr}"/>
                    <endEvent id="approvedEnd" name="审批通过"/>
                    <endEvent id="rejectedEnd" name="审批拒绝"/>
                    
                    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="managerApproval"/>
                    <sequenceFlow id="flow2" sourceRef="managerApproval" targetRef="decision"/>
                    <sequenceFlow id="flow3" sourceRef="decision" targetRef="hrApproval">
                      <conditionExpression>${approved == true}</conditionExpression>
                    </sequenceFlow>
                    <sequenceFlow id="flow4" sourceRef="decision" targetRef="rejectedEnd">
                      <conditionExpression>${approved == false}</conditionExpression>
                    </sequenceFlow>
                    <sequenceFlow id="flow5" sourceRef="hrApproval" targetRef="approvedEnd"/>
                  </process>
                  
                </definitions>
                """;
    }

    /**
     * 创建请假审批BPMN流程定义V2
     */
    private String createLeaveApprovalBpmnV2() {
        return """
                <?xml version="1.0" encoding="UTF-8"?>
                <definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
                           xmlns:flowable="http://flowable.org/bpmn"
                           targetNamespace="http://www.flowable.org/processdef">
                
                  <process id="leaveApproval" name="请假审批流程V2" isExecutable="true">
                    <startEvent id="startEvent" name="提交请假申请"/>
                    <userTask id="managerApproval" name="经理审批" flowable:assignee="${manager}"/>
                    <userTask id="directorApproval" name="总监审批" flowable:assignee="${director}"/>
                    <exclusiveGateway id="decision" name="审批决策"/>
                    <userTask id="hrApproval" name="HR审批" flowable:assignee="${hr}"/>
                    <endEvent id="approvedEnd" name="审批通过"/>
                    <endEvent id="rejectedEnd" name="审批拒绝"/>
                    
                    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="managerApproval"/>
                    <sequenceFlow id="flow2" sourceRef="managerApproval" targetRef="directorApproval"/>
                    <sequenceFlow id="flow3" sourceRef="directorApproval" targetRef="decision"/>
                    <sequenceFlow id="flow4" sourceRef="decision" targetRef="hrApproval">
                      <conditionExpression>${approved == true}</conditionExpression>
                    </sequenceFlow>
                    <sequenceFlow id="flow5" sourceRef="decision" targetRef="rejectedEnd">
                      <conditionExpression>${approved == false}</conditionExpression>
                    </sequenceFlow>
                    <sequenceFlow id="flow6" sourceRef="hrApproval" targetRef="approvedEnd"/>
                  </process>
                  
                </definitions>
                """;
    }
}
