package com.hzwangda.edu.common.security;

import jakarta.servlet.http.HttpServletRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 敏感数据处理切面（Web层）
 * 处理Web请求中的敏感数据加密和脱敏
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Aspect
@Component
public class SensitiveDataAspect {

    private static final Logger logger = LoggerFactory.getLogger(SensitiveDataAspect.class);

    @Autowired
    private SensitiveDataProcessor sensitiveDataProcessor;

    /**
     * 拦截Repository的save方法，对敏感数据进行加密
     */
    @Around("execution(* org.springframework.data.repository.CrudRepository.save(..))")
    public Object encryptBeforeSave(ProceedingJoinPoint joinPoint) throws Throwable {
        return sensitiveDataProcessor.processBeforeSave(joinPoint);
    }

    /**
     * 拦截Repository的find方法，对敏感数据进行解密
     */
    @Around("execution(* org.springframework.data.repository.CrudRepository.find*(..))")
    public Object decryptAfterFind(ProceedingJoinPoint joinPoint) throws Throwable {
        return sensitiveDataProcessor.processAfterFind(joinPoint);
    }

    /**
     * 拦截Controller方法，对返回的敏感数据进行脱敏
     */
    @Around("execution(* com.hky.hr.*.controller.*.*(..))")
    public Object maskSensitiveDataInResponse(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = joinPoint.proceed();

        if (result != null) {
            // 检查是否需要脱敏（可以通过请求头或用户权限判断）
            if (shouldMaskData(joinPoint)) {
                sensitiveDataProcessor.maskSensitiveFields(result);
            }
        }

        return result;
    }

    /**
     * 判断是否需要脱敏数据
     */
    private boolean shouldMaskData(ProceedingJoinPoint joinPoint) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();

                // 检查请求头中的脱敏标识
                String maskHeader = request.getHeader("X-Mask-Sensitive-Data");
                if ("false".equalsIgnoreCase(maskHeader)) {
                    return false;
                }

                // 检查用户权限（这里可以根据实际业务逻辑实现）
                String userRole = request.getHeader("X-User-Role");
                if ("ADMIN".equalsIgnoreCase(userRole) || "SUPER_ADMIN".equalsIgnoreCase(userRole)) {
                    return false; // 管理员不脱敏
                }

                // 默认需要脱敏
                return true;
            }
        } catch (Exception e) {
            logger.warn("判断脱敏条件时发生异常", e);
        }

        // 默认需要脱敏
        return true;
    }
}
