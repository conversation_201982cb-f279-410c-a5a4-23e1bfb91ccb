package com.hzwangda.edu.common.exception;

import com.hzwangda.edu.common.result.Result;
import com.hzwangda.edu.common.result.ResultCode;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * 全局异常处理器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleBusinessException(BusinessException e, HttpServletRequest request) {
        String traceId = generateTraceId();
        logger.warn("业务异常 [{}]: {}", traceId, e.getMessage(), e);

        Result<Void> result = Result.error(e.getCode(), e.getMessage());
        result.setPath(request.getRequestURI());
        result.setTraceId(traceId);
        return result;
    }

    /**
     * 处理参数验证异常 - @Valid
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    public Result<List<ValidationError>> handleMethodArgumentNotValidException(
            MethodArgumentNotValidException e, HttpServletRequest request) {
        String traceId = generateTraceId();
        logger.warn("参数验证异常 [{}]: {}", traceId, e.getMessage());

        List<ValidationError> errors = new ArrayList<>();
        for (FieldError fieldError : e.getBindingResult().getFieldErrors()) {
            errors.add(new ValidationError(fieldError.getField(), fieldError.getDefaultMessage()));
        }

        Result<List<ValidationError>> result = Result.error(
                ResultCode.UNPROCESSABLE_ENTITY.getCode(),
                "请求参数验证失败");
        result.setData(errors);
        result.setPath(request.getRequestURI());
        result.setTraceId(traceId);
        return result;
    }

    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    public Result<List<ValidationError>> handleBindException(BindException e, HttpServletRequest request) {
        String traceId = generateTraceId();
        logger.warn("参数绑定异常 [{}]: {}", traceId, e.getMessage());

        List<ValidationError> errors = new ArrayList<>();
        for (FieldError fieldError : e.getBindingResult().getFieldErrors()) {
            errors.add(new ValidationError(fieldError.getField(), fieldError.getDefaultMessage()));
        }

        Result<List<ValidationError>> result = Result.error(
                ResultCode.UNPROCESSABLE_ENTITY.getCode(),
                "请求参数验证失败");
        result.setData(errors);
        result.setPath(request.getRequestURI());
        result.setTraceId(traceId);
        return result;
    }

    /**
     * 处理约束违反异常 - @Validated
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.UNPROCESSABLE_ENTITY)
    public Result<List<ValidationError>> handleConstraintViolationException(
            ConstraintViolationException e, HttpServletRequest request) {
        String traceId = generateTraceId();
        logger.warn("约束违反异常 [{}]: {}", traceId, e.getMessage());

        List<ValidationError> errors = new ArrayList<>();
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        for (ConstraintViolation<?> violation : violations) {
            String propertyPath = violation.getPropertyPath().toString();
            String field = propertyPath.substring(propertyPath.lastIndexOf('.') + 1);
            errors.add(new ValidationError(field, violation.getMessage()));
        }

        Result<List<ValidationError>> result = Result.error(
                ResultCode.UNPROCESSABLE_ENTITY.getCode(),
                "请求参数验证失败");
        result.setData(errors);
        result.setPath(request.getRequestURI());
        result.setTraceId(traceId);
        return result;
    }

    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Void> handleMethodArgumentTypeMismatchException(
            MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        String traceId = generateTraceId();
        logger.warn("参数类型不匹配异常 [{}]: {}", traceId, e.getMessage());

        Result<Void> result = Result.error(
                ResultCode.BAD_REQUEST.getCode(),
                String.format("参数 '%s' 类型不正确", e.getName()));
        result.setPath(request.getRequestURI());
        result.setTraceId(traceId);
        return result;
    }

    /**
     * 处理其他未知异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result<Void> handleException(Exception e, HttpServletRequest request) {
        String traceId = generateTraceId();
        logger.error("系统异常 [{}]: {}", traceId, e.getMessage(), e);

        Result<Void> result = Result.error(ResultCode.INTERNAL_SERVER_ERROR);
        result.setPath(request.getRequestURI());
        result.setTraceId(traceId);
        return result;
    }

    /**
     * 处理自定义异常，不打印堆栈信息
     */
    @ExceptionHandler(CustomException.class)
    public Result<Void> customException(CustomException e, HttpServletRequest request) {
        String traceId = generateTraceId();
        logger.warn("处理自定义 [{}]", traceId, e.getMessage());

        Result<Void> result = Result.error(e.getCode(), e.getMessage());
        result.setPath(request.getRequestURI());
        result.setTraceId(traceId);
        return result;
    }

    /**
     * 生成追踪ID
     */
    private String generateTraceId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }

    /**
     * 验证错误详情
     */
    public static class ValidationError {
        private String field;
        private String message;

        public ValidationError(String field, String message) {
            this.field = field;
            this.message = message;
        }

        public String getField() {
            return field;
        }

        public void setField(String field) {
            this.field = field;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }
}
