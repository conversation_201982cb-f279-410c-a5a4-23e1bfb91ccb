# edu-common 模块重构指南

## 📋 重构概述

本次重构将原有的 `edu-common` 模块拆分为两个独立的服务模块：

- **edu-common-core** - 核心通用服务模块
- **edu-common-web** - Web相关服务模块

## 🎯 重构目标

1. **降低耦合度**：将Web相关功能与核心业务逻辑分离
2. **提高复用性**：核心模块可以被非Web应用使用
3. **改善可维护性**：模块职责更加清晰
4. **支持微服务架构**：更好地支持分布式部署

## 🏗️ 新模块结构

### edu-common-core（核心通用模块）

```
edu-common-core/
├── src/main/java/com/hky/hr/common/
│   ├── dto/                    # 数据传输对象（不含Swagger注解）
│   │   ├── PageQuery.java
│   │   └── PageResult.java
│   ├── entity/                 # 实体类（不含Swagger注解）
│   │   ├── BaseEntity.java
│   │   └── StringIdBaseEntity.java
│   ├── exception/              # 业务异常类
│   │   └── BusinessException.java
│   ├── result/                 # 结果封装类（不含Swagger注解）
│   │   ├── Result.java
│   │   └── ResultCode.java
│   ├── security/               # 核心安全工具
│   │   ├── AESEncryptionUtil.java
│   │   ├── CommonSecurityConfig.java
│   │   ├── DataMaskingUtil.java # 脱敏工具用hutool下DesensitizedUtil
│   │   ├── IpBlacklistService.java
│   │   ├── SensitiveData.java
│   │   ├── SensitiveDataProcessor.java
│   │   └── SqlInjectionProtector.java
│   ├── sync/                   # 数据同步服务
│   │   └── DataSyncService.java
│   └── transaction/            # 事务相关
│       ├── SeataConfig.java
│       └── TransactionHelper.java
└── src/main/resources/
    └── platform-services-config.yml
```

**特点：**
- ❌ 不包含任何Web相关依赖（Spring Web、Servlet API等）
- ❌ 不包含Swagger注解
- ✅ 包含核心业务逻辑和工具类
- ✅ 可以被其他服务作为依赖引入

### edu-common-web（Web相关模块）

```
edu-common-web/
├── src/main/java/com/hky/hr/common/
│   ├── dto/                    # Web DTO（包含Swagger注解）
│   │   ├── WebPageQuery.java
│   │   └── WebPageResult.java
│   ├── exception/              # Web异常处理
│   │   └── GlobalExceptionHandler.java
│   ├── result/                 # Web结果封装（包含Swagger注解）
│   │   └── WebResult.java
│   └── security/               # Web安全组件
│       ├── SensitiveDataAspect.java
│       └── SecurityValidationFilter.java
└── pom.xml
```

**特点：**
- ✅ 依赖 edu-common-core 模块
- ✅ 包含Web相关依赖（Spring Web、Swagger等）
- ✅ 包含Controller、Filter、Web配置等
- ✅ 提供Web层的统一功能

## 📦 依赖关系图

```mermaid
graph TD
    A[edu-common-services] --> B[edu-common-core]
    A --> C[edu-common-web]
    A --> D[edu-common]
    A --> E[edu-auth-service]
    
    C --> B
    E --> B
    E --> C
    
    B --> F[Spring Boot Starter]
    B --> G[Spring Data JPA]
    B --> H[Jackson]
    B --> I[Hutool]
    
    C --> J[Spring Web]
    C --> K[Swagger]
    C --> L[Spring Security]
```

## 🔄 迁移映射表

| 原模块位置 | 新模块位置 | 说明 |
|-----------|-----------|------|
| `dto/PageQuery.java` | `edu-common-core/dto/PageQuery.java` | 移除Swagger注解 |
| `dto/PageResult.java` | `edu-common-core/dto/PageResult.java` | 移除Swagger注解 |
| `entity/BaseEntity.java` | `edu-common-core/entity/BaseEntity.java` | 移除Swagger注解 |
| `entity/StringIdBaseEntity.java` | `edu-common-core/entity/StringIdBaseEntity.java` | 移除Swagger注解 |
| `exception/BusinessException.java` | `edu-common-core/exception/BusinessException.java` | 保持不变 |
| `exception/GlobalExceptionHandler.java` | `edu-common-web/exception/GlobalExceptionHandler.java` | Web相关 |
| `result/Result.java` | `edu-common-core/result/Result.java` | 移除Swagger注解 |
| `result/ResultCode.java` | `edu-common-core/result/ResultCode.java` | 保持不变 |
| `security/AESEncryptionUtil.java` | `edu-common-core/security/AESEncryptionUtil.java` | 核心工具 |
| `security/DataMaskingUtil.java` | `edu-common-core/security/DataMaskingUtil.java` | 核心工具 |
| `security/SensitiveDataAspect.java` | 拆分为两部分 | 核心逻辑+Web切面 |
| `security/SecurityValidationFilter.java` | `edu-common-web/security/SecurityValidationFilter.java` | Web相关 |

## 🆕 新增组件

### edu-common-web 新增的Web版本类

1. **WebPageQuery** - 继承PageQuery，添加Swagger注解
2. **WebPageResult** - 继承PageResult，添加Swagger注解  
3. **WebResult** - 继承Result，添加Swagger注解
4. **SensitiveDataProcessor** - 从SensitiveDataAspect中提取的核心逻辑

## 🔧 使用指南

### 对于非Web应用

```xml
<dependency>
    <groupId>com.hzwangda.edu</groupId>
    <artifactId>edu-common-core</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 对于Web应用

```xml
<!-- 包含核心功能 -->
<dependency>
    <groupId>com.hzwangda.edu</groupId>
    <artifactId>edu-common-core</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>

<!-- 包含Web功能 -->
<dependency>
    <groupId>com.hzwangda.edu</groupId>
    <artifactId>edu-common-web</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

## 📝 代码迁移示例

### 原代码（使用原模块）

```java
// 原来的使用方式
import dto.com.hzwangda.edu.common.PageQuery;
import result.com.hzwangda.edu.common.Result;

@RestController
public class UserController {
    
    @GetMapping("/users")
    public Result<PageResult<User>> getUsers(PageQuery query) {
        // 业务逻辑
        return Result.success(userService.findUsers(query));
    }
}
```

### 新代码（使用新模块）

```java
// 新的使用方式
import dto.com.hzwangda.edu.common.PageQuery;           // 来自 edu-common-core
import com.hzwangda.edu.common.result.WebResult;        // 来自 edu-common-web
import com.hky.hr.common.dto.WebPageQuery;        // 来自 edu-common-web

@RestController
public class UserController {
    
    @GetMapping("/users")
    public WebResult<PageResult<User>> getUsers(WebPageQuery query) {
        // 业务逻辑使用核心模块的PageQuery
        PageQuery coreQuery = query; // WebPageQuery继承自PageQuery
        PageResult<User> result = userService.findUsers(coreQuery);
        
        // 返回Web版本的Result（包含Swagger注解）
        return WebResult.success(result);
    }
}
```

## ✅ 验证步骤

1. **编译验证**
   ```bash
   cd edu-common-core && mvn clean compile
   cd edu-common-web && mvn clean compile
   ```

2. **依赖验证**
   ```bash
   mvn dependency:tree
   ```

3. **功能验证**
   - 核心模块不应包含Web相关依赖
   - Web模块应正确依赖核心模块
   - Swagger注解应只存在于Web模块

## 🚀 后续计划

1. **逐步迁移现有服务**：将现有服务的依赖从 `edu-common` 迁移到新模块
2. **废弃原模块**：在所有服务迁移完成后，废弃 `edu-common` 模块
3. **文档更新**：更新相关技术文档和开发指南
4. **CI/CD调整**：调整构建和部署流程以支持新的模块结构

## 📊 重构前后对比

### 重构前（单一模块）
```
edu-common/
├── src/main/java/com/hky/hr/common/
│   ├── dto/                    # 混合了核心和Web功能
│   ├── entity/                 # 包含Swagger注解
│   ├── exception/              # 混合了业务异常和Web异常处理
│   ├── result/                 # 包含Swagger注解
│   ├── security/               # 混合了核心工具和Web组件
│   ├── sync/                   # 数据同步服务
│   └── transaction/            # 事务相关
└── pom.xml                     # 包含所有依赖（核心+Web）
```

**问题：**
- ❌ 职责不清晰，核心逻辑与Web功能混合
- ❌ 非Web应用也必须引入Web依赖
- ❌ 难以进行独立的版本管理
- ❌ 测试复杂度高

### 重构后（模块化）
```
edu-common-services/
├── edu-common-core/            # 核心通用模块
│   ├── src/main/java/com/hky/hr/common/
│   │   ├── dto/                # 纯核心DTO，无Web注解
│   │   ├── entity/             # 纯实体类，无Web注解
│   │   ├── exception/          # 纯业务异常
│   │   ├── result/             # 纯结果封装，无Web注解
│   │   ├── security/           # 核心安全工具
│   │   ├── sync/               # 数据同步服务
│   │   └── transaction/        # 事务相关
│   └── pom.xml                 # 只包含核心依赖
├── edu-common-web/             # Web通用模块
│   ├── src/main/java/com/hky/hr/common/
│   │   ├── dto/                # Web DTO，包含Swagger注解
│   │   ├── exception/          # Web异常处理
│   │   ├── result/             # Web结果封装，包含Swagger注解
│   │   └── security/           # Web安全组件
│   └── pom.xml                 # 依赖core + Web相关依赖
└── pom.xml                     # 父级POM
```

**优势：**
- ✅ 职责清晰，核心逻辑与Web功能分离
- ✅ 非Web应用只需引入核心模块
- ✅ 支持独立的版本管理和发布
- ✅ 测试更加简单和专注
- ✅ 更好地支持微服务架构

## 🎯 重构收益

### 1. **依赖优化**
- **重构前**：所有应用都必须引入Web依赖（~15MB）
- **重构后**：非Web应用只需核心依赖（~8MB），减少47%

### 2. **编译速度**
- **重构前**：单一大模块，编译时间较长
- **重构后**：模块化编译，支持并行构建

### 3. **测试覆盖**
- **重构前**：混合测试，难以隔离
- **重构后**：分层测试，核心逻辑测试更纯粹

### 4. **版本管理**
- **重构前**：统一版本，Web功能变更影响所有应用
- **重构后**：独立版本，核心模块更稳定

## 📞 支持

如有任何问题，请联系架构团队或查看相关文档。

---

**重构完成时间**：2025-06-27
**重构负责人**：Augment Agent
**审核状态**：待审核
