package com.hzwangda.edu.common.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 基于String ID的基础实体类（核心模块版本）
 * 包含通用字段：String ID、创建时间、创建人、更新时间、更新人、删除标记
 * 使用UUID作为主键，适用于分布式系统和微服务架构
 *
 * <AUTHOR>
 * @since 2.0.0
 */
@Data
public abstract class BaseEntityDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String createBy;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    private String updateBy;

    private Boolean deleted = Boolean.FALSE;

    private Long version;

    // FastJSON2 序列化时，将Long ID转换为String
    public String writeId() {
        return id == null ? null : id.toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BaseEntityDTO that = (BaseEntityDTO) o;
        return id != null && id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
