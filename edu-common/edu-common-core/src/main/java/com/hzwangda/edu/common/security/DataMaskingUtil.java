package com.hzwangda.edu.common.security;

import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.regex.Pattern;

/**
 * 数据脱敏工具类
 * 用于敏感数据的脱敏处理
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Component
public class DataMaskingUtil {

    // 身份证号正则表达式
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$");

    // 手机号正则表达式
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    // 邮箱正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

    // 银行卡号正则表达式
    private static final Pattern BANK_CARD_PATTERN = Pattern.compile("^[1-9]\\d{15,18}$");

    /**
     * 身份证号脱敏
     * 显示前6位和后4位，中间用*代替
     * 例：330106199001011234 -> 330106********1234
     *
     * @param idCard 身份证号
     * @return 脱敏后的身份证号
     */
    public String maskIdCard(String idCard) {
        if (!StringUtils.hasText(idCard)) {
            return idCard;
        }

        // 验证身份证号格式
        if (!ID_CARD_PATTERN.matcher(idCard).matches()) {
            return idCard; // 格式不正确，不进行脱敏
        }

        if (idCard.length() == 18) {
            return idCard.substring(0, 6) + "********" + idCard.substring(14);
        } else if (idCard.length() == 15) {
            return idCard.substring(0, 6) + "*****" + idCard.substring(11);
        }

        return idCard;
    }

    /**
     * 手机号脱敏
     * 显示前3位和后4位，中间用*代替
     * 例：*********** -> 138****5678
     *
     * @param phone 手机号
     * @return 脱敏后的手机号
     */
    public String maskPhone(String phone) {
        if (!StringUtils.hasText(phone)) {
            return phone;
        }

        // 验证手机号格式
        if (!PHONE_PATTERN.matcher(phone).matches()) {
            return phone; // 格式不正确，不进行脱敏
        }

        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    /**
     * 邮箱脱敏
     * 显示用户名前2位和@后的域名，中间用*代替
     * 例：<EMAIL> -> zh****@example.com
     *
     * @param email 邮箱
     * @return 脱敏后的邮箱
     */
    public String maskEmail(String email) {
        if (!StringUtils.hasText(email)) {
            return email;
        }

        // 验证邮箱格式
        if (!EMAIL_PATTERN.matcher(email).matches()) {
            return email; // 格式不正确，不进行脱敏
        }

        int atIndex = email.indexOf('@');
        if (atIndex <= 2) {
            return email; // 用户名太短，不进行脱敏
        }

        String username = email.substring(0, atIndex);
        String domain = email.substring(atIndex);

        if (username.length() <= 4) {
            return username.substring(0, 2) + "**" + domain;
        } else {
            return username.substring(0, 2) + "****" + domain;
        }
    }

    /**
     * 银行卡号脱敏
     * 显示前4位和后4位，中间用*代替
     * 例：**************** -> 6222********7890
     *
     * @param bankCard 银行卡号
     * @return 脱敏后的银行卡号
     */
    public String maskBankCard(String bankCard) {
        if (!StringUtils.hasText(bankCard)) {
            return bankCard;
        }

        // 验证银行卡号格式
        if (!BANK_CARD_PATTERN.matcher(bankCard).matches()) {
            return bankCard; // 格式不正确，不进行脱敏
        }

        if (bankCard.length() < 8) {
            return bankCard; // 太短，不进行脱敏
        }

        int maskLength = bankCard.length() - 8;
        StringBuilder masked = new StringBuilder();
        masked.append(bankCard.substring(0, 4));
        for (int i = 0; i < maskLength; i++) {
            masked.append('*');
        }
        masked.append(bankCard.substring(bankCard.length() - 4));

        return masked.toString();
    }

    /**
     * 姓名脱敏
     * 显示姓氏，名字用*代替
     * 例：张三 -> 张*，李小明 -> 李**
     *
     * @param name 姓名
     * @return 脱敏后的姓名
     */
    public String maskName(String name) {
        if (!StringUtils.hasText(name)) {
            return name;
        }

        if (name.length() == 1) {
            return name; // 单字名不脱敏
        } else if (name.length() == 2) {
            return name.substring(0, 1) + "*";
        } else {
            StringBuilder masked = new StringBuilder();
            masked.append(name.substring(0, 1));
            for (int i = 1; i < name.length(); i++) {
                masked.append('*');
            }
            return masked.toString();
        }
    }

    /**
     * 地址脱敏
     * 显示省市，详细地址用*代替
     * 例：浙江省杭州市西湖区文三路123号 -> 浙江省杭州市****
     *
     * @param address 地址
     * @return 脱敏后的地址
     */
    public String maskAddress(String address) {
        if (!StringUtils.hasText(address)) {
            return address;
        }

        // 查找省市信息
        int provinceIndex = address.indexOf("省");
        int cityIndex = address.indexOf("市");

        if (provinceIndex > 0 && cityIndex > provinceIndex) {
            return address.substring(0, cityIndex + 1) + "****";
        } else if (cityIndex > 0) {
            return address.substring(0, cityIndex + 1) + "****";
        } else {
            // 没有明确的省市信息，显示前几个字符
            if (address.length() <= 6) {
                return address.substring(0, address.length() / 2) + "****";
            } else {
                return address.substring(0, 6) + "****";
            }
        }
    }

    /**
     * 通用脱敏方法
     * 根据数据类型自动选择脱敏策略
     *
     * @param data 原始数据
     * @param dataType 数据类型
     * @return 脱敏后的数据
     */
    public String maskData(String data, DataType dataType) {
        if (dataType == null) {
            return data;
        }

        switch (dataType) {
            case ID_CARD:
                return maskIdCard(data);
            case PHONE:
                return maskPhone(data);
            case EMAIL:
                return maskEmail(data);
            case BANK_CARD:
                return maskBankCard(data);
            case NAME:
                return maskName(data);
            case ADDRESS:
                return maskAddress(data);
            default:
                return data;
        }
    }

    /**
     * 自动检测数据类型并脱敏
     *
     * @param data 原始数据
     * @return 脱敏后的数据
     */
    public String autoMask(String data) {
        if (!StringUtils.hasText(data)) {
            return data;
        }

        // 按照匹配度从高到低检测
        if (ID_CARD_PATTERN.matcher(data).matches()) {
            return maskIdCard(data);
        } else if (PHONE_PATTERN.matcher(data).matches()) {
            return maskPhone(data);
        } else if (EMAIL_PATTERN.matcher(data).matches()) {
            return maskEmail(data);
        } else if (BANK_CARD_PATTERN.matcher(data).matches()) {
            return maskBankCard(data);
        } else {
            // 默认按照姓名处理
            return maskName(data);
        }
    }

    /**
     * 检查数据是否需要脱敏
     *
     * @param data 数据
     * @return 是否需要脱敏
     */
    public boolean needsMasking(String data) {
        if (!StringUtils.hasText(data)) {
            return false;
        }

        return ID_CARD_PATTERN.matcher(data).matches() ||
               PHONE_PATTERN.matcher(data).matches() ||
               EMAIL_PATTERN.matcher(data).matches() ||
               BANK_CARD_PATTERN.matcher(data).matches();
    }

    /**
     * 数据类型枚举
     */
    public enum DataType {
        ID_CARD,    // 身份证号
        PHONE,      // 手机号
        EMAIL,      // 邮箱
        BANK_CARD,  // 银行卡号
        NAME,       // 姓名
        ADDRESS     // 地址
    }
}
