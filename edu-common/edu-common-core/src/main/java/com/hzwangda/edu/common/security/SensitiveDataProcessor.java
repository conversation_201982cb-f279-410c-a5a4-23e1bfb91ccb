package com.hzwangda.edu.common.security;

import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.List;

/**
 * 敏感数据处理器（核心逻辑）
 * 提供敏感数据加密和脱敏的核心功能
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Component
public class SensitiveDataProcessor {

    private static final Logger logger = LoggerFactory.getLogger(SensitiveDataProcessor.class);

    @Autowired
    private AESEncryptionUtil encryptionUtil;

    @Autowired
    private DataMaskingUtil maskingUtil;

    /**
     * 处理Repository保存前的数据加密
     */
    public Object processBeforeSave(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();

        if (args != null && args.length > 0) {
            Object entity = args[0];
            if (entity != null) {
                encryptSensitiveFields(entity);
            }
        }

        return joinPoint.proceed();
    }

    /**
     * 处理查询后的数据解密
     */
    public Object processAfterFind(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = joinPoint.proceed();

        if (result != null) {
            if (result instanceof List) {
                List<?> list = (List<?>) result;
                for (Object item : list) {
                    decryptSensitiveFields(item);
                }
            } else {
                decryptSensitiveFields(result);
            }
        }

        return result;
    }

    /**
     * 加密敏感字段
     */
    public void encryptSensitiveFields(Object entity) {
        if (entity == null) {
            return;
        }

        Class<?> clazz = entity.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            if (field.isAnnotationPresent(SensitiveData.class)) {
                SensitiveData annotation = field.getAnnotation(SensitiveData.class);

                try {
                    field.setAccessible(true);
                    Object value = field.get(entity);

                    if (value instanceof String && !((String) value).isEmpty()) {
                        String encryptedValue = encryptionUtil.encrypt((String) value);
                        field.set(entity, encryptedValue);

                        logger.debug("已加密字段: {}.{}", clazz.getSimpleName(), field.getName());
                    }
                } catch (Exception e) {
                    logger.error("加密字段失败: {}.{}", clazz.getSimpleName(), field.getName(), e);
                }
            }
        }
    }

    /**
     * 解密敏感字段
     */
    public void decryptSensitiveFields(Object entity) {
        if (entity == null) {
            return;
        }

        Class<?> clazz = entity.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            if (field.isAnnotationPresent(SensitiveData.class)) {
                try {
                    field.setAccessible(true);
                    Object value = field.get(entity);

                    if (value instanceof String && !((String) value).isEmpty()) {
                        String decryptedValue = encryptionUtil.decrypt((String) value);
                        field.set(entity, decryptedValue);

                        logger.debug("已解密字段: {}.{}", clazz.getSimpleName(), field.getName());
                    }
                } catch (Exception e) {
                    logger.error("解密字段失败: {}.{}", clazz.getSimpleName(), field.getName(), e);
                }
            }
        }
    }

    /**
     * 脱敏敏感字段
     */
    public void maskSensitiveFields(Object entity) {
        if (entity == null) {
            return;
        }

        Class<?> clazz = entity.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            if (field.isAnnotationPresent(SensitiveData.class)) {
                SensitiveData annotation = field.getAnnotation(SensitiveData.class);

                try {
                    field.setAccessible(true);
                    Object value = field.get(entity);

                    if (value instanceof String && !((String) value).isEmpty()) {
                        // 使用默认的脱敏方式，具体类型可以通过注解的其他属性来确定
                        String maskedValue = maskingUtil.maskData((String) value, DataMaskingUtil.DataType.NAME);
                        field.set(entity, maskedValue);

                        logger.debug("已脱敏字段: {}.{}", clazz.getSimpleName(), field.getName());
                    }
                } catch (Exception e) {
                    logger.error("脱敏字段失败: {}.{}", clazz.getSimpleName(), field.getName(), e);
                }
            }
        }
    }
}
