package com.hzwangda.edu.common.security;

import java.lang.annotation.*;

/**
 * 敏感数据注解
 * 用于标记需要加密存储的敏感字段
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SensitiveData {

    /**
     * 敏感数据类型
     */
    SensitiveType value() default SensitiveType.PERSONAL;

    /**
     * 是否启用加密
     * 默认启用
     */
    boolean encrypt() default true;

    /**
     * 是否启用脱敏
     * 默认启用
     */
    boolean mask() default true;

    /**
     * 脱敏策略
     */
    MaskStrategy maskStrategy() default MaskStrategy.AUTO;

    /**
     * 敏感数据类型枚举
     */
    enum SensitiveType {
        PERSONAL,       // 个人信息
        IDENTITY,       // 身份信息
        CONTACT,        // 联系方式
        FINANCIAL,      // 财务信息
        MEDICAL,        // 医疗信息
        EDUCATION,      // 教育信息
        WORK,           // 工作信息
        OTHER           // 其他
    }

    /**
     * 脱敏策略枚举
     */
    enum MaskStrategy {
        AUTO,           // 自动检测
        ID_CARD,        // 身份证号
        PHONE,          // 手机号
        EMAIL,          // 邮箱
        BANK_CARD,      // 银行卡号
        NAME,           // 姓名
        ADDRESS,        // 地址
        CUSTOM          // 自定义
    }
}
