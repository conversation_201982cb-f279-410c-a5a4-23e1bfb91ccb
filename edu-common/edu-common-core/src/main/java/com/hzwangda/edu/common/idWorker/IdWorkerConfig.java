package com.hzwangda.edu.common.idWorker;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/2/21
 * @description IdWorker分布式负载配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "idworker")
public class IdWorkerConfig {

    /**
     * (0~3)-(0~3)
     */
    private String datacenterWorkerIds;
}
