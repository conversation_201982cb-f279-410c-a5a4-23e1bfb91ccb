package com.hzwangda.edu.common.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 通用安全配置类（核心模块）
 * 整合核心安全功能的配置，不包含Web相关功能
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Configuration
@EnableScheduling
@ConditionalOnProperty(name = "hky.security.enabled", havingValue = "true", matchIfMissing = true)
public class CommonSecurityConfig {

    private static final Logger logger = LoggerFactory.getLogger(CommonSecurityConfig.class);

    @Autowired
    private AESEncryptionUtil encryptionUtil;

    @Autowired
    private IpBlacklistService ipBlacklistService;

    /**
     * 应用启动后的安全初始化
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        logger.info("开始核心安全模块初始化...");

        try {
            // 验证加密配置
            boolean encryptionValid = encryptionUtil.validateConfiguration();
            if (!encryptionValid) {
                logger.error("加密配置验证失败，请检查配置");
            } else {
                logger.info("加密配置验证成功");
            }

            // 清理过期的黑名单条目
            ipBlacklistService.cleanupExpiredEntries();

            // 输出安全统计信息
            IpBlacklistService.BlacklistStats stats = ipBlacklistService.getBlacklistStats();
            logger.info("当前黑名单IP数量: {}", stats.getTotalBlacklisted());

            logger.info("核心安全模块初始化完成");

        } catch (Exception e) {
            logger.error("核心安全模块初始化失败", e);
        }
    }

    /**
     * 定期清理过期的黑名单条目
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void cleanupExpiredBlacklistEntries() {
        try {
            logger.debug("开始清理过期的黑名单条目...");
            ipBlacklistService.cleanupExpiredEntries();
            logger.debug("黑名单清理完成");
        } catch (Exception e) {
            logger.error("清理黑名单条目时发生错误", e);
        }
    }

    /**
     * 定期输出安全统计信息
     * 每天执行一次
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点
    public void logSecurityStats() {
        try {
            IpBlacklistService.BlacklistStats stats = ipBlacklistService.getBlacklistStats();
            logger.info("=== 安全统计信息 ===");
            logger.info("黑名单IP总数: {}", stats.getTotalBlacklisted());
            logger.info("==================");
        } catch (Exception e) {
            logger.error("输出安全统计信息时发生错误", e);
        }
    }
}
