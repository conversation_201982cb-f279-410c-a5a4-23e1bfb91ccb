package com.hzwangda.edu.common.sync;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 默认数据同步服务实现
 * 基于Redis实现的数据同步机制
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Service
public class DefaultDataSyncService implements DataSyncService {

    private static final Logger logger = LoggerFactory.getLogger(DefaultDataSyncService.class);

    @Autowired
    private ReactiveStringRedisTemplate redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    // 同步状态缓存
    private final Map<String, SyncStatus> syncStatusCache = new ConcurrentHashMap<>();

    // Redis键前缀
    private static final String SYNC_KEY_PREFIX = "data_sync:";
    private static final String CONSISTENCY_KEY_PREFIX = "consistency:";
    private static final String SYNC_STATUS_KEY_PREFIX = "sync_status:";

    @Override
    public SyncResult syncEntity(String entityType, String entityId, Map<String, Object> data, SyncOperation operation) {
        String syncId = generateSyncId();

        try {
            logger.info("开始同步实体数据: entityType={}, entityId={}, operation={}, syncId={}",
                       entityType, entityId, operation, syncId);

            // 更新同步状态
            updateSyncStatus(syncId, SyncStatus.IN_PROGRESS);

            // 构建同步数据
            Map<String, Object> syncData = new HashMap<>();
            syncData.put("entityType", entityType);
            syncData.put("entityId", entityId);
            syncData.put("data", data);
            syncData.put("operation", operation.name());
            syncData.put("timestamp", System.currentTimeMillis());
            syncData.put("syncId", syncId);

            // 保存到Redis
            String key = SYNC_KEY_PREFIX + entityType + ":" + entityId;
            String value = objectMapper.writeValueAsString(syncData);

            redisTemplate.opsForValue()
                    .set(key, value, Duration.ofHours(24))
                    .subscribe();

            // 发布同步事件（可以通过消息队列实现）
            publishSyncEvent(syncData);

            // 更新同步状态
            updateSyncStatus(syncId, SyncStatus.COMPLETED);

            logger.info("实体数据同步完成: syncId={}", syncId);
            return new SyncResult(true, syncId, "数据同步成功");

        } catch (Exception e) {
            logger.error("实体数据同步失败: syncId={}, error={}", syncId, e.getMessage(), e);
            updateSyncStatus(syncId, SyncStatus.FAILED);
            return new SyncResult(false, syncId, "数据同步失败: " + e.getMessage());
        }
    }

    @Override
    public BatchSyncResult batchSyncEntities(List<SyncRequest> syncRequests) {
        logger.info("开始批量同步实体数据: count={}", syncRequests.size());

        List<SyncResult> results = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;

        for (SyncRequest request : syncRequests) {
            SyncResult result = syncEntity(
                request.getEntityType(),
                request.getEntityId(),
                request.getData(),
                request.getOperation()
            );

            results.add(result);

            if (result.isSuccess()) {
                successCount++;
            } else {
                failureCount++;
            }
        }

        logger.info("批量同步完成: total={}, success={}, failure={}",
                   syncRequests.size(), successCount, failureCount);

        return new BatchSyncResult(syncRequests.size(), successCount, failureCount, results);
    }

    @Override
    public ConsistencyCheckResult checkConsistency(String entityType, String entityId) {
        logger.info("检查数据一致性: entityType={}, entityId={}", entityType, entityId);

        try {
            // 从Redis获取同步数据
            String key = SYNC_KEY_PREFIX + entityType + ":" + entityId;

            Mono<String> dataMono = redisTemplate.opsForValue().get(key);
            String syncDataStr = dataMono.block(Duration.ofSeconds(5));

            if (syncDataStr == null) {
                logger.warn("未找到同步数据: entityType={}, entityId={}", entityType, entityId);
                return new ConsistencyCheckResult(false, entityType, entityId);
            }

            // 解析同步数据
            Map<String, Object> syncData = objectMapper.readValue(syncDataStr, Map.class);

            // 这里应该调用各个服务的接口来检查数据一致性
            // 为了演示，我们假设数据是一致的
            boolean consistent = checkDataConsistencyAcrossServices(entityType, entityId, syncData);

            ConsistencyCheckResult result = new ConsistencyCheckResult(consistent, entityType, entityId);

            if (!consistent) {
                // 记录不一致的服务
                result.setInconsistentServices(Arrays.asList("service1", "service2"));
                result.setDifferences(Map.of("field1", "value1", "field2", "value2"));
            }

            logger.info("数据一致性检查完成: entityType={}, entityId={}, consistent={}",
                       entityType, entityId, consistent);

            return result;

        } catch (Exception e) {
            logger.error("数据一致性检查失败: entityType={}, entityId={}, error={}",
                        entityType, entityId, e.getMessage(), e);
            return new ConsistencyCheckResult(false, entityType, entityId);
        }
    }

    @Override
    public RepairResult repairInconsistency(String entityType, String entityId, String sourceService) {
        logger.info("修复数据不一致: entityType={}, entityId={}, sourceService={}",
                   entityType, entityId, sourceService);

        try {
            // 从源服务获取正确的数据
            Map<String, Object> correctData = fetchDataFromSourceService(sourceService, entityType, entityId);

            if (correctData == null) {
                return new RepairResult(false, "无法从源服务获取数据", 0);
            }

            // 同步到其他服务
            SyncResult syncResult = syncEntity(entityType, entityId, correctData, SyncOperation.UPDATE);

            if (syncResult.isSuccess()) {
                logger.info("数据不一致修复成功: entityType={}, entityId={}", entityType, entityId);
                return new RepairResult(true, "数据修复成功", 1);
            } else {
                logger.error("数据不一致修复失败: entityType={}, entityId={}, error={}",
                           entityType, entityId, syncResult.getMessage());
                return new RepairResult(false, "数据修复失败: " + syncResult.getMessage(), 0);
            }

        } catch (Exception e) {
            logger.error("数据不一致修复异常: entityType={}, entityId={}, error={}",
                        entityType, entityId, e.getMessage(), e);
            return new RepairResult(false, "数据修复异常: " + e.getMessage(), 0);
        }
    }

    @Override
    public SyncStatus getSyncStatus(String syncId) {
        return syncStatusCache.getOrDefault(syncId, SyncStatus.PENDING);
    }

    /**
     * 生成同步ID
     */
    private String generateSyncId() {
        return "sync_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 更新同步状态
     */
    private void updateSyncStatus(String syncId, SyncStatus status) {
        syncStatusCache.put(syncId, status);

        // 同时保存到Redis
        String key = SYNC_STATUS_KEY_PREFIX + syncId;
        redisTemplate.opsForValue()
                .set(key, status.name(), Duration.ofHours(24))
                .subscribe();
    }

    /**
     * 发布同步事件
     */
    private void publishSyncEvent(Map<String, Object> syncData) {
        try {
            // 这里可以集成消息队列（如RabbitMQ、Kafka等）来发布同步事件
            // 目前使用Redis的发布订阅功能
            String channel = "data_sync_events";
            String message = objectMapper.writeValueAsString(syncData);

            redisTemplate.convertAndSend(channel, message).subscribe();

            logger.debug("同步事件已发布: channel={}, syncId={}", channel, syncData.get("syncId"));

        } catch (Exception e) {
            logger.error("发布同步事件失败: error={}", e.getMessage(), e);
        }
    }

    /**
     * 检查跨服务数据一致性
     */
    private boolean checkDataConsistencyAcrossServices(String entityType, String entityId, Map<String, Object> syncData) {
        // 这里应该调用各个相关服务的接口来检查数据一致性
        // 为了演示，我们简化处理
        try {
            // 模拟检查逻辑
            Thread.sleep(100); // 模拟网络延迟

            // 假设90%的情况下数据是一致的
            return Math.random() > 0.1;

        } catch (Exception e) {
            logger.error("检查数据一致性时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 从源服务获取数据
     */
    private Map<String, Object> fetchDataFromSourceService(String sourceService, String entityType, String entityId) {
        // 这里应该调用源服务的接口获取数据
        // 为了演示，我们返回模拟数据
        Map<String, Object> data = new HashMap<>();
        data.put("id", entityId);
        data.put("type", entityType);
        data.put("sourceService", sourceService);
        data.put("timestamp", System.currentTimeMillis());

        return data;
    }
}
