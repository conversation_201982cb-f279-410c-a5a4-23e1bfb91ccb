package com.hzwangda.edu.common.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.hzwangda.edu.common.idWorker.IdWorker;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.sql.Update;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 基础实体类（核心模块版本）
 * 包含通用字段：ID、创建时间、创建人、更新时间、更新人、删除标记
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public abstract class BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @NotNull(groups = Update.class)
    @Column(updatable = false)
    protected Long id;

    @CreatedDate
    @Column(name = "create_time", nullable = false, updatable = false)
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    protected LocalDateTime createTime;

    @CreatedBy
    @Column(name = "create_by", length = 64, updatable = false)
    protected String createBy;

    @LastModifiedDate
    @Column(name = "update_time")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    protected LocalDateTime updateTime;

    @LastModifiedBy
    @Column(name = "update_by", length = 64)
    protected String updateBy;

    @Column(name = "deleted", nullable = false)
    protected Boolean deleted = Boolean.FALSE;

    @Version
    @Column(name = "version")
    protected Integer version;

    @PrePersist
    protected void onCreate() {
        if(this.id == null) {
            setId(IdWorker.generateId());
        }
        if (createTime == null) {
            createTime = LocalDateTime.now();
        }
        if (deleted == null) {
            deleted = Boolean.FALSE;
        }
        if (version == null) {
            version = 0;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updateTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "BaseEntity{" +
                "id=" + id +
                ", createTime=" + createTime +
                ", createBy='" + createBy + '\'' +
                ", updateTime=" + updateTime +
                ", updateBy='" + updateBy + '\'' +
                ", deleted=" + deleted +
                ", version=" + version +
                '}';
    }
}
