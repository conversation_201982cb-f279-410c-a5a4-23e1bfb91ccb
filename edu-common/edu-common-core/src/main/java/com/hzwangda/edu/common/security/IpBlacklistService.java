package com.hzwangda.edu.common.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * IP黑名单服务
 * 用于管理恶意IP的封禁和解封
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Service
public class IpBlacklistService {

    private static final Logger logger = LoggerFactory.getLogger(IpBlacklistService.class);

    @Autowired
    private StringRedisTemplate redisTemplate;

    // Redis键前缀
    private static final String BLACKLIST_KEY_PREFIX = "security:blacklist:";
    private static final String VIOLATION_COUNT_KEY_PREFIX = "security:violation:";
    private static final String RATE_LIMIT_KEY_PREFIX = "security:rate_limit:";

    // 内存缓存，提高查询性能
    private final Map<String, BlacklistEntry> blacklistCache = new ConcurrentHashMap<>();
    private volatile long lastCacheUpdate = 0;
    private static final long CACHE_REFRESH_INTERVAL = 60000; // 1分钟

    // 默认配置
    private static final int DEFAULT_MAX_VIOLATIONS = 10; // 默认最大违规次数
    private static final long DEFAULT_BAN_DURATION = 3600000; // 默认封禁时长1小时
    private static final int DEFAULT_RATE_LIMIT = 100; // 默认每分钟100次请求

    /**
     * 检查IP是否在黑名单中
     *
     * @param ip IP地址
     * @return 是否被封禁
     */
    public boolean isBlacklisted(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        // 先检查内存缓存
        BlacklistEntry entry = getFromCache(ip);
        if (entry != null) {
            if (entry.isExpired()) {
                removeFromBlacklist(ip);
                return false;
            }
            return true;
        }

        // 检查Redis
        String key = BLACKLIST_KEY_PREFIX + ip;
        String value = redisTemplate.opsForValue().get(key);

        if (value != null) {
            try {
                BlacklistEntry redisEntry = BlacklistEntry.fromString(value);
                if (redisEntry.isExpired()) {
                    removeFromBlacklist(ip);
                    return false;
                } else {
                    // 更新缓存
                    blacklistCache.put(ip, redisEntry);
                    return true;
                }
            } catch (Exception e) {
                logger.error("解析黑名单条目失败: ip={}, value={}", ip, value, e);
                return false;
            }
        }

        return false;
    }

    /**
     * 将IP添加到黑名单
     *
     * @param ip IP地址
     * @param reason 封禁原因
     * @param durationMinutes 封禁时长（分钟）
     */
    public void addToBlacklist(String ip, String reason, long durationMinutes) {
        if (ip == null || ip.isEmpty()) {
            return;
        }

        long durationMillis = durationMinutes * 60 * 1000;
        long expireTime = System.currentTimeMillis() + durationMillis;

        BlacklistEntry entry = new BlacklistEntry(ip, reason, System.currentTimeMillis(), expireTime);

        // 保存到Redis
        String key = BLACKLIST_KEY_PREFIX + ip;
        redisTemplate.opsForValue().set(key, entry.toString(), Duration.ofMillis(durationMillis));

        // 更新内存缓存
        blacklistCache.put(ip, entry);

        logger.warn("IP已添加到黑名单: ip={}, reason={}, duration={}分钟", ip, reason, durationMinutes);
    }

    /**
     * 从黑名单中移除IP
     *
     * @param ip IP地址
     */
    public void removeFromBlacklist(String ip) {
        if (ip == null || ip.isEmpty()) {
            return;
        }

        // 从Redis删除
        String key = BLACKLIST_KEY_PREFIX + ip;
        redisTemplate.delete(key);

        // 从内存缓存删除
        blacklistCache.remove(ip);

        logger.info("IP已从黑名单移除: ip={}", ip);
    }

    /**
     * 记录IP违规行为
     *
     * @param ip IP地址
     * @param violationType 违规类型
     * @return 是否应该封禁
     */
    public boolean recordViolation(String ip, ViolationType violationType) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        String key = VIOLATION_COUNT_KEY_PREFIX + ip;
        String countStr = redisTemplate.opsForValue().get(key);

        int currentCount = 0;
        if (countStr != null) {
            try {
                currentCount = Integer.parseInt(countStr);
            } catch (NumberFormatException e) {
                logger.warn("解析违规计数失败: ip={}, count={}", ip, countStr);
            }
        }

        currentCount++;

        // 设置违规计数，1小时过期
        redisTemplate.opsForValue().set(key, String.valueOf(currentCount), Duration.ofHours(1));

        logger.warn("记录IP违规: ip={}, type={}, count={}", ip, violationType, currentCount);

        // 检查是否需要封禁
        int maxViolations = getMaxViolations(violationType);
        if (currentCount >= maxViolations) {
            long banDuration = getBanDuration(violationType);
            addToBlacklist(ip, "违规次数过多: " + violationType, banDuration);

            // 清除违规计数
            redisTemplate.delete(key);

            return true;
        }

        return false;
    }

    /**
     * 检查IP访问频率
     *
     * @param ip IP地址
     * @param maxRequests 最大请求数
     * @param windowMinutes 时间窗口（分钟）
     * @return 是否超过限制
     */
    public boolean checkRateLimit(String ip, int maxRequests, int windowMinutes) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }

        String key = RATE_LIMIT_KEY_PREFIX + ip;
        String countStr = redisTemplate.opsForValue().get(key);

        int currentCount = 0;
        if (countStr != null) {
            try {
                currentCount = Integer.parseInt(countStr);
            } catch (NumberFormatException e) {
                logger.warn("解析访问计数失败: ip={}, count={}", ip, countStr);
            }
        }

        currentCount++;

        if (currentCount == 1) {
            // 第一次访问，设置过期时间
            redisTemplate.opsForValue().set(key, String.valueOf(currentCount), Duration.ofMinutes(windowMinutes));
        } else {
            // 更新计数，保持原有过期时间
            redisTemplate.opsForValue().set(key, String.valueOf(currentCount));
        }

        boolean exceeded = currentCount > maxRequests;
        if (exceeded) {
            logger.warn("IP访问频率超限: ip={}, count={}, limit={}, window={}分钟",
                       ip, currentCount, maxRequests, windowMinutes);

            // 记录违规
            recordViolation(ip, ViolationType.RATE_LIMIT_EXCEEDED);
        }

        return exceeded;
    }

    /**
     * 获取黑名单统计信息
     *
     * @return 统计信息
     */
    public BlacklistStats getBlacklistStats() {
        Set<String> keys = redisTemplate.keys(BLACKLIST_KEY_PREFIX + "*");
        int totalBlacklisted = keys != null ? keys.size() : 0;

        // 统计各种违规类型
        Map<ViolationType, Integer> violationStats = new HashMap<>();
        for (ViolationType type : ViolationType.values()) {
            violationStats.put(type, 0);
        }

        // 这里可以进一步统计详细信息
        return new BlacklistStats(totalBlacklisted, violationStats);
    }

    /**
     * 清理过期的黑名单条目
     */
    public void cleanupExpiredEntries() {
        logger.info("开始清理过期的黑名单条目");

        Set<String> keys = redisTemplate.keys(BLACKLIST_KEY_PREFIX + "*");
        if (keys == null || keys.isEmpty()) {
            return;
        }

        int cleanedCount = 0;
        for (String key : keys) {
            String value = redisTemplate.opsForValue().get(key);
            if (value != null) {
                try {
                    BlacklistEntry entry = BlacklistEntry.fromString(value);
                    if (entry.isExpired()) {
                        redisTemplate.delete(key);
                        String ip = key.substring(BLACKLIST_KEY_PREFIX.length());
                        blacklistCache.remove(ip);
                        cleanedCount++;
                    }
                } catch (Exception e) {
                    logger.error("清理黑名单条目失败: key={}", key, e);
                }
            }
        }

        logger.info("清理过期黑名单条目完成: count={}", cleanedCount);
    }

    /**
     * 从缓存获取黑名单条目
     */
    private BlacklistEntry getFromCache(String ip) {
        // 检查缓存是否需要刷新
        long now = System.currentTimeMillis();
        if (now - lastCacheUpdate > CACHE_REFRESH_INTERVAL) {
            refreshCache();
            lastCacheUpdate = now;
        }

        return blacklistCache.get(ip);
    }

    /**
     * 刷新内存缓存
     */
    private void refreshCache() {
        try {
            Set<String> keys = redisTemplate.keys(BLACKLIST_KEY_PREFIX + "*");
            if (keys == null || keys.isEmpty()) {
                blacklistCache.clear();
                return;
            }

            Map<String, BlacklistEntry> newCache = new HashMap<>();
            for (String key : keys) {
                String value = redisTemplate.opsForValue().get(key);
                if (value != null) {
                    try {
                        BlacklistEntry entry = BlacklistEntry.fromString(value);
                        if (!entry.isExpired()) {
                            String ip = key.substring(BLACKLIST_KEY_PREFIX.length());
                            newCache.put(ip, entry);
                        }
                    } catch (Exception e) {
                        logger.error("刷新缓存时解析条目失败: key={}", key, e);
                    }
                }
            }

            blacklistCache.clear();
            blacklistCache.putAll(newCache);

        } catch (Exception e) {
            logger.error("刷新黑名单缓存失败", e);
        }
    }

    /**
     * 获取违规类型对应的最大违规次数
     */
    private int getMaxViolations(ViolationType violationType) {
        switch (violationType) {
            case SQL_INJECTION:
            case XSS_ATTACK:
                return 3; // 安全攻击容忍度低
            case RATE_LIMIT_EXCEEDED:
                return 5; // 频率限制中等容忍度
            case INVALID_REQUEST:
                return 10; // 无效请求高容忍度
            default:
                return DEFAULT_MAX_VIOLATIONS;
        }
    }

    /**
     * 获取违规类型对应的封禁时长（分钟）
     */
    private long getBanDuration(ViolationType violationType) {
        switch (violationType) {
            case SQL_INJECTION:
            case XSS_ATTACK:
                return 1440; // 24小时
            case RATE_LIMIT_EXCEEDED:
                return 60; // 1小时
            case INVALID_REQUEST:
                return 30; // 30分钟
            default:
                return DEFAULT_BAN_DURATION / 60000; // 转换为分钟
        }
    }

    /**
     * 违规类型枚举
     */
    public enum ViolationType {
        SQL_INJECTION("SQL注入攻击"),
        XSS_ATTACK("XSS攻击"),
        RATE_LIMIT_EXCEEDED("访问频率超限"),
        INVALID_REQUEST("无效请求");

        private final String description;

        ViolationType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 黑名单条目
     */
    public static class BlacklistEntry {
        private String ip;
        private String reason;
        private long createTime;
        private long expireTime;

        public BlacklistEntry(String ip, String reason, long createTime, long expireTime) {
            this.ip = ip;
            this.reason = reason;
            this.createTime = createTime;
            this.expireTime = expireTime;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expireTime;
        }

        @Override
        public String toString() {
            return ip + "|" + reason + "|" + createTime + "|" + expireTime;
        }

        public static BlacklistEntry fromString(String str) {
            String[] parts = str.split("\\|");
            if (parts.length != 4) {
                throw new IllegalArgumentException("Invalid blacklist entry format: " + str);
            }
            return new BlacklistEntry(parts[0], parts[1], Long.parseLong(parts[2]), Long.parseLong(parts[3]));
        }

        // Getters
        public String getIp() { return ip; }
        public String getReason() { return reason; }
        public long getCreateTime() { return createTime; }
        public long getExpireTime() { return expireTime; }
    }

    /**
     * 黑名单统计信息
     */
    public static class BlacklistStats {
        private int totalBlacklisted;
        private Map<ViolationType, Integer> violationStats;
        private long timestamp;

        public BlacklistStats(int totalBlacklisted, Map<ViolationType, Integer> violationStats) {
            this.totalBlacklisted = totalBlacklisted;
            this.violationStats = violationStats;
            this.timestamp = System.currentTimeMillis();
        }

        // Getters
        public int getTotalBlacklisted() { return totalBlacklisted; }
        public Map<ViolationType, Integer> getViolationStats() { return violationStats; }
        public long getTimestamp() { return timestamp; }
    }
}
