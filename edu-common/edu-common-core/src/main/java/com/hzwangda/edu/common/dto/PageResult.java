package com.hzwangda.edu.common.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 分页结果封装类（核心模块版本）
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
public class PageResult<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private List<T> content;

    private Long totalElements;

    private Integer totalPages;

    private Integer page;

    private Integer size;

    private Boolean first;

    private Boolean last;

    private Boolean empty;

    public PageResult() {
    }

    public PageResult(List<T> content, Long totalElements, Integer page, Integer size) {
        this.content = content;
        this.totalElements = totalElements;
        this.page = page;
        this.size = size;
        this.totalPages = (int) Math.ceil((double) totalElements / size);
        this.first = page == 0;
        this.last = page >= totalPages - 1;
        this.empty = content == null || content.isEmpty();
    }

    public static <T> PageResult<T> of(List<T> content, Long totalElements, Integer page, Integer size) {
        return new PageResult<>(content, totalElements, page, size);
    }

    public static <T> PageResult<T> empty() {
        return new PageResult<>(List.of(), 0L, 0, 0);
    }

    @Override
    public String toString() {
        return "PageResult{" +
                "content=" + content +
                ", totalElements=" + totalElements +
                ", totalPages=" + totalPages +
                ", page=" + page +
                ", size=" + size +
                ", first=" + first +
                ", last=" + last +
                ", empty=" + empty +
                '}';
    }
}
