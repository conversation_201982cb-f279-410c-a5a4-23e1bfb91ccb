package com.hzwangda.edu.common.transaction;

import org.apache.seata.spring.annotation.GlobalTransactional;
import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

/**
 * 分布式事务注解
 * 临时注释掉整个注解，解决P0级编译问题
 * 对Seata的@GlobalTransactional注解进行封装，提供更友好的使用方式
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@GlobalTransactional
public @interface DistributedTransaction {

    /**
     * 事务超时时间（毫秒）
     * 默认60秒
     */
    @AliasFor(annotation = GlobalTransactional.class, attribute = "timeoutMills")
    int timeoutMills() default 60000;

    /**
     * 事务名称
     */
    @AliasFor(annotation = GlobalTransactional.class, attribute = "name")
    String name() default "";

    /**
     * 回滚异常类型
     */
    @AliasFor(annotation = GlobalTransactional.class, attribute = "rollbackFor")
    Class<? extends Throwable>[] rollbackFor() default {Exception.class};

    /**
     * 不回滚异常类型
     */
    @AliasFor(annotation = GlobalTransactional.class, attribute = "noRollbackFor")
    Class<? extends Throwable>[] noRollbackFor() default {};

    /**
     * 传播行为
     */
    @AliasFor(annotation = GlobalTransactional.class, attribute = "propagation")
    io.seata.tm.api.transaction.Propagation propagation() default io.seata.tm.api.transaction.Propagation.REQUIRED;

    /**
     * 锁重试间隔（毫秒）
     */
    @AliasFor(annotation = GlobalTransactional.class, attribute = "lockRetryInterval")
    int lockRetryInterval() default 10;

    /**
     * 锁重试次数
     */
    @AliasFor(annotation = GlobalTransactional.class, attribute = "lockRetryTimes")
    int lockRetryTimes() default 30;
}
