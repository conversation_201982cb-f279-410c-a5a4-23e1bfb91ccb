package com.hzwangda.edu.common.result;

import lombok.Getter;

/**
 * 统一响应码枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum ResultCode {

    // 成功响应
    SUCCESS(200, "操作成功"),
    PARTIAL_CONTENT(206, "部分内容成功"),

    // 客户端错误 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未认证"),
    FORBIDDEN(403, "无权限访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    UNPROCESSABLE_ENTITY(422, "请求参数验证失败"),

    // 服务器错误 5xx
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),

    // 业务错误码 1xxx
    BUSINESS_ERROR(1000, "业务处理错误"),
    // 认证授权相关 10xx
    AUTH_INVALID_TOKEN(1001, "无效的访问令牌"),
    AUTH_TOKEN_EXPIRED(1002, "访问令牌已过期"),
    AUTH_REFRESH_TOKEN_EXPIRED(1003, "刷新令牌已过期"),
    AUTH_INSUFFICIENT_PERMISSIONS(1004, "权限不足"),
    AUTH_ACCOUNT_DISABLED(1005, "账户已被禁用"),
    AUTH_ACCOUNT_LOCKED(1006, "账户已被锁定"),

    // 组织管理相关 11xx
    ORG_INSTITUTION_NOT_FOUND(1101, "机构不存在"),
    ORG_INSTITUTION_HAS_CHILDREN(1102, "机构下存在子机构，无法删除"),
    ORG_INSTITUTION_HAS_EMPLOYEES(1103, "机构下存在教职工，无法删除"),
    ORG_INSTITUTION_CODE_EXISTS(1104, "机构代码已存在"),
    ORG_INSTITUTION_NAME_EXISTS(1105, "机构名称已存在"),

    // 教职工信息管理相关 12xx
    EMP_EMPLOYEE_NOT_FOUND(1201, "教职工不存在"),
    EMP_EMPLOYEE_ID_EXISTS(1202, "工号已存在"),
    EMP_ID_CARD_EXISTS(1203, "身份证号已存在"),
    EMP_PHONE_EXISTS(1204, "手机号已存在"),
    EMP_EMAIL_EXISTS(1205, "邮箱已存在"),
    EMP_INVALID_STATUS(1206, "教职工状态无效"),

    // 人事变动相关 13xx
    PC_CHANGE_NOT_FOUND(1301, "人事变动记录不存在"),
    PC_INVALID_CHANGE_TYPE(1302, "无效的变动类型"),
    PC_EMPLOYEE_ALREADY_IN_PROCESS(1303, "教职工已有进行中的变动流程"),

    // 合同管理相关 14xx
    CONTRACT_NOT_FOUND(1401, "合同不存在"),
    CONTRACT_ALREADY_SIGNED(1402, "合同已签署"),
    CONTRACT_EXPIRED(1403, "合同已过期"),
    CONTRACT_INVALID_STATUS(1404, "合同状态无效"),

    // 考勤管理相关 15xx
    ATT_RECORD_NOT_FOUND(1501, "考勤记录不存在"),
    ATT_LEAVE_REQUEST_NOT_FOUND(1502, "请假申请不存在"),
    ATT_INVALID_LEAVE_TYPE(1503, "无效的请假类型"),
    ATT_LEAVE_DAYS_EXCEEDED(1504, "请假天数超出限制"),

    // 薪酬福利相关 16xx
    COMP_SALARY_RECORD_NOT_FOUND(1601, "薪酬记录不存在"),
    COMP_BENEFIT_NOT_FOUND(1602, "福利记录不存在"),
    COMP_INVALID_SALARY_STRUCTURE(1603, "无效的薪酬结构"),

    // 招聘管理相关 17xx
    REC_JOB_POSTING_NOT_FOUND(1701, "招聘岗位不存在"),
    REC_APPLICATION_NOT_FOUND(1702, "应聘申请不存在"),
    REC_INVALID_APPLICATION_STATUS(1703, "无效的申请状态"),

    // 师资发展相关 18xx
    FD_TRAINING_NOT_FOUND(1801, "培训记录不存在"),
    FD_DEVELOPMENT_PLAN_NOT_FOUND(1802, "发展计划不存在"),

    // 考核管理相关 19xx
    APP_APPRAISAL_NOT_FOUND(1901, "考核记录不存在"),
    APP_INVALID_APPRAISAL_STATUS(1902, "无效的考核状态"),
    APP_APPRAISAL_PERIOD_CONFLICT(1903, "考核周期冲突"),

    // 职称评聘相关 20xx
    TITLE_EVALUATION_NOT_FOUND(2001, "职称评聘记录不存在"),
    TITLE_INVALID_TITLE_LEVEL(2002, "无效的职称级别"),

    // 文件管理相关 21xx
    FILE_NOT_FOUND(2101, "文件不存在"),
    FILE_UPLOAD_FAILED(2102, "文件上传失败"),
    FILE_TYPE_NOT_SUPPORTED(2103, "不支持的文件类型"),
    FILE_SIZE_EXCEEDED(2104, "文件大小超出限制"),

    // 工作流相关 22xx
    WF_PROCESS_NOT_FOUND(2201, "流程实例不存在"),
    WF_TASK_NOT_FOUND(2202, "任务不存在"),
    WF_INVALID_PROCESS_STATUS(2203, "无效的流程状态"),
    WF_TASK_ALREADY_COMPLETED(2204, "任务已完成"),

    // 数据字典相关 23xx
    DICT_NOT_FOUND(2301, "字典项不存在"),
    DICT_CODE_EXISTS(2302, "字典编码已存在"),
    DICT_TYPE_NOT_FOUND(2303, "字典类型不存在"),

    // 消息相关 24xx
    TOO_MANY_REQUESTS(2401, "短信发送频率超限"),
    EXTERNAL_SERVICE_ERROR(2402, "外部服务错误"),
    INVALID_CHANNEL(2403, "无效的通知渠道");

    private final Integer code;
    private final String message;

    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
