package com.hzwangda.edu.common.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;

/**
 * 分页查询基础类（核心模块版本）
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
public class PageQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Min(value = 0, message = "页码不能小于0")
    private Integer page = 0;

    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 100, message = "每页大小不能大于100")
    private Integer size = 10;

    private String sort;

    private String direction = "desc";

    public PageQuery() {
    }

    public PageQuery(Integer page, Integer size) {
        this.page = page;
        this.size = size;
    }

    public PageQuery(Integer page, Integer size, String sort, String direction) {
        this.page = page;
        this.size = size;
        this.sort = sort;
        this.direction = direction;
    }

    @Override
    public String toString() {
        return "PageQuery{" +
                "page=" + page +
                ", size=" + size +
                ", sort='" + sort + '\'' +
                ", direction='" + direction + '\'' +
                '}';
    }
}
