package com.hzwangda.edu.common.transaction;


import org.apache.seata.rm.tcc.api.BusinessActionContext;
import org.apache.seata.rm.tcc.api.BusinessActionContextParameter;
import org.apache.seata.rm.tcc.api.LocalTCC;
import org.apache.seata.rm.tcc.api.TwoPhaseBusinessAction;

/**
 * TCC事务服务基础接口
 * 定义TCC模式的Try、Confirm、Cancel三个阶段的标准接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@LocalTCC
public interface TccTransactionService {

    /**
     * Try阶段：尝试执行业务操作，预留资源
     *
     * @param businessKey 业务主键
     * @param params 业务参数
     * @return 是否成功
     */
    @TwoPhaseBusinessAction(
        name = "tccTransactionTry",
        commitMethod = "confirm",
        rollbackMethod = "cancel"
    )
    boolean tryExecute(
        @BusinessActionContextParameter(paramName = "businessKey") String businessKey,
        @BusinessActionContextParameter(paramName = "params") Object params
    );

    /**
     * Confirm阶段：确认执行业务操作，提交资源
     *
     * @param context 业务上下文
     * @return 是否成功
     */
    boolean confirm(BusinessActionContext context);

    /**
     * Cancel阶段：取消业务操作，释放资源
     *
     * @param context 业务上下文
     * @return 是否成功
     */
    boolean cancel(BusinessActionContext context);
}
