package com.hzwangda.edu.common.transaction;

import io.seata.rm.datasource.DataSourceProxy;
import io.seata.spring.annotation.GlobalTransactionScanner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import javax.sql.DataSource;

/**
 * Seata分布式事务配置
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Configuration
@ConditionalOnProperty(name = "seata.enabled", havingValue = "true", matchIfMissing = false)
public class SeataConfig {

    @Value("${spring.application.name}")
    private String applicationName;

    @Value("${seata.tx-service-group:${spring.application.name}-group}")
    private String txServiceGroup;

    /**
     * 配置全局事务扫描器
     */
    @Bean
    public GlobalTransactionScanner globalTransactionScanner() {
        return new GlobalTransactionScanner(applicationName, txServiceGroup);
    }

    /**
     * 配置数据源代理
     * 注意：只有在使用AT模式时才需要数据源代理
     */
    @Bean
    @Primary
    @ConditionalOnProperty(name = "seata.mode", havingValue = "AT", matchIfMissing = true)
    public DataSource dataSourceProxy(DataSource dataSource) {
        return new DataSourceProxy(dataSource);
    }
}
