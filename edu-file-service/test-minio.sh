#!/bin/bash

echo "=== MinIO Connection Test ==="
echo "Testing connection to: https://qyb-minio.hzwangda.com"
echo

# 测试网络连接
echo "1. Testing network connectivity..."
if curl -s --connect-timeout 10 https://qyb-minio.hzwangda.com > /dev/null; then
    echo "   ✓ Network connection successful"
else
    echo "   ✗ Network connection failed"
    echo "   Please check:"
    echo "   - Is the MinIO server running?"
    echo "   - Is the URL correct?"
    echo "   - Are there any firewall restrictions?"
fi

echo

# 测试MinIO API端点
echo "2. Testing MinIO API endpoint..."
response=$(curl -s -w "%{http_code}" -o /dev/null https://qyb-minio.hzwangda.com/minio/health/live)
if [ "$response" = "200" ]; then
    echo "   ✓ MinIO API endpoint is accessible"
elif [ "$response" = "000" ]; then
    echo "   ✗ Cannot reach MinIO API endpoint"
else
    echo "   ⚠ MinIO API endpoint returned HTTP $response"
fi

echo

# 测试认证（需要MinIO客户端工具）
echo "3. Testing authentication..."
echo "   Note: This requires MinIO client (mc) to be installed"
echo "   You can install it from: https://min.io/docs/minio/linux/reference/minio-mc.html"

echo
echo "=== Manual Test Instructions ==="
echo "If you have MinIO client (mc) installed, run these commands:"
echo
echo "mc alias set test https://qyb-minio.hzwangda.com minio p5S3A9nDrUAvCjof"
echo "mc ls test"
echo "mc mb test/hky-hr"
echo
echo "If successful, your MinIO configuration is correct."
