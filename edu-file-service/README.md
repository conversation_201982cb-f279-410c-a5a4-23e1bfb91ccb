# 业务管理系统 - 文件管理服务

## 📋 服务概述

文件管理服务是业务管理系统的核心平台服务之一，负责系统内所有文件的上传、下载、存储和管理。基于MinIO分布式对象存储，提供安全、可靠、高效的文件管理功能。

## 🚀 主要功能

### 核心功能
- ✅ **文件上传**: 支持单文件和批量文件上传
- ✅ **文件下载**: 支持安全的文件下载和预览
- ✅ **预签名URL**: 支持前端直接上传/下载，减轻服务器压力
- ✅ **文件管理**: 文件信息查询、搜索、删除等管理功能
- ✅ **版本控制**: 支持文件版本管理和历史追溯
- ✅ **安全控制**: 基于业务模块的文件访问权限控制

### 技术特性
- ✅ **分布式存储**: 基于MinIO对象存储，支持PB级数据存储
- ✅ **文件类型检测**: 自动检测和验证文件类型
- ✅ **完整性校验**: MD5哈希值校验确保文件完整性
- ✅ **路径规范**: 统一的文件存储路径和命名规范
- ✅ **元数据管理**: 完整的文件元数据存储和查询
- ✅ **统计分析**: 文件使用情况统计和分析

## 🏗️ 技术架构

### 技术栈
- **框架**: Spring Boot 3.0
- **数据库**: PostgreSQL 15
- **对象存储**: MinIO
- **缓存**: Redis 7
- **文档**: SpringDoc OpenAPI 3

### 架构设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   API网关       │    │   文件服务      │
│                 │───▶│                 │───▶│                 │
│ Vue.js 3        │    │ Spring Gateway  │    │ Spring Boot 3   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐             │
                       │   MinIO存储     │◀────────────┘
                       │                 │
                       │ 分布式对象存储  │
                       └─────────────────┘
                                │
                       ┌─────────────────┐
                       │   PostgreSQL    │
                       │                 │
                       │ 文件元数据存储  │
                       └─────────────────┘
```

## 📁 项目结构

```
hky-file-service/
├── src/main/java/com/hky/hr/file/
│   ├── FileServiceApplication.java     # 启动类
│   ├── config/                         # 配置类
│   │   ├── MinioConfig.java           # MinIO配置
│   │   ├── FileServiceConfig.java     # 文件服务配置
│   │   └── SecurityConfig.java        # 安全配置
│   ├── controller/                     # 控制器
│   │   └── FileController.java        # 文件管理控制器
│   ├── dto/                           # 数据传输对象
│   │   ├── FileUploadRequest.java     # 文件上传请求
│   │   ├── FileInfoResponse.java      # 文件信息响应
│   │   └── PresignedUrlResponse.java  # 预签名URL响应
│   ├── entity/                        # 实体类
│   │   └── FileInfo.java             # 文件信息实体
│   ├── enums/                         # 枚举类
│   │   └── FileStatus.java           # 文件状态枚举
│   ├── repository/                    # 数据访问层
│   │   └── FileInfoRepository.java   # 文件信息Repository
│   ├── service/                       # 业务服务层
│   │   ├── FileService.java          # 文件服务接口
│   │   └── impl/
│   │       └── FileServiceImpl.java  # 文件服务实现
│   └── util/                          # 工具类
│       ├── FileUtil.java             # 文件工具类
│       └── FilePathUtil.java         # 文件路径工具类
├── src/main/resources/
│   └── application.yml                # 配置文件
└── README.md                          # 项目说明
```

## 🔧 配置说明

### 应用配置 (application.yml)

```yaml
server:
  port: 8002

# MinIO配置
minio:
  endpoint: http://localhost:9000
  access-key: minioadmin
  secret-key: minioadmin
  bucket-name: hr-system-files
  region: us-east-1
  secure: false

# 文件服务配置
file:
  max-file-size: 100MB
  max-request-size: 100MB
  allowed-types:
    - image/jpeg
    - image/png
    - application/pdf
    - application/msword
    # ... 更多支持的文件类型
  presigned-url-expiry: 3600
  path-template: "{module}/{bizId}/{fileType}/{date}/{uuid}.{ext}"
  versioning:
    enabled: true
    max-versions: 10
```

### 环境要求
- **JDK**: 17+
- **Maven**: 3.8+
- **PostgreSQL**: 15+
- **MinIO**: 最新版本
- **Redis**: 7+

## 📚 API文档

### 核心接口

#### 1. 文件上传
```http
POST /api/v1/files/upload/single
Content-Type: multipart/form-data

参数:
- file: 上传的文件
- moduleCode: 业务模块代码
- bizId: 业务实体ID
- fileType: 文件类型代码
- description: 文件描述
- overwrite: 是否覆盖同名文件
```

#### 2. 文件下载
```http
GET /api/v1/files/download/{fileId}

响应:
- Content-Type: 文件MIME类型
- Content-Disposition: attachment; filename="文件名"
- 文件二进制流
```

#### 3. 获取预签名上传URL
```http
GET /api/v1/files/upload/presigned-url?filename=test.pdf&contentType=application/pdf&moduleCode=contract&fileType=main_contract

响应:
{
  "fileId": "uuid",
  "uploadUrl": "预签名上传URL",
  "expiryTime": "2024-12-01T10:00:00",
  "expirySeconds": 3600
}
```

#### 4. 文件信息查询
```http
GET /api/v1/files/{fileId}/info

响应:
{
  "fileId": "uuid",
  "originalFilename": "合同文档.pdf",
  "fileSize": 1048576,
  "contentType": "application/pdf",
  "moduleCode": "contract",
  "bizId": "123",
  "fileType": "main_contract",
  "status": "ACTIVE",
  "createTime": "2024-12-01T10:00:00"
}
```

### 完整API文档
启动服务后访问: http://localhost:8002/swagger-ui.html

## 🗄️ 数据模型

### 文件信息表 (file_info)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| file_id | VARCHAR(36) | 文件唯一标识符 |
| original_filename | VARCHAR(255) | 原始文件名 |
| stored_filename | VARCHAR(255) | 存储文件名 |
| file_path | VARCHAR(500) | 文件在MinIO中的路径 |
| file_size | BIGINT | 文件大小（字节） |
| content_type | VARCHAR(100) | 文件MIME类型 |
| file_extension | VARCHAR(10) | 文件扩展名 |
| md5_hash | VARCHAR(32) | 文件MD5哈希值 |
| module_code | VARCHAR(50) | 业务模块代码 |
| biz_id | VARCHAR(50) | 业务实体ID |
| file_type | VARCHAR(50) | 文件类型代码 |
| status | VARCHAR(20) | 文件状态 |
| version | INTEGER | 文件版本号 |
| parent_file_id | VARCHAR(36) | 父文件ID（版本控制） |
| description | VARCHAR(500) | 文件描述 |
| upload_ip | VARCHAR(45) | 上传者IP地址 |
| download_count | INTEGER | 下载次数 |
| create_time | TIMESTAMP | 创建时间 |
| create_by | VARCHAR(64) | 创建人 |
| update_time | TIMESTAMP | 更新时间 |
| update_by | VARCHAR(64) | 更新人 |
| deleted | BOOLEAN | 删除标记 |

## 🚀 快速开始

### 1. 环境准备
```bash
# 启动基础设施服务
docker-compose up -d postgresql redis minio

# 等待服务启动完成
docker-compose ps
```

### 2. 编译运行
```bash
# 编译项目
mvn clean compile

# 运行服务
mvn spring-boot:run

# 或者打包后运行
mvn clean package
java -jar target/hky-file-service-1.0.0-SNAPSHOT.jar
```

### 3. 验证服务
```bash
# 健康检查
curl http://localhost:8002/api/v1/files/health

# 查看API文档
open http://localhost:8002/swagger-ui.html
```

## 🔍 监控与运维

### 健康检查
- **应用健康**: GET /api/v1/files/health
- **系统健康**: GET /actuator/health
- **应用信息**: GET /actuator/info
- **性能指标**: GET /actuator/metrics

### 日志监控
- **应用日志**: logs/file-service.log
- **日志级别**: 可通过配置文件调整
- **日志格式**: 包含时间戳、线程、级别、类名、消息

## 🔒 安全考虑

### 文件安全
- **类型验证**: 严格的文件类型白名单
- **大小限制**: 可配置的文件大小限制
- **路径安全**: 防止路径遍历攻击
- **访问控制**: 基于业务模块的权限控制

### 存储安全
- **加密存储**: MinIO服务端加密
- **传输加密**: HTTPS强制传输加密
- **完整性校验**: MD5哈希值验证
- **访问审计**: 完整的操作日志记录

## 📈 性能优化

### 上传优化
- **预签名URL**: 前端直接上传到MinIO
- **分片上传**: 大文件分片上传支持
- **并发控制**: 合理的并发上传限制

### 下载优化
- **预签名URL**: 临时直接下载链接
- **缓存策略**: 文件元数据缓存
- **CDN集成**: 可集成CDN加速下载

## 🔄 版本历史

- **v1.0.0**: 初始版本，实现基础文件管理功能

## 📞 联系方式

如有问题或建议，请联系开发团队。
