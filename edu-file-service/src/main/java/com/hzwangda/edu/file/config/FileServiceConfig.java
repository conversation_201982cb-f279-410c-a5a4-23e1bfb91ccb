package com.hzwangda.edu.file.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.util.List;

/**
 * 文件服务配置类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@ConfigurationProperties(prefix = "file")
@Getter
@Setter
@Validated
public class FileServiceConfig {

    /**
     * 最大文件大小
     */
    @NotNull(message = "最大文件大小不能为空")
    private String maxFileSize = "100MB";

    /**
     * 最大请求大小
     */
    @NotNull(message = "最大请求大小不能为空")
    private String maxRequestSize = "100MB";

    /**
     * 允许的文件类型
     */
    @NotEmpty(message = "允许的文件类型不能为空")
    private List<String> allowedTypes;

    /**
     * 预签名URL过期时间（秒）
     */
    @Positive(message = "预签名URL过期时间必须为正数")
    private Integer presignedUrlExpiry = 3600;

    /**
     * 文件存储路径模板
     */
    @NotNull(message = "文件存储路径模板不能为空")
    private String pathTemplate = "{module}/{bizId}/{fileType}/{date}/{uuid}.{ext}";

    /**
     * 文件版本控制配置
     */
    private VersioningConfig versioning = new VersioningConfig();

    /**
     * 文件版本控制配置类
     */
    public static class VersioningConfig {
        /**
         * 是否启用版本控制
         */
        private Boolean enabled = true;

        /**
         * 最大版本数
         */
        @Positive(message = "最大版本数必须为正数")
        private Integer maxVersions = 10;

        // Getter和Setter方法
        public Boolean getEnabled() {
            return enabled;
        }

        public void setEnabled(Boolean enabled) {
            this.enabled = enabled;
        }

        public Integer getMaxVersions() {
            return maxVersions;
        }

        public void setMaxVersions(Integer maxVersions) {
            this.maxVersions = maxVersions;
        }

        @Override
        public String toString() {
            return "VersioningConfig{" +
                    "enabled=" + enabled +
                    ", maxVersions=" + maxVersions +
                    '}';
        }
    }

    /**
     * 检查文件类型是否被允许
     *
     * @param contentType 文件MIME类型
     * @return 是否允许
     */
    public boolean isAllowedContentType(String contentType) {
        if (contentType == null || allowedTypes == null) {
            return false;
        }
        return allowedTypes.contains(contentType.toLowerCase());
    }

    /**
     * 获取最大文件大小（字节）
     *
     * @return 最大文件大小
     */
    public long getMaxFileSizeInBytes() {
        return parseSize(maxFileSize);
    }

    /**
     * 获取最大请求大小（字节）
     *
     * @return 最大请求大小
     */
    public long getMaxRequestSizeInBytes() {
        return parseSize(maxRequestSize);
    }

    /**
     * 解析大小字符串为字节数
     *
     * @param size 大小字符串（如 "100MB", "1GB"）
     * @return 字节数
     */
    private long parseSize(String size) {
        if (size == null || size.trim().isEmpty()) {
            return 0;
        }

        size = size.trim().toUpperCase();
        long multiplier = 1;

        if (size.endsWith("KB")) {
            multiplier = 1024;
            size = size.substring(0, size.length() - 2);
        } else if (size.endsWith("MB")) {
            multiplier = 1024 * 1024;
            size = size.substring(0, size.length() - 2);
        } else if (size.endsWith("GB")) {
            multiplier = 1024 * 1024 * 1024;
            size = size.substring(0, size.length() - 2);
        } else if (size.endsWith("B")) {
            size = size.substring(0, size.length() - 1);
        }

        try {
            return Long.parseLong(size.trim()) * multiplier;
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid size format: " + size, e);
        }
    }

    @Override
    public String toString() {
        return "FileServiceConfig{" +
                "maxFileSize='" + maxFileSize + '\'' +
                ", maxRequestSize='" + maxRequestSize + '\'' +
                ", allowedTypes=" + allowedTypes +
                ", presignedUrlExpiry=" + presignedUrlExpiry +
                ", pathTemplate='" + pathTemplate + '\'' +
                ", versioning=" + versioning +
                '}';
    }
}
