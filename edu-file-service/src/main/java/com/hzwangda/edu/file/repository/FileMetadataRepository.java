package com.hzwangda.edu.file.repository;

import com.hzwangda.edu.file.entity.FileMetadata;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 文件信息Repository接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface FileMetadataRepository extends JpaRepository<FileMetadata, Long> {

    /**
     * 根据文件ID查找文件信息
     *
     * @param fileId 文件ID
     * @return 文件信息
     */
    Optional<FileMetadata> findByFileIdAndDeletedFalse(String fileId);

    /**
     * 根据文件ID查找文件信息（包括已删除的）
     *
     * @param fileId 文件ID
     * @return 文件信息
     */
    Optional<FileMetadata> findByFileId(String fileId);

    /**
     * 根据业务模块和业务ID查找文件列表
     *
     * @param moduleCode 业务模块代码
     * @param bizId 业务实体ID
     * @param pageable 分页参数
     * @return 文件列表
     */
    Page<FileMetadata> findByModuleCodeAndBizIdAndDeletedFalseOrderByCreateTimeDesc(
            String moduleCode, String bizId, Pageable pageable);

    /**
     * 根据业务模块、业务ID和文件类型查找文件列表
     *
     * @param moduleCode 业务模块代码
     * @param bizId 业务实体ID
     * @param fileType 文件类型
     * @param pageable 分页参数
     * @return 文件列表
     */
    Page<FileMetadata> findByModuleCodeAndBizIdAndFileTypeAndDeletedFalseOrderByCreateTimeDesc(
            String moduleCode, String bizId, String fileType, Pageable pageable);

    /**
     * 根据业务模块查找文件列表
     *
     * @param moduleCode 业务模块代码
     * @param pageable 分页参数
     * @return 文件列表
     */
    Page<FileMetadata> findByModuleCodeAndDeletedFalseOrderByCreateTimeDesc(
            String moduleCode, Pageable pageable);

    /**
     * 根据文件状态查找文件列表
     *
     * @param status 文件状态
     * @param pageable 分页参数
     * @return 文件列表
     */
    Page<FileMetadata> findByStatusAndDeletedFalseOrderByCreateTimeDesc(
            String status, Pageable pageable);

    /**
     * 根据MD5哈希值查找文件
     *
     * @param md5Hash MD5哈希值
     * @return 文件列表
     */
    List<FileMetadata> findByMd5HashAndDeletedFalse(String md5Hash);

    /**
     * 根据父文件ID查找所有版本
     *
     * @param parentFileId 父文件ID
     * @return 文件版本列表
     */
    List<FileMetadata> findByParentFileIdAndDeletedFalseOrderByCreateTimeDesc(String parentFileId);

    /**
     * 根据原始文件名模糊查询
     *
     * @param filename 文件名关键字
     * @param pageable 分页参数
     * @return 文件列表
     */
    @Query("SELECT f FROM FileMetadata f WHERE f.originalFilename LIKE %:filename% AND f.deleted = false ORDER BY f.createTime DESC")
    Page<FileMetadata> findByOriginalFilenameContaining(@Param("filename") String filename, Pageable pageable);

    /**
     * 统计业务模块的文件数量
     *
     * @param moduleCode 业务模块代码
     * @return 文件数量
     */
    long countByModuleCodeAndDeletedFalse(String moduleCode);

    /**
     * 统计业务实体的文件数量
     *
     * @param moduleCode 业务模块代码
     * @param bizId 业务实体ID
     * @return 文件数量
     */
    long countByModuleCodeAndBizIdAndDeletedFalse(String moduleCode, String bizId);

    /**
     * 统计指定时间范围内的文件数量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 文件数量
     */
    long countByCreateTimeBetweenAndDeletedFalse(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 计算业务模块的文件总大小
     *
     * @param moduleCode 业务模块代码
     * @return 文件总大小（字节）
     */
    @Query("SELECT COALESCE(SUM(f.fileSize), 0) FROM FileMetadata f WHERE f.moduleCode = :moduleCode AND f.deleted = false")
    Long sumFileSizeByModuleCode(@Param("moduleCode") String moduleCode);

    /**
     * 逻辑删除文件
     *
     * @param fileId 文件ID
     * @param updateBy 更新人
     * @param updateTime 更新时间
     * @return 影响的行数
     */
    @Modifying
    @Query("UPDATE FileMetadata f SET f.deleted = true, f.status = 'DELETED', f.updateBy = :updateBy, f.updateTime = :updateTime WHERE f.fileId = :fileId")
    int logicalDeleteByFileId(@Param("fileId") String fileId, @Param("updateBy") String updateBy, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新文件下载次数
     *
     * @param fileId 文件ID
     * @return 影响的行数
     */
    @Modifying
    @Query("UPDATE FileMetadata f SET f.downloadCount = f.downloadCount + 1 WHERE f.fileId = :fileId")
    int incrementDownloadCount(@Param("fileId") String fileId);

    /**
     * 更新文件状态
     *
     * @param fileId 文件ID
     * @param status 新状态
     * @param updateBy 更新人
     * @param updateTime 更新时间
     * @return 影响的行数
     */
    @Modifying
    @Query("UPDATE FileMetadata f SET f.status = :status, f.updateBy = :updateBy, f.updateTime = :updateTime WHERE f.fileId = :fileId")
    int updateStatusByFileId(@Param("fileId") String fileId, @Param("status") String status,
                           @Param("updateBy") String updateBy, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 查找需要清理的临时文件
     *
     * @param beforeTime 时间阈值
     * @param statuses 状态列表
     * @return 文件列表
     */
    @Query("SELECT f FROM FileMetadata f WHERE f.createTime < :beforeTime AND f.status IN :statuses")
    List<FileMetadata> findFilesForCleanup(@Param("beforeTime") LocalDateTime beforeTime, @Param("statuses") List<String> statuses);

    /**
     * 查找指定业务实体的最新版本文件
     *
     * @param moduleCode 业务模块代码
     * @param bizId 业务实体ID
     * @param fileType 文件类型
     * @return 最新版本文件
     */
    @Query("SELECT f FROM FileMetadata f WHERE f.moduleCode = :moduleCode AND f.bizId = :bizId AND f.fileType = :fileType AND f.deleted = false ORDER BY f.createTime DESC")
    List<FileMetadata> findLatestVersionFile(@Param("moduleCode") String moduleCode, @Param("bizId") String bizId, @Param("fileType") String fileType);
}
