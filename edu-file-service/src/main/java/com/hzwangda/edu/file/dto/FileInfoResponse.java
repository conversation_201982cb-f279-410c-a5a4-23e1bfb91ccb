package com.hzwangda.edu.file.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件信息响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "文件信息响应")
@Data
public class FileInfoResponse {

    @Schema(description = "文件唯一标识符", example = "f47ac10b-58cc-4372-a567-0e02b2c3d479")
    private String fileId;

    @Schema(description = "原始文件名", example = "合同文档.pdf")
    private String originalFilename;

    @Schema(description = "文件大小（字节）", example = "1048576")
    private Long fileSize;

    @Schema(description = "文件MIME类型", example = "application/pdf")
    private String contentType;

    @Schema(description = "文件扩展名", example = "pdf")
    private String fileExtension;

    @Schema(description = "业务模块代码", example = "contract")
    private String moduleCode;

    @Schema(description = "业务实体ID", example = "123")
    private String bizId;

    @Schema(description = "文件类型代码", example = "main_contract")
    private String fileType;

    @Schema(description = "文件状态", example = "ACTIVE")
    private String status;

    @Schema(description = "文件版本号", example = "1")
    private Integer version;

    @Schema(description = "文件描述", example = "员工聘用合同文档")
    private String description;

    @Schema(description = "下载次数", example = "5")
    private Integer downloadCount;

    @Schema(description = "文件访问URL", example = "http://localhost:8002/api/v1/files/download/f47ac10b-58cc-4372-a567-0e02b2c3d479")
    private String fileUrl;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "创建人", example = "admin")
    private String createBy;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "更新人", example = "admin")
    private String updateBy;

    // 构造函数
    public FileInfoResponse() {
    }

    @Override
    public String toString() {
        return "FileInfoResponse{" +
                "fileId='" + fileId + '\'' +
                ", originalFilename='" + originalFilename + '\'' +
                ", fileSize=" + fileSize +
                ", contentType='" + contentType + '\'' +
                ", moduleCode='" + moduleCode + '\'' +
                ", bizId='" + bizId + '\'' +
                ", fileType='" + fileType + '\'' +
                ", status='" + status + '\'' +
                ", version=" + version +
                ", downloadCount=" + downloadCount +
                '}';
    }
}
