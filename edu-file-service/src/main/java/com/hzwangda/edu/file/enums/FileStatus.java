package com.hzwangda.edu.file.enums;

import lombok.Getter;

/**
 * 文件状态枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum FileStatus {

    /**
     * 活跃状态 - 文件正常可用
     */
    ACTIVE("ACTIVE", "活跃"),

    /**
     * 已删除状态 - 文件已被逻辑删除
     */
    DELETED("DELETED", "已删除"),

    /**
     * 上传中状态 - 文件正在上传过程中
     */
    UPLOADING("UPLOADING", "上传中"),

    /**
     * 上传失败状态 - 文件上传失败
     */
    UPLOAD_FAILED("UPLOAD_FAILED", "上传失败"),

    /**
     * 病毒扫描中状态 - 文件正在进行病毒扫描
     */
    SCANNING("SCANNING", "扫描中"),

    /**
     * 病毒扫描失败状态 - 文件包含病毒或扫描失败
     */
    SCAN_FAILED("SCAN_FAILED", "扫描失败"),

    /**
     * 归档状态 - 文件已归档到冷存储
     */
    ARCHIVED("ARCHIVED", "已归档"),

    /**
     * 损坏状态 - 文件已损坏无法使用
     */
    CORRUPTED("CORRUPTED", "已损坏");

    private final String code;
    private final String description;

    FileStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取枚举值
     *
     * @param code 状态代码
     * @return 对应的枚举值
     */
    public static FileStatus fromCode(String code) {
        for (FileStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown file status code: " + code);
    }

    /**
     * 检查是否为可用状态
     *
     * @return 是否可用
     */
    public boolean isAvailable() {
        return this == ACTIVE;
    }

    /**
     * 检查是否为删除状态
     *
     * @return 是否已删除
     */
    public boolean isDeleted() {
        return this == DELETED;
    }

    /**
     * 检查是否为上传中状态
     *
     * @return 是否正在上传
     */
    public boolean isUploading() {
        return this == UPLOADING;
    }

    /**
     * 检查是否为失败状态
     *
     * @return 是否失败
     */
    public boolean isFailed() {
        return this == UPLOAD_FAILED || this == SCAN_FAILED || this == CORRUPTED;
    }

    @Override
    public String toString() {
        return code;
    }
}
