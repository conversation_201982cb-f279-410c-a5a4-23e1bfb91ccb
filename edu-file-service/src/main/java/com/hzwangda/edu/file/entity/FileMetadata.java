package com.hzwangda.edu.file.entity;

import com.hzwangda.edu.common.entity.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

/**
 * 文件信息实体类
 *
 * 存储文件的元数据信息，包括文件名、路径、大小、类型等
 * 支持文件版本控制和业务关联
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "file_metadata", indexes = {
    @Index(name = "idx_file_info_module_biz", columnList = "module_code, biz_id"),
    @Index(name = "idx_file_info_file_type", columnList = "file_type"),
    @Index(name = "idx_file_info_status", columnList = "status"),
    @Index(name = "idx_file_info_create_time", columnList = "create_time")
})
@Schema(description = "文件信息")
@Getter
@Setter
public class FileMetadata extends BaseEntity {

    /**
     * 文件唯一标识符
     */
    @Column(name = "file_id", nullable = false, unique = true, length = 36)
    @NotBlank(message = "文件ID不能为空")
    @Size(max = 36, message = "文件ID长度不能超过36个字符")
    @Schema(description = "文件唯一标识符", example = "f47ac10b-58cc-4372-a567-0e02b2c3d479")
    private String fileId;

    /**
     * 原始文件名
     */
    @Column(name = "original_filename", nullable = false, length = 255)
    @NotBlank(message = "原始文件名不能为空")
    @Size(max = 255, message = "原始文件名长度不能超过255个字符")
    @Schema(description = "原始文件名", example = "合同文档.pdf")
    private String originalFilename;

    /**
     * 存储文件名（UUID + 扩展名）
     */
    @Column(name = "stored_filename", nullable = false, length = 255)
    @NotBlank(message = "存储文件名不能为空")
    @Size(max = 255, message = "存储文件名长度不能超过255个字符")
    @Schema(description = "存储文件名", example = "f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf")
    private String storedFilename;

    /**
     * 文件在MinIO中的完整路径
     */
    @Column(name = "file_path", nullable = false, length = 500)
    @NotBlank(message = "文件路径不能为空")
    @Size(max = 500, message = "文件路径长度不能超过500个字符")
    @Schema(description = "文件在MinIO中的完整路径", example = "contract/123/main_contract/20241201/f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf")
    private String filePath;

    /**
     * 文件大小（字节）
     */
    @Column(name = "file_size", nullable = false)
    @NotNull(message = "文件大小不能为空")
    @Schema(description = "文件大小（字节）", example = "1048576")
    private Long fileSize;

    /**
     * 文件MIME类型
     */
    @Column(name = "content_type", nullable = false, length = 100)
    @NotBlank(message = "文件类型不能为空")
    @Size(max = 100, message = "文件类型长度不能超过100个字符")
    @Schema(description = "文件MIME类型", example = "application/pdf")
    private String contentType;

    /**
     * 文件扩展名
     */
    @Column(name = "file_extension", length = 10)
    @Size(max = 10, message = "文件扩展名长度不能超过10个字符")
    @Schema(description = "文件扩展名", example = "pdf")
    private String fileExtension;

    /**
     * 文件MD5哈希值
     */
    @Column(name = "md5_hash", length = 32)
    @Size(max = 32, message = "MD5哈希值长度不能超过32个字符")
    @Schema(description = "文件MD5哈希值", example = "d41d8cd98f00b204e9800998ecf8427e")
    private String md5Hash;

    /**
     * 业务模块代码
     */
    @Column(name = "module_code", nullable = false, length = 50)
    @NotBlank(message = "业务模块代码不能为空")
    @Size(max = 50, message = "业务模块代码长度不能超过50个字符")
    @Schema(description = "业务模块代码", example = "contract")
    private String moduleCode;

    /**
     * 业务实体ID
     */
    @Column(name = "biz_id", length = 50)
    @Size(max = 50, message = "业务实体ID长度不能超过50个字符")
    @Schema(description = "业务实体ID", example = "123")
    private String bizId;

    /**
     * 文件类型代码
     */
    @Column(name = "file_type", nullable = false, length = 50)
    @NotBlank(message = "文件类型代码不能为空")
    @Size(max = 50, message = "文件类型代码长度不能超过50个字符")
    @Schema(description = "文件类型代码", example = "main_contract")
    private String fileType;

    /**
     * 文件状态
     */
    @Column(name = "status", nullable = false, length = 20)
    @NotBlank(message = "文件状态不能为空")
    @Size(max = 20, message = "文件状态长度不能超过20个字符")
    @Schema(description = "文件状态", example = "ACTIVE")
    private String status;

    /**
     * 父文件ID（用于版本控制）
     */
    @Column(name = "parent_file_id", length = 36)
    @Size(max = 36, message = "父文件ID长度不能超过36个字符")
    @Schema(description = "父文件ID（用于版本控制）", example = "f47ac10b-58cc-4372-a567-0e02b2c3d478")
    private String parentFileId;

    /**
     * 文件描述
     */
    @Column(name = "description", length = 500)
    @Size(max = 500, message = "文件描述长度不能超过500个字符")
    @Schema(description = "文件描述", example = "员工聘用合同文档")
    private String description;

    /**
     * 上传者IP地址
     */
    @Column(name = "upload_ip", length = 45)
    @Size(max = 45, message = "上传者IP地址长度不能超过45个字符")
    @Schema(description = "上传者IP地址", example = "*************")
    private String uploadIp;

    /**
     * 下载次数
     */
    @Column(name = "download_count", nullable = false)
    @Schema(description = "下载次数", example = "5")
    private Integer downloadCount = 0;

    // 构造函数
    public FileMetadata() {
        super();
    }


    @Override
    public String toString() {
        return "FileInfo{" +
                "fileId='" + fileId + '\'' +
                ", originalFilename='" + originalFilename + '\'' +
                ", storedFilename='" + storedFilename + '\'' +
                ", filePath='" + filePath + '\'' +
                ", fileSize=" + fileSize +
                ", contentType='" + contentType + '\'' +
                ", fileExtension='" + fileExtension + '\'' +
                ", moduleCode='" + moduleCode + '\'' +
                ", bizId='" + bizId + '\'' +
                ", fileType='" + fileType + '\'' +
                ", status='" + status + '\'' +
                ", version=" + getVersion() +
                ", downloadCount=" + downloadCount +
                '}';
    }
}
