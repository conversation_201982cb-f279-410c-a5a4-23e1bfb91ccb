package com.hzwangda.edu.file.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 文件上传请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "文件上传请求")
@Data
public class FileUploadRequest {

    /**
     * 业务模块代码
     */
    @NotBlank(message = "业务模块代码不能为空")
    @Size(max = 50, message = "业务模块代码长度不能超过50个字符")
    @Schema(description = "业务模块代码", example = "contract", required = true)
    private String moduleCode;

    /**
     * 业务实体ID
     */
    @Size(max = 50, message = "业务实体ID长度不能超过50个字符")
    @Schema(description = "业务实体ID", example = "123")
    private String bizId;

    /**
     * 文件类型代码
     */
    @NotBlank(message = "文件类型代码不能为空")
    @Size(max = 50, message = "文件类型代码长度不能超过50个字符")
    @Schema(description = "文件类型代码", example = "main_contract", required = true)
    private String fileType;

    /**
     * 文件描述
     */
    @Size(max = 500, message = "文件描述长度不能超过500个字符")
    @Schema(description = "文件描述", example = "员工聘用合同文档")
    private String description;

    /**
     * 是否覆盖同名文件
     */
    @Schema(description = "是否覆盖同名文件", example = "false")
    private Boolean overwrite = false;

    // 构造函数
    public FileUploadRequest() {
    }

    public FileUploadRequest(String moduleCode, String bizId, String fileType) {
        this.moduleCode = moduleCode;
        this.bizId = bizId;
        this.fileType = fileType;
    }

    @Override
    public String toString() {
        return "FileUploadRequest{" +
                "moduleCode='" + moduleCode + '\'' +
                ", bizId='" + bizId + '\'' +
                ", fileType='" + fileType + '\'' +
                ", description='" + description + '\'' +
                ", overwrite=" + overwrite +
                '}';
    }
}
