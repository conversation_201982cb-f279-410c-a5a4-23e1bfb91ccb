package com.hzwangda.edu.file.controller;

import com.hzwangda.edu.common.result.Result;
import com.hzwangda.edu.file.dto.FileInfoResponse;
import com.hzwangda.edu.file.dto.FileStatistics;
import com.hzwangda.edu.file.dto.FileUploadRequest;
import com.hzwangda.edu.file.dto.PresignedUrlResponse;
import com.hzwangda.edu.file.service.FileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 文件管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/v1/files")
@Tag(name = "文件管理", description = "文件上传、下载、存储管理等功能")
public class FileController {

    private static final Logger logger = LoggerFactory.getLogger(FileController.class);

    @Autowired
    private FileService fileService;

    /**
     * 单文件上传
     */
    @PostMapping("/upload/single")
    @Operation(summary = "单文件上传", description = "上传单个文件到MinIO存储")
    public Result<FileInfoResponse> uploadSingleFile(
            @Parameter(description = "上传的文件", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "业务模块代码", required = true)
            @RequestParam("moduleCode") String moduleCode,
            @Parameter(description = "业务实体ID")
            @RequestParam(value = "bizId", required = false) String bizId,
            @Parameter(description = "文件类型代码", required = true)
            @RequestParam("fileType") String fileType,
            @Parameter(description = "文件描述")
            @RequestParam(value = "description", required = false) String description,
            @Parameter(description = "是否覆盖同名文件")
            @RequestParam(value = "overwrite", defaultValue = "false") Boolean overwrite) {

        logger.info("接收单文件上传请求: {}, 模块: {}, 业务ID: {}, 文件类型: {}",
                   file.getOriginalFilename(), moduleCode, bizId, fileType);

        FileUploadRequest request = new FileUploadRequest(moduleCode, bizId, fileType);
        request.setDescription(description);
        request.setOverwrite(overwrite);

        FileInfoResponse response = fileService.uploadFile(file, request);
        return Result.success("文件上传成功", response);
    }

    /**
     * 批量文件上传
     */
    @PostMapping("/upload/batch")
    @Operation(summary = "批量文件上传", description = "批量上传多个文件到MinIO存储")
    public Result<List<FileInfoResponse>> uploadBatchFiles(
            @Parameter(description = "上传的文件列表", required = true)
            @RequestParam("files") List<MultipartFile> files,
            @Parameter(description = "业务模块代码", required = true)
            @RequestParam("moduleCode") String moduleCode,
            @Parameter(description = "业务实体ID")
            @RequestParam(value = "bizId", required = false) String bizId,
            @Parameter(description = "文件类型代码", required = true)
            @RequestParam("fileType") String fileType,
            @Parameter(description = "文件描述")
            @RequestParam(value = "description", required = false) String description,
            @Parameter(description = "是否覆盖同名文件")
            @RequestParam(value = "overwrite", defaultValue = "false") Boolean overwrite) {

        logger.info("接收批量文件上传请求: 文件数量={}, 模块: {}, 业务ID: {}, 文件类型: {}",
                   files.size(), moduleCode, bizId, fileType);

        FileUploadRequest request = new FileUploadRequest(moduleCode, bizId, fileType);
        request.setDescription(description);
        request.setOverwrite(overwrite);

        List<FileInfoResponse> responses = fileService.uploadFiles(files, request);
        return Result.success("批量文件上传成功", responses);
    }

    /**
     * 获取预签名上传URL
     */
    @GetMapping("/upload/presigned-url")
    @Operation(summary = "获取预签名上传URL", description = "获取用于前端直接上传到MinIO的预签名URL")
    public Result<PresignedUrlResponse> getPresignedUploadUrl(
            @Parameter(description = "文件名", required = true)
            @RequestParam("filename") String filename,
            @Parameter(description = "文件MIME类型", required = true)
            @RequestParam("contentType") String contentType,
            @Parameter(description = "业务模块代码", required = true)
            @RequestParam("moduleCode") String moduleCode,
            @Parameter(description = "业务实体ID")
            @RequestParam(value = "bizId", required = false) String bizId,
            @Parameter(description = "文件类型代码", required = true)
            @RequestParam("fileType") String fileType,
            @Parameter(description = "文件描述")
            @RequestParam(value = "description", required = false) String description,
            @Parameter(description = "URL过期时间（秒）")
            @RequestParam(value = "expirySeconds", required = false) Integer expirySeconds) {

        logger.info("获取预签名上传URL: {}, 类型: {}, 模块: {}", filename, contentType, moduleCode);

        FileUploadRequest request = new FileUploadRequest(moduleCode, bizId, fileType);
        request.setDescription(description);

        PresignedUrlResponse response = fileService.getPresignedUploadUrl(filename, contentType, request, expirySeconds);
        return Result.success("预签名URL生成成功", response);
    }

    /**
     * 确认预签名上传完成
     */
    @PostMapping("/upload/confirm")
    @Operation(summary = "确认预签名上传完成", description = "确认通过预签名URL上传的文件已完成")
    public Result<FileInfoResponse> confirmPresignedUpload(
            @Parameter(description = "文件ID", required = true)
            @RequestParam("fileId") String fileId,
            @Parameter(description = "ETag值", required = true)
            @RequestParam("etag") String etag) {

        logger.info("确认预签名上传完成: {}, ETag: {}", fileId, etag);

        FileInfoResponse response = fileService.confirmPresignedUpload(fileId, etag);
        return Result.success("上传确认成功", response);
    }

    /**
     * 下载文件
     */
    @GetMapping("/download/{fileId}")
    @Operation(summary = "下载文件", description = "根据文件ID下载文件")
    public void downloadFile(
            @Parameter(description = "文件ID", required = true)
            @PathVariable("fileId") String fileId,
            HttpServletResponse response) throws IOException {

        logger.info("下载文件: {}", fileId);

        // 获取文件信息
        FileInfoResponse fileInfo = fileService.getFileInfo(fileId);

        // 获取文件流
        InputStream inputStream = fileService.downloadFile(fileId);

        // 设置响应头
        response.setContentType(fileInfo.getContentType());
        response.setContentLengthLong(fileInfo.getFileSize());

        // 设置文件名，支持中文
        String encodedFilename = URLEncoder.encode(fileInfo.getOriginalFilename(), StandardCharsets.UTF_8);
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION,
                          "attachment; filename=\"" + encodedFilename + "\"; filename*=UTF-8''" + encodedFilename);

        // 输出文件流
        IOUtils.copy(inputStream, response.getOutputStream());
        inputStream.close();
        response.getOutputStream().flush();

        logger.info("文件下载完成: {}", fileId);
    }

    /**
     * 获取预签名下载URL
     */
    @GetMapping("/download/presigned-url/{fileId}")
    @Operation(summary = "获取预签名下载URL", description = "获取用于临时访问文件的预签名下载URL")
    public Result<PresignedUrlResponse> getPresignedDownloadUrl(
            @Parameter(description = "文件ID", required = true)
            @PathVariable("fileId") String fileId,
            @Parameter(description = "URL过期时间（秒）")
            @RequestParam(value = "expirySeconds", required = false) Integer expirySeconds) {

        logger.info("获取预签名下载URL: {}", fileId);

        PresignedUrlResponse response = fileService.getPresignedDownloadUrl(fileId, expirySeconds);
        return Result.success("预签名下载URL生成成功", response);
    }

    /**
     * 删除文件（逻辑删除）
     */
    @DeleteMapping("/{fileId}")
    @Operation(summary = "删除文件", description = "逻辑删除文件，文件标记为已删除状态")
    public Result<String> deleteFile(
            @Parameter(description = "文件ID", required = true)
            @PathVariable("fileId") String fileId) {

        logger.info("删除文件: {}", fileId);

        fileService.deleteFile(fileId);
        return Result.success("文件删除成功");
    }

    /**
     * 物理删除文件
     */
    @DeleteMapping("/physical/{fileId}")
    @Operation(summary = "物理删除文件", description = "从MinIO和数据库中彻底删除文件")
    public Result<String> physicalDeleteFile(
            @Parameter(description = "文件ID", required = true)
            @PathVariable("fileId") String fileId) {

        logger.info("物理删除文件: {}", fileId);

        fileService.physicalDeleteFile(fileId);
        return Result.success("文件物理删除成功");
    }

    /**
     * 获取文件信息
     */
    @GetMapping("/{fileId}/info")
    @Operation(summary = "获取文件信息", description = "根据文件ID获取文件的详细信息")
    public Result<FileInfoResponse> getFileInfo(
            @Parameter(description = "文件ID", required = true)
            @PathVariable("fileId") String fileId) {

        logger.info("获取文件信息: {}", fileId);

        FileInfoResponse response = fileService.getFileInfo(fileId);
        return Result.success("获取文件信息成功", response);
    }

    /**
     * 根据业务查询文件列表
     */
    @GetMapping("/business")
    @Operation(summary = "根据业务查询文件列表", description = "根据业务模块和业务ID查询文件列表")
    public Result<Page<FileInfoResponse>> getFilesByBusiness(
            @Parameter(description = "业务模块代码", required = true)
            @RequestParam("moduleCode") String moduleCode,
            @Parameter(description = "业务实体ID")
            @RequestParam(value = "bizId", required = false) String bizId,
            @Parameter(description = "文件类型代码")
            @RequestParam(value = "fileType", required = false) String fileType,
            @Parameter(description = "页码", example = "0")
            @RequestParam(value = "page", defaultValue = "0") int page,
            @Parameter(description = "每页大小", example = "10")
            @RequestParam(value = "size", defaultValue = "10") int size) {

        logger.info("查询业务文件列表: 模块={}, 业务ID={}, 文件类型={}", moduleCode, bizId, fileType);

        Pageable pageable = PageRequest.of(page, size);
        Page<FileInfoResponse> responses;

        if (fileType != null && !fileType.trim().isEmpty()) {
            responses = fileService.getFilesByBusinessAndType(moduleCode, bizId, fileType, pageable);
        } else {
            responses = fileService.getFilesByBusiness(moduleCode, bizId, pageable);
        }

        return Result.success("查询文件列表成功", responses);
    }

    /**
     * 根据文件名搜索文件
     */
    @GetMapping("/search")
    @Operation(summary = "根据文件名搜索文件", description = "根据文件名关键字搜索文件")
    public Result<Page<FileInfoResponse>> searchFilesByName(
            @Parameter(description = "文件名关键字", required = true)
            @RequestParam("filename") String filename,
            @Parameter(description = "页码", example = "0")
            @RequestParam(value = "page", defaultValue = "0") int page,
            @Parameter(description = "每页大小", example = "10")
            @RequestParam(value = "size", defaultValue = "10") int size) {

        logger.info("搜索文件: {}", filename);

        Pageable pageable = PageRequest.of(page, size);
        Page<FileInfoResponse> responses = fileService.searchFilesByName(filename, pageable);

        return Result.success("搜索文件成功", responses);
    }

    /**
     * 获取文件版本列表
     */
    @GetMapping("/{parentFileId}/versions")
    @Operation(summary = "获取文件版本列表", description = "获取指定文件的所有版本")
    public Result<List<FileInfoResponse>> getFileVersions(
            @Parameter(description = "父文件ID", required = true)
            @PathVariable("parentFileId") String parentFileId) {

        logger.info("获取文件版本列表: {}", parentFileId);

        List<FileInfoResponse> responses = fileService.getFileVersions(parentFileId);
        return Result.success("获取文件版本列表成功", responses);
    }

    /**
     * 预览文件
     */
    @GetMapping("/preview/{fileId}")
    @Operation(summary = "预览文件", description = "在线预览文件内容")
    public void previewFile(
            @Parameter(description = "文件ID", required = true)
            @PathVariable("fileId") String fileId,
            HttpServletResponse response) throws IOException {

        logger.info("预览文件: {}", fileId);

        // 获取文件信息
        FileInfoResponse fileInfo = fileService.getFileInfo(fileId);

        // 获取文件流
        InputStream inputStream = fileService.previewFile(fileId);

        // 设置响应头
        response.setContentType(fileInfo.getContentType());
        response.setContentLengthLong(fileInfo.getFileSize());

        // 设置为内联显示
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "inline");

        // 输出文件流
        IOUtils.copy(inputStream, response.getOutputStream());
        inputStream.close();
        response.getOutputStream().flush();

        logger.info("文件预览完成: {}", fileId);
    }

    /**
     * 检查文件是否存在
     */
    @GetMapping("/{fileId}/exists")
    @Operation(summary = "检查文件是否存在", description = "检查指定文件ID的文件是否存在且可用")
    public Result<Boolean> fileExists(
            @Parameter(description = "文件ID", required = true)
            @PathVariable("fileId") String fileId) {

        logger.info("检查文件是否存在: {}", fileId);

        boolean exists = fileService.fileExists(fileId);
        return Result.success("检查完成", exists);
    }

    /**
     * 获取文件统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取文件统计信息", description = "获取指定业务模块的文件统计信息")
    public Result<FileStatistics> getFileStatistics(
            @Parameter(description = "业务模块代码", required = true)
            @RequestParam("moduleCode") String moduleCode) {

        logger.info("获取文件统计信息: {}", moduleCode);

        FileStatistics statistics = fileService.getFileStatistics(moduleCode);
        return Result.success("获取统计信息成功", statistics);
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查文件服务的健康状态")
    public Result<String> health() {
        return Result.success("文件服务运行正常", "OK");
    }

}
