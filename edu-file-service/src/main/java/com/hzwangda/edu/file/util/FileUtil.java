package com.hzwangda.edu.file.util;

import org.apache.commons.io.IOUtils;
import org.apache.tika.Tika;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.List;

/**
 * 文件工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class FileUtil {

    private static final Logger logger = LoggerFactory.getLogger(FileUtil.class);

    private static final Tika tika = new Tika();

    /**
     * 图片文件类型
     */
    public static final List<String> IMAGE_TYPES = Arrays.asList(
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp", "image/bmp"
    );

    /**
     * 文档文件类型
     */
    public static final List<String> DOCUMENT_TYPES = Arrays.asList(
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-powerpoint",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "text/plain"
    );

    /**
     * 压缩文件类型
     */
    public static final List<String> ARCHIVE_TYPES = Arrays.asList(
            "application/zip", "application/x-rar-compressed", "application/x-7z-compressed"
    );

    /**
     * 获取文件扩展名
     *
     * @param filename 文件名
     * @return 扩展名（不包含点号）
     */
    public static String getFileExtension(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "";
        }

        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }

        return filename.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * 获取不带扩展名的文件名
     *
     * @param filename 文件名
     * @return 不带扩展名的文件名
     */
    public static String getFileNameWithoutExtension(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "";
        }

        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return filename;
        }

        return filename.substring(0, lastDotIndex);
    }

    /**
     * 检测文件MIME类型
     *
     * @param inputStream 文件输入流
     * @param filename 文件名
     * @return MIME类型
     */
    public static String detectContentType(InputStream inputStream, String filename) {
        try {
            return tika.detect(inputStream, filename);
        } catch (IOException e) {
            logger.warn("检测文件类型失败: {}", filename, e);
            return "application/octet-stream";
        }
    }

    /**
     * 检测文件MIME类型
     *
     * @param filename 文件名
     * @return MIME类型
     */
    public static String detectContentType(String filename) {
        return tika.detect(filename);
    }

    /**
     * 计算文件MD5哈希值
     *
     * @param inputStream 文件输入流
     * @return MD5哈希值
     * @throws IOException IO异常
     */
    public static String calculateMD5(InputStream inputStream) throws IOException {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] buffer = new byte[8192];
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                md.update(buffer, 0, bytesRead);
            }

            byte[] hashBytes = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }

            return sb.toString();

        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5算法不可用", e);
        }
    }

    /**
     * 计算文件SHA256哈希值
     *
     * @param inputStream 文件输入流
     * @return SHA256哈希值
     * @throws IOException IO异常
     */
    public static String calculateSHA256(InputStream inputStream) throws IOException {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] buffer = new byte[8192];
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                md.update(buffer, 0, bytesRead);
            }

            byte[] hashBytes = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }

            return sb.toString();

        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }

    /**
     * 格式化文件大小
     *
     * @param size 文件大小（字节）
     * @return 格式化后的大小字符串
     */
    public static String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 检查是否为图片文件
     *
     * @param contentType MIME类型
     * @return 是否为图片
     */
    public static boolean isImage(String contentType) {
        return contentType != null && IMAGE_TYPES.contains(contentType.toLowerCase());
    }

    /**
     * 检查是否为文档文件
     *
     * @param contentType MIME类型
     * @return 是否为文档
     */
    public static boolean isDocument(String contentType) {
        return contentType != null && DOCUMENT_TYPES.contains(contentType.toLowerCase());
    }

    /**
     * 检查是否为压缩文件
     *
     * @param contentType MIME类型
     * @return 是否为压缩文件
     */
    public static boolean isArchive(String contentType) {
        return contentType != null && ARCHIVE_TYPES.contains(contentType.toLowerCase());
    }

    /**
     * 验证文件名是否安全
     *
     * @param filename 文件名
     * @return 是否安全
     */
    public static boolean isValidFilename(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return false;
        }

        // 检查文件名长度
        if (filename.length() > 255) {
            return false;
        }

        // 检查是否包含非法字符
        String[] illegalChars = {"\\", "/", ":", "*", "?", "\"", "<", ">", "|"};
        for (String illegalChar : illegalChars) {
            if (filename.contains(illegalChar)) {
                return false;
            }
        }

        // 检查是否为保留名称
        String[] reservedNames = {"CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4",
                                 "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2",
                                 "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"};

        String nameWithoutExt = getFileNameWithoutExtension(filename).toUpperCase();
        for (String reservedName : reservedNames) {
            if (reservedName.equals(nameWithoutExt)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 清理文件名，移除非法字符
     *
     * @param filename 原始文件名
     * @return 清理后的文件名
     */
    public static String sanitizeFilename(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "unnamed";
        }

        // 替换非法字符
        String sanitized = filename.replaceAll("[\\\\/:*?\"<>|]", "_");

        // 限制长度
        if (sanitized.length() > 255) {
            String extension = getFileExtension(sanitized);
            String nameWithoutExt = getFileNameWithoutExtension(sanitized);
            int maxNameLength = 255 - extension.length() - 1; // -1 for the dot
            if (maxNameLength > 0) {
                sanitized = nameWithoutExt.substring(0, Math.min(nameWithoutExt.length(), maxNameLength)) + "." + extension;
            } else {
                sanitized = sanitized.substring(0, 255);
            }
        }

        return sanitized;
    }

    /**
     * 复制输入流到字节数组
     *
     * @param inputStream 输入流
     * @return 字节数组
     * @throws IOException IO异常
     */
    public static byte[] toByteArray(InputStream inputStream) throws IOException {
        return IOUtils.toByteArray(inputStream);
    }
}
