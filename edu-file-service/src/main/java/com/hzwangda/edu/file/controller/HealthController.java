package com.hzwangda.edu.file.controller;

import com.hzwangda.edu.common.result.Result;
import com.hzwangda.edu.common.result.ResultCode;
import com.hzwangda.edu.file.util.MinIODiagnostic;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/health")
@RequiredArgsConstructor
public class HealthController {

    private final MinIODiagnostic minIODiagnostic;

    @GetMapping("/minio")
    public ResponseEntity<Result<Map<String, Object>>> checkMinIOHealth() {
        Map<String, Object> healthInfo = new HashMap<>();

        try {
            boolean isHealthy = minIODiagnostic.isHealthy();
            healthInfo.put("status", isHealthy ? "UP" : "DOWN");
            healthInfo.put("service", "MinIO File Service");
            healthInfo.put("timestamp", System.currentTimeMillis());

            if (isHealthy) {
                healthInfo.put("message", "MinIO service is healthy and accessible");
                return ResponseEntity.ok(Result.success(healthInfo));
            } else {
                healthInfo.put("message", "MinIO service is not accessible");
                return ResponseEntity.status(503).body(new Result<>(ResultCode.BUSINESS_ERROR.getCode(), "MinIO service unavailable", healthInfo));
            }
        } catch (Exception e) {
            healthInfo.put("status", "ERROR");
            healthInfo.put("message", "Health check failed: " + e.getMessage());
            return ResponseEntity.status(500).body(new Result<>(ResultCode.BUSINESS_ERROR.getCode(), "Health check error", healthInfo));
        }
    }

    @GetMapping("/diagnose")
    public ResponseEntity<Result<String>> diagnoseMinIO() {
        try {
            // 在控制台输出诊断信息
            minIODiagnostic.diagnoseConnection();
            return ResponseEntity.ok(Result.success("Diagnostic completed. Check console output for details."));
        } catch (Exception e) {
            return ResponseEntity.status(500).body(Result.error("Diagnostic failed: " + e.getMessage()));
        }
    }
}
