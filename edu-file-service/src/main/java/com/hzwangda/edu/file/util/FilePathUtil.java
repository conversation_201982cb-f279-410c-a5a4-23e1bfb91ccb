package com.hzwangda.edu.file.util;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 文件路径工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class FilePathUtil {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    /**
     * 生成文件存储路径
     *
     * @param moduleCode 业务模块代码
     * @param bizId 业务实体ID
     * @param fileType 文件类型代码
     * @param originalFilename 原始文件名
     * @param pathTemplate 路径模板
     * @return 文件存储路径
     */
    public static String generateFilePath(String moduleCode, String bizId, String fileType,
                                        String originalFilename, String pathTemplate) {
        // 生成UUID作为文件名
        String uuid = UUID.randomUUID().toString();

        // 获取文件扩展名
        String extension = FileUtil.getFileExtension(originalFilename);

        // 获取当前日期
        String date = LocalDate.now().format(DATE_FORMATTER);

        // 处理空值
        bizId = bizId != null ? bizId : "common";

        // 替换模板中的占位符
        String filePath = pathTemplate
                .replace("{module}", sanitizePathComponent(moduleCode))
                .replace("{bizId}", sanitizePathComponent(bizId))
                .replace("{fileType}", sanitizePathComponent(fileType))
                .replace("{date}", date)
                .replace("{uuid}", uuid)
                .replace("{ext}", extension);

        return filePath;
    }

    /**
     * 生成简单的文件路径（不使用模板）
     *
     * @param moduleCode 业务模块代码
     * @param bizId 业务实体ID
     * @param fileType 文件类型代码
     * @param originalFilename 原始文件名
     * @return 文件存储路径
     */
    public static String generateSimpleFilePath(String moduleCode, String bizId, String fileType,
                                              String originalFilename) {
        String uuid = UUID.randomUUID().toString();
        String extension = FileUtil.getFileExtension(originalFilename);
        String date = LocalDate.now().format(DATE_FORMATTER);

        bizId = bizId != null ? bizId : "common";

        StringBuilder pathBuilder = new StringBuilder();
        pathBuilder.append(sanitizePathComponent(moduleCode)).append("/");
        pathBuilder.append(sanitizePathComponent(bizId)).append("/");
        pathBuilder.append(sanitizePathComponent(fileType)).append("/");
        pathBuilder.append(date).append("/");
        pathBuilder.append(uuid);

        if (!extension.isEmpty()) {
            pathBuilder.append(".").append(extension);
        }

        return pathBuilder.toString();
    }

    /**
     * 从文件路径中提取文件名
     *
     * @param filePath 文件路径
     * @return 文件名
     */
    public static String extractFilename(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return "";
        }

        int lastSlashIndex = filePath.lastIndexOf('/');
        if (lastSlashIndex == -1) {
            return filePath;
        }

        return filePath.substring(lastSlashIndex + 1);
    }

    /**
     * 从文件路径中提取目录路径
     *
     * @param filePath 文件路径
     * @return 目录路径
     */
    public static String extractDirectoryPath(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return "";
        }

        int lastSlashIndex = filePath.lastIndexOf('/');
        if (lastSlashIndex == -1) {
            return "";
        }

        return filePath.substring(0, lastSlashIndex);
    }

    /**
     * 生成缩略图路径
     *
     * @param originalPath 原始文件路径
     * @param size 缩略图尺寸
     * @return 缩略图路径
     */
    public static String generateThumbnailPath(String originalPath, String size) {
        String directory = extractDirectoryPath(originalPath);
        String filename = extractFilename(originalPath);
        String nameWithoutExt = FileUtil.getFileNameWithoutExtension(filename);
        String extension = FileUtil.getFileExtension(filename);

        StringBuilder thumbnailPath = new StringBuilder();
        if (!directory.isEmpty()) {
            thumbnailPath.append(directory).append("/");
        }
        thumbnailPath.append("thumbnails/");
        thumbnailPath.append(nameWithoutExt).append("_").append(size);
        if (!extension.isEmpty()) {
            thumbnailPath.append(".").append(extension);
        }

        return thumbnailPath.toString();
    }

    /**
     * 生成版本文件路径
     *
     * @param originalPath 原始文件路径
     * @param version 版本号
     * @return 版本文件路径
     */
    public static String generateVersionPath(String originalPath, int version) {
        String directory = extractDirectoryPath(originalPath);
        String filename = extractFilename(originalPath);
        String nameWithoutExt = FileUtil.getFileNameWithoutExtension(filename);
        String extension = FileUtil.getFileExtension(filename);

        StringBuilder versionPath = new StringBuilder();
        if (!directory.isEmpty()) {
            versionPath.append(directory).append("/");
        }
        versionPath.append("versions/");
        versionPath.append(nameWithoutExt).append("_v").append(version);
        if (!extension.isEmpty()) {
            versionPath.append(".").append(extension);
        }

        return versionPath.toString();
    }

    /**
     * 清理路径组件，移除非法字符
     *
     * @param component 路径组件
     * @return 清理后的路径组件
     */
    public static String sanitizePathComponent(String component) {
        if (component == null || component.trim().isEmpty()) {
            return "unknown";
        }

        // 移除或替换非法字符
        String sanitized = component
                .replaceAll("[\\\\/:*?\"<>|]", "_")  // 替换文件系统非法字符
                .replaceAll("\\s+", "_")             // 替换空白字符
                .replaceAll("_{2,}", "_")            // 合并多个下划线
                .toLowerCase();                      // 转为小写

        // 移除开头和结尾的下划线
        sanitized = sanitized.replaceAll("^_+|_+$", "");

        // 如果清理后为空，使用默认值
        if (sanitized.isEmpty()) {
            sanitized = "unknown";
        }

        return sanitized;
    }

    /**
     * 检查路径是否安全
     *
     * @param path 文件路径
     * @return 是否安全
     */
    public static boolean isSecurePath(String path) {
        if (path == null || path.trim().isEmpty()) {
            return false;
        }

        // 检查路径遍历攻击
        if (path.contains("..") || path.contains("./") || path.contains("\\")) {
            return false;
        }

        // 检查绝对路径
        if (path.startsWith("/") || path.matches("^[a-zA-Z]:.*")) {
            return false;
        }

        return true;
    }

    /**
     * 标准化路径分隔符
     *
     * @param path 文件路径
     * @return 标准化后的路径
     */
    public static String normalizePath(String path) {
        if (path == null) {
            return "";
        }

        // 将反斜杠替换为正斜杠
        return path.replace('\\', '/');
    }

    /**
     * 连接路径组件
     *
     * @param components 路径组件
     * @return 连接后的路径
     */
    public static String joinPath(String... components) {
        if (components == null || components.length == 0) {
            return "";
        }

        StringBuilder pathBuilder = new StringBuilder();
        for (int i = 0; i < components.length; i++) {
            String component = components[i];
            if (component == null || component.trim().isEmpty()) {
                continue;
            }

            component = component.trim();

            // 移除开头的斜杠（除了第一个组件）
            if (i > 0 && component.startsWith("/")) {
                component = component.substring(1);
            }

            // 移除结尾的斜杠
            if (component.endsWith("/")) {
                component = component.substring(0, component.length() - 1);
            }

            if (!component.isEmpty()) {
                if (pathBuilder.length() > 0) {
                    pathBuilder.append("/");
                }
                pathBuilder.append(component);
            }
        }

        return pathBuilder.toString();
    }

    /**
     * 获取文件的相对路径
     *
     * @param basePath 基础路径
     * @param fullPath 完整路径
     * @return 相对路径
     */
    public static String getRelativePath(String basePath, String fullPath) {
        if (basePath == null || fullPath == null) {
            return fullPath;
        }

        basePath = normalizePath(basePath);
        fullPath = normalizePath(fullPath);

        if (fullPath.startsWith(basePath)) {
            String relativePath = fullPath.substring(basePath.length());
            if (relativePath.startsWith("/")) {
                relativePath = relativePath.substring(1);
            }
            return relativePath;
        }

        return fullPath;
    }
}
