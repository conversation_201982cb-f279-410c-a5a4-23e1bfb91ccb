package com.hzwangda.edu.file.util;

import com.hzwangda.edu.file.config.MinioConfig;
import io.minio.BucketExistsArgs;
import io.minio.MinioClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.HttpURLConnection;
import java.net.URL;

@Component
public class MinIODiagnostic {

    @Autowired
    private MinioClient minioClient;

    @Autowired
    private MinioConfig minioConfig;

    public void diagnoseConnection() {
        System.out.println("=== MinIO Connection Diagnostic ===");

        // 1. 检查配置
        System.out.println("1. Configuration Check:");
        System.out.println("   Endpoint: " + minioConfig.getEndpoint());
        System.out.println("   AccessKey: " + minioConfig.getAccessKey());
        System.out.println("   SecretKey: " + (minioConfig.getAccessKey() != null ? "***" + minioConfig.getAccessKey().substring(Math.max(0, minioConfig.getAccessKey().length() - 4)) : "null"));
        System.out.println("   BucketName: " + minioConfig.getBucketName());

        // 2. 检查网络连接
        System.out.println("\n2. Network Connectivity Check:");
        checkNetworkConnectivity();

        // 3. 检查MinIO服务
        System.out.println("\n3. MinIO Service Check:");
        checkMinIOService();

        // 4. 检查存储桶
        System.out.println("\n4. Bucket Check:");
        checkBucket();

        System.out.println("\n=== Diagnostic Complete ===");
    }

    private void checkNetworkConnectivity() {
        try {
            URL url = new URL(minioConfig.getEndpoint());
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);

            int responseCode = connection.getResponseCode();
            System.out.println("   Network connectivity: OK (Response code: " + responseCode + ")");
        } catch (Exception e) {
            System.err.println("   Network connectivity: FAILED - " + e.getMessage());
            System.err.println("   Please check if the MinIO server is running and accessible.");
        }
    }

    private void checkMinIOService() {
        try {
            // 尝试列出存储桶来测试连接
            minioClient.listBuckets();
            System.out.println("   MinIO service: OK - Successfully connected and authenticated");
        } catch (Exception e) {
            System.err.println("   MinIO service: FAILED - " + e.getMessage());
            System.err.println("   This could indicate:");
            System.err.println("   - Incorrect credentials (accessKey/secretKey)");
            System.err.println("   - MinIO server not running");
            System.err.println("   - Network firewall blocking connection");
            System.err.println("   - SSL/TLS certificate issues");
        }
    }

    private void checkBucket() {
        try {
            boolean exists = minioClient.bucketExists(
                BucketExistsArgs.builder().bucket(minioConfig.getBucketName()).build()
            );
            if (exists) {
                System.out.println("   Bucket '" + minioConfig.getBucketName() + "': EXISTS");
            } else {
                System.out.println("   Bucket '" + minioConfig.getBucketName() + "': NOT EXISTS (will be created automatically)");
            }
        } catch (Exception e) {
            System.err.println("   Bucket check: FAILED - " + e.getMessage());
        }
    }

    public boolean isHealthy() {
        try {
            minioClient.listBuckets();
            minioClient.bucketExists(BucketExistsArgs.builder().bucket(minioConfig.getBucketName()).build());
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
