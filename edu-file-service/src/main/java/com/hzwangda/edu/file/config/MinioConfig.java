package com.hzwangda.edu.file.config;

import io.minio.BucketExistsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import jakarta.annotation.PostConstruct;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

/**
 * MinIO配置类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "minio")
@Validated
@Data
public class MinioConfig {

    /**
     * MinIO服务端点
     */
    @NotBlank(message = "MinIO端点不能为空")
    private String endpoint;

    /**
     * 访问密钥
     */
    @NotBlank(message = "MinIO访问密钥不能为空")
    private String accessKey;

    /**
     * 秘密密钥
     */
    @NotBlank(message = "MinIO秘密密钥不能为空")
    private String secretKey;

    /**
     * 存储桶名称
     */
    @NotBlank(message = "MinIO存储桶名称不能为空")
    private String bucketName;

    /**
     * 区域
     */
    private String region = "us-east-1";

    /**
     * 是否使用HTTPS
     */
    private Boolean secure = false;

    /**
     * 创建MinIO客户端Bean
     *
     * @return MinIO客户端
     */
    @Bean
    public MinioClient minioClient() {
        try {
            log.info("正在初始化MinIO客户端...");
            log.info("MinIO端点: {}", endpoint);
            log.info("MinIO存储桶: {}", bucketName);
            log.info("MinIO安全连接: {}", secure);

            MinioClient client = MinioClient.builder()
                    .endpoint(endpoint)
                    .credentials(accessKey, secretKey)
                    .region(region)
                    .build();

            log.info("MinIO客户端初始化成功");
            return client;

        } catch (Exception e) {
            log.error("MinIO客户端初始化失败", e);
            throw new RuntimeException("Failed to initialize MinIO client", e);
        }
    }

    @PostConstruct
    public void createBucketIfNotExists() {
        try {
            MinioClient client = minioClient();
            boolean bucketExists = client.bucketExists(
                    BucketExistsArgs.builder().bucket(bucketName).build()
            );

            if (!bucketExists) {
                client.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                log.info("Bucket created successfully: {}", bucketName);
            } else {
                log.info("Bucket already exists: {}", bucketName);
            }
        } catch (Exception e) {
            log.error("Warning: Failed to check/create bucket: {}", e.getMessage());
            log.error("This might be due to network issues or incorrect credentials.");
            log.error("Please verify your MinIO configuration and network connectivity.");
            // 不抛出异常，允许应用启动，但记录警告
        }
    }
    @Override
    public String toString() {
        return "MinioConfig{" +
                "endpoint='" + endpoint + '\'' +
                ", accessKey='" + accessKey + '\'' +
                ", bucketName='" + bucketName + '\'' +
                ", region='" + region + '\'' +
                ", secure=" + secure +
                '}';
    }
}
