package com.hzwangda.edu.file.service;

import com.hzwangda.edu.file.dto.FileInfoResponse;
import com.hzwangda.edu.file.dto.FileStatistics;
import com.hzwangda.edu.file.dto.FileUploadRequest;
import com.hzwangda.edu.file.dto.PresignedUrlResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

/**
 * 文件服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface FileService {

    /**
     * 单文件上传
     *
     * @param file 文件
     * @param request 上传请求参数
     * @return 文件信息响应
     */
    FileInfoResponse uploadFile(MultipartFile file, FileUploadRequest request);

    /**
     * 批量文件上传
     *
     * @param files 文件列表
     * @param request 上传请求参数
     * @return 文件信息响应列表
     */
    List<FileInfoResponse> uploadFiles(List<MultipartFile> files, FileUploadRequest request);

    /**
     * 获取预签名上传URL
     *
     * @param filename 文件名
     * @param contentType 文件类型
     * @param request 上传请求参数
     * @param expirySeconds 过期时间（秒）
     * @return 预签名URL响应
     */
    PresignedUrlResponse getPresignedUploadUrl(String filename, String contentType,
                                             FileUploadRequest request, Integer expirySeconds);

    /**
     * 确认预签名URL上传完成
     *
     * @param fileId 文件ID
     * @param etag ETag值
     * @return 文件信息响应
     */
    FileInfoResponse confirmPresignedUpload(String fileId, String etag);

    /**
     * 下载文件
     *
     * @param fileId 文件ID
     * @return 文件输入流
     */
    InputStream downloadFile(String fileId);

    /**
     * 获取预签名下载URL
     *
     * @param fileId 文件ID
     * @param expirySeconds 过期时间（秒）
     * @return 预签名URL响应
     */
    PresignedUrlResponse getPresignedDownloadUrl(String fileId, Integer expirySeconds);

    /**
     * 删除文件（逻辑删除）
     *
     * @param fileId 文件ID
     */
    void deleteFile(String fileId);

    /**
     * 物理删除文件
     *
     * @param fileId 文件ID
     */
    void physicalDeleteFile(String fileId);

    /**
     * 获取文件信息
     *
     * @param fileId 文件ID
     * @return 文件信息响应
     */
    FileInfoResponse getFileInfo(String fileId);

    /**
     * 根据业务模块和业务ID查询文件列表
     *
     * @param moduleCode 业务模块代码
     * @param bizId 业务实体ID
     * @param pageable 分页参数
     * @return 文件列表
     */
    Page<FileInfoResponse> getFilesByBusiness(String moduleCode, String bizId, Pageable pageable);

    /**
     * 根据业务模块、业务ID和文件类型查询文件列表
     *
     * @param moduleCode 业务模块代码
     * @param bizId 业务实体ID
     * @param fileType 文件类型
     * @param pageable 分页参数
     * @return 文件列表
     */
    Page<FileInfoResponse> getFilesByBusinessAndType(String moduleCode, String bizId,
                                                   String fileType, Pageable pageable);

    /**
     * 根据文件名搜索文件
     *
     * @param filename 文件名关键字
     * @param pageable 分页参数
     * @return 文件列表
     */
    Page<FileInfoResponse> searchFilesByName(String filename, Pageable pageable);

    /**
     * 获取文件版本列表
     *
     * @param parentFileId 父文件ID
     * @return 文件版本列表
     */
    List<FileInfoResponse> getFileVersions(String parentFileId);

    /**
     * 预览文件
     *
     * @param fileId 文件ID
     * @return 文件输入流
     */
    InputStream previewFile(String fileId);

    /**
     * 检查文件是否存在
     *
     * @param fileId 文件ID
     * @return 是否存在
     */
    boolean fileExists(String fileId);

    /**
     * 获取文件统计信息
     *
     * @param moduleCode 业务模块代码
     * @return 统计信息
     */
    FileStatistics getFileStatistics(String moduleCode);

}
