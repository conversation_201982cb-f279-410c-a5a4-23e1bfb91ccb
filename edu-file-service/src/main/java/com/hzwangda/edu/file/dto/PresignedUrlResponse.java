package com.hzwangda.edu.file.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 预签名URL响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "预签名URL响应")
@Data
public class PresignedUrlResponse {

    @Schema(description = "文件唯一标识符", example = "f47ac10b-58cc-4372-a567-0e02b2c3d479")
    private String fileId;

    @Schema(description = "预签名上传URL", example = "http://localhost:9000/hr-system-files/contract/123/main_contract/20241201/f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf?X-Amz-Algorithm=...")
    private String uploadUrl;

    @Schema(description = "预签名下载URL", example = "http://localhost:9000/hr-system-files/contract/123/main_contract/20241201/f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf?X-Amz-Algorithm=...")
    private String downloadUrl;

    @Schema(description = "URL过期时间")
    private LocalDateTime expiryTime;

    @Schema(description = "过期时间（秒）", example = "3600")
    private Integer expirySeconds;

    @Schema(description = "上传方法", example = "PUT")
    private String method;

    @Schema(description = "额外的请求头信息")
    private String headers;

    // 构造函数
    public PresignedUrlResponse() {
    }

    public PresignedUrlResponse(String fileId, String uploadUrl, LocalDateTime expiryTime, Integer expirySeconds) {
        this.fileId = fileId;
        this.uploadUrl = uploadUrl;
        this.expiryTime = expiryTime;
        this.expirySeconds = expirySeconds;
        this.method = "PUT";
    }

    public PresignedUrlResponse(String fileId, String downloadUrl, LocalDateTime expiryTime, Integer expirySeconds, boolean isDownload) {
        this.fileId = fileId;
        this.downloadUrl = downloadUrl;
        this.expiryTime = expiryTime;
        this.expirySeconds = expirySeconds;
        this.method = "GET";
    }

    @Override
    public String toString() {
        return "PresignedUrlResponse{" +
                "fileId='" + fileId + '\'' +
                ", uploadUrl='" + uploadUrl + '\'' +
                ", downloadUrl='" + downloadUrl + '\'' +
                ", expiryTime=" + expiryTime +
                ", expirySeconds=" + expirySeconds +
                ", method='" + method + '\'' +
                '}';
    }
}
