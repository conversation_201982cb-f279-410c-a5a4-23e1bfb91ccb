# 测试环境配置
spring:
  application:
    name: edu-file-service-test
  
  # 禁用数据库自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration

# 文件服务配置
file:
  max-file-size: 100MB
  max-request-size: 100MB
  allowed-types:
    - image/jpeg
    - image/png
    - application/pdf
    - text/plain
  presigned-url-expiry: 3600
  path-template: "{module}/{bizId}/{fileType}/{date}/{uuid}.{ext}"
  versioning:
    enabled: true
    max-versions: 10

# MinIO配置（测试用）
minio:
  endpoint: http://localhost:9000
  access-key: minioadmin
  secret-key: minioadmin
  bucket-name: test-bucket

# 日志配置
logging:
  level:
    com.hzwangda.edu.file: DEBUG
    org.springframework.web: DEBUG
