package com.hzwangda.edu.file.config;

import io.minio.BucketExistsArgs;
import io.minio.MinioClient;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@TestPropertySource(properties = {
    "minio.endpoint=https://qyb-minio.hzwangda.com",
    "minio.accessKey=minio",
    "minio.secretKey=p5S3A9nDrUAvCjof",
    "minio.bucketName=hky-hr"
})
public class MinioConnectionTest {

    @Autowired
    private MinioClient minioClient;

    @Value("${minio.bucketName}")
    private String bucketName;

    @Value("${minio.endpoint}")
    private String endpoint;

    @Test
    public void testMinioConnection() {
        assertNotNull(minioClient, "MinioClient should not be null");
        System.out.println("MinIO Endpoint: " + endpoint);
        System.out.println("Bucket Name: " + bucketName);
    }

    @Test
    public void testBucketExists() {
        try {
            boolean exists = minioClient.bucketExists(
                BucketExistsArgs.builder().bucket(bucketName).build()
            );
            System.out.println("Bucket '" + bucketName + "' exists: " + exists);
            // 不强制要求bucket存在，因为可能需要创建
        } catch (Exception e) {
            System.err.println("Error checking bucket existence: " + e.getMessage());
            e.printStackTrace();
            fail("Failed to check bucket existence: " + e.getMessage());
        }
    }
}
