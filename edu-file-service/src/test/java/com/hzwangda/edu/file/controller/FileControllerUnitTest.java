package com.hzwangda.edu.file.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hzwangda.edu.file.dto.FileInfoResponse;
import com.hzwangda.edu.file.dto.FileStatistics;
import com.hzwangda.edu.file.dto.PresignedUrlResponse;
import com.hzwangda.edu.file.service.FileService;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * FileController单元测试（独立测试，不依赖Spring上下文）
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("文件控制器单元测试")
class FileControllerUnitTest {

    private MockMvc mockMvc;

    @Mock
    private FileService fileService;

    @InjectMocks
    private FileController fileController;

    private ObjectMapper objectMapper;
    private FileInfoResponse mockFileInfo;
    private PresignedUrlResponse mockPresignedUrl;
    private FileStatistics mockStatistics;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(fileController).build();
        objectMapper = new ObjectMapper();
        
        // 初始化测试数据
        mockFileInfo = createMockFileInfo();
        mockPresignedUrl = createMockPresignedUrl();
        mockStatistics = createMockStatistics();
    }

    @Test
    @DisplayName("单文件上传 - 成功")
    void uploadSingleFile_Success() throws Exception {
        // Given
        MockMultipartFile file = new MockMultipartFile(
                "file", "test.pdf", "application/pdf", "test content".getBytes());
        
        when(fileService.uploadFile(any(MultipartFile.class), any())).thenReturn(mockFileInfo);

        // When & Then
        mockMvc.perform(multipart("/api/v1/files/upload/single")
                        .file(file)
                        .param("moduleCode", "contract")
                        .param("bizId", "123")
                        .param("fileType", "main_contract")
                        .param("description", "测试文件")
                        .param("overwrite", "false"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("文件上传成功"))
                .andExpect(jsonPath("$.data.fileId").value(mockFileInfo.getFileId()))
                .andExpect(jsonPath("$.data.originalFilename").value(mockFileInfo.getOriginalFilename()));

        // 验证服务方法被调用
        verify(fileService, times(1)).uploadFile(any(MultipartFile.class), any());
    }

    @Test
    @DisplayName("批量文件上传 - 成功")
    void uploadBatchFiles_Success() throws Exception {
        // Given
        MockMultipartFile file1 = new MockMultipartFile(
                "files", "test1.pdf", "application/pdf", "test content 1".getBytes());
        MockMultipartFile file2 = new MockMultipartFile(
                "files", "test2.pdf", "application/pdf", "test content 2".getBytes());
        
        List<FileInfoResponse> mockResponses = Arrays.asList(mockFileInfo, mockFileInfo);
        when(fileService.uploadFiles(anyList(), any())).thenReturn(mockResponses);

        // When & Then
        mockMvc.perform(multipart("/api/v1/files/upload/batch")
                        .file(file1)
                        .file(file2)
                        .param("moduleCode", "contract")
                        .param("bizId", "123")
                        .param("fileType", "main_contract")
                        .param("description", "批量测试文件")
                        .param("overwrite", "false"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("批量文件上传成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2));

        verify(fileService, times(1)).uploadFiles(anyList(), any());
    }

    @Test
    @DisplayName("获取预签名上传URL - 成功")
    void getPresignedUploadUrl_Success() throws Exception {
        // Given
        when(fileService.getPresignedUploadUrl(anyString(), anyString(), any(), any()))
                .thenReturn(mockPresignedUrl);

        // When & Then
        mockMvc.perform(get("/api/v1/files/upload/presigned-url")
                        .param("filename", "test.pdf")
                        .param("contentType", "application/pdf")
                        .param("moduleCode", "contract")
                        .param("bizId", "123")
                        .param("fileType", "main_contract")
                        .param("description", "预签名测试")
                        .param("expirySeconds", "3600"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("预签名URL生成成功"))
                .andExpect(jsonPath("$.data.fileId").value(mockPresignedUrl.getFileId()))
                .andExpect(jsonPath("$.data.uploadUrl").value(mockPresignedUrl.getUploadUrl()));

        verify(fileService, times(1)).getPresignedUploadUrl(anyString(), anyString(), any(), any());
    }

    @Test
    @DisplayName("确认预签名上传完成 - 成功")
    void confirmPresignedUpload_Success() throws Exception {
        // Given
        when(fileService.confirmPresignedUpload(anyString(), anyString())).thenReturn(mockFileInfo);

        // When & Then
        mockMvc.perform(post("/api/v1/files/upload/confirm")
                        .param("fileId", "test-file-id")
                        .param("etag", "test-etag"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("上传确认成功"))
                .andExpect(jsonPath("$.data.fileId").value(mockFileInfo.getFileId()));

        verify(fileService, times(1)).confirmPresignedUpload("test-file-id", "test-etag");
    }

    @Test
    @DisplayName("删除文件（逻辑删除） - 成功")
    void deleteFile_Success() throws Exception {
        // Given
        String fileId = "test-file-id";
        doNothing().when(fileService).deleteFile(fileId);

        // When & Then
        mockMvc.perform(delete("/api/v1/files/{fileId}", fileId))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"))
                .andExpect(jsonPath("$.data").value("文件删除成功"));

        verify(fileService, times(1)).deleteFile(fileId);
    }

    @Test
    @DisplayName("物理删除文件 - 成功")
    void physicalDeleteFile_Success() throws Exception {
        // Given
        String fileId = "test-file-id";
        doNothing().when(fileService).physicalDeleteFile(fileId);

        // When & Then
        mockMvc.perform(delete("/api/v1/files/physical/{fileId}", fileId))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("操作成功"))
                .andExpect(jsonPath("$.data").value("文件物理删除成功"));

        verify(fileService, times(1)).physicalDeleteFile(fileId);
    }

    @Test
    @DisplayName("获取文件信息 - 成功")
    void getFileInfo_Success() throws Exception {
        // Given
        String fileId = "test-file-id";
        when(fileService.getFileInfo(fileId)).thenReturn(mockFileInfo);

        // When & Then
        mockMvc.perform(get("/api/v1/files/{fileId}/info", fileId))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取文件信息成功"))
                .andExpect(jsonPath("$.data.fileId").value(mockFileInfo.getFileId()));

        verify(fileService, times(1)).getFileInfo(fileId);
    }

    @Test
    @DisplayName("根据业务查询文件列表 - 成功")
    void getFilesByBusiness_Success() throws Exception {
        // Given
        List<FileInfoResponse> fileList = Arrays.asList(mockFileInfo);
        Page<FileInfoResponse> mockPage = new PageImpl<>(fileList, PageRequest.of(0, 10), 1);
        
        when(fileService.getFilesByBusiness(anyString(), anyString(), any(Pageable.class)))
                .thenReturn(mockPage);

        // When & Then
        mockMvc.perform(get("/api/v1/files/business")
                        .param("moduleCode", "contract")
                        .param("bizId", "123")
                        .param("page", "0")
                        .param("size", "10"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("查询文件列表成功"))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.totalElements").value(1));

        verify(fileService, times(1)).getFilesByBusiness(eq("contract"), eq("123"), any(Pageable.class));
    }

    @Test
    @DisplayName("健康检查 - 成功")
    void health_Success() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/files/health"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("文件服务运行正常"))
                .andExpect(jsonPath("$.data").value("OK"));

        // 健康检查不需要调用服务层
        verifyNoInteractions(fileService);
    }

    // 创建测试数据的辅助方法
    private FileInfoResponse createMockFileInfo() {
        FileInfoResponse fileInfo = new FileInfoResponse();
        fileInfo.setFileId("test-file-id");
        fileInfo.setOriginalFilename("test.pdf");
        fileInfo.setFileSize(1024L);
        fileInfo.setContentType("application/pdf");
        fileInfo.setFileExtension("pdf");
        fileInfo.setModuleCode("contract");
        fileInfo.setBizId("123");
        fileInfo.setFileType("main_contract");
        fileInfo.setStatus("ACTIVE");
        fileInfo.setVersion(1);
        fileInfo.setDescription("测试文件");
        fileInfo.setDownloadCount(0);
        fileInfo.setCreateTime(LocalDateTime.now());
        fileInfo.setCreateBy("test-user");
        return fileInfo;
    }

    private PresignedUrlResponse createMockPresignedUrl() {
        return new PresignedUrlResponse(
                "test-file-id",
                "http://localhost:9000/test-bucket/test-file.pdf?signature=xxx",
                LocalDateTime.now().plusHours(1),
                3600
        );
    }

    private FileStatistics createMockStatistics() {
        return new FileStatistics(100L, 1024000L, 95L, 5L);
    }

    @Test
    @DisplayName("根据文件名搜索文件 - 成功")
    void searchFilesByName_Success() throws Exception {
        // Given
        List<FileInfoResponse> fileList = Arrays.asList(mockFileInfo);
        Page<FileInfoResponse> mockPage = new PageImpl<>(fileList, PageRequest.of(0, 10), 1);

        when(fileService.searchFilesByName(anyString(), any(Pageable.class))).thenReturn(mockPage);

        // When & Then
        mockMvc.perform(get("/api/v1/files/search")
                        .param("filename", "test")
                        .param("page", "0")
                        .param("size", "10"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("搜索文件成功"))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.totalElements").value(1));

        verify(fileService, times(1)).searchFilesByName(eq("test"), any(Pageable.class));
    }

    @Test
    @DisplayName("获取文件版本列表 - 成功")
    void getFileVersions_Success() throws Exception {
        // Given
        String parentFileId = "parent-file-id";
        List<FileInfoResponse> versions = Arrays.asList(mockFileInfo);
        when(fileService.getFileVersions(parentFileId)).thenReturn(versions);

        // When & Then
        mockMvc.perform(get("/api/v1/files/{parentFileId}/versions", parentFileId))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取文件版本列表成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1));

        verify(fileService, times(1)).getFileVersions(parentFileId);
    }

    @Test
    @DisplayName("检查文件是否存在 - 成功")
    void fileExists_Success() throws Exception {
        // Given
        String fileId = "test-file-id";
        when(fileService.fileExists(fileId)).thenReturn(true);

        // When & Then
        mockMvc.perform(get("/api/v1/files/{fileId}/exists", fileId))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("检查完成"))
                .andExpect(jsonPath("$.data").value(true));

        verify(fileService, times(1)).fileExists(fileId);
    }

    @Test
    @DisplayName("获取文件统计信息 - 成功")
    void getFileStatistics_Success() throws Exception {
        // Given
        String moduleCode = "contract";
        when(fileService.getFileStatistics(moduleCode)).thenReturn(mockStatistics);

        // When & Then
        mockMvc.perform(get("/api/v1/files/statistics")
                        .param("moduleCode", moduleCode))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取统计信息成功"))
                .andExpect(jsonPath("$.data.totalFiles").value(100))
                .andExpect(jsonPath("$.data.totalSize").value(1024000))
                .andExpect(jsonPath("$.data.activeFiles").value(95))
                .andExpect(jsonPath("$.data.deletedFiles").value(5));

        verify(fileService, times(1)).getFileStatistics(moduleCode);
    }

    @Test
    @DisplayName("获取预签名下载URL - 成功")
    void getPresignedDownloadUrl_Success() throws Exception {
        // Given
        String fileId = "test-file-id";
        when(fileService.getPresignedDownloadUrl(fileId, null)).thenReturn(mockPresignedUrl);

        // When & Then
        mockMvc.perform(get("/api/v1/files/download/presigned-url/{fileId}", fileId))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("预签名下载URL生成成功"))
                .andExpect(jsonPath("$.data.fileId").value(mockPresignedUrl.getFileId()));

        verify(fileService, times(1)).getPresignedDownloadUrl(fileId, null);
    }

    // 异常情况测试
    @Test
    @DisplayName("单文件上传 - 服务异常")
    void uploadSingleFile_ServiceException() throws Exception {
        // Given
        MockMultipartFile file = new MockMultipartFile(
                "file", "test.pdf", "application/pdf", "test content".getBytes());

        when(fileService.uploadFile(any(MultipartFile.class), any()))
                .thenThrow(new RuntimeException("上传失败"));

        // When & Then - 期望抛出异常
        try {
            mockMvc.perform(multipart("/api/v1/files/upload/single")
                            .file(file)
                            .param("moduleCode", "contract")
                            .param("bizId", "123")
                            .param("fileType", "main_contract"))
                    .andDo(print());
        } catch (Exception e) {
            // 验证异常被抛出
            assertThat(e.getCause()).isInstanceOf(RuntimeException.class);
            assertThat(e.getCause().getMessage()).isEqualTo("上传失败");
        }

        verify(fileService, times(1)).uploadFile(any(MultipartFile.class), any());
    }

    @Test
    @DisplayName("获取文件信息 - 文件不存在")
    void getFileInfo_FileNotFound() throws Exception {
        // Given
        String fileId = "non-existent-file-id";
        when(fileService.getFileInfo(fileId))
                .thenThrow(new RuntimeException("文件不存在"));

        // When & Then - 期望抛出异常
        try {
            mockMvc.perform(get("/api/v1/files/{fileId}/info", fileId))
                    .andDo(print());
        } catch (Exception e) {
            // 验证异常被抛出
            assertThat(e.getCause()).isInstanceOf(RuntimeException.class);
            assertThat(e.getCause().getMessage()).isEqualTo("文件不存在");
        }

        verify(fileService, times(1)).getFileInfo(fileId);
    }
}
