package com.hzwangda.edu.file;

import io.minio.BucketExistsArgs;
import io.minio.MinioClient;

/**
 * 手动测试MinIO连接的简单工具
 * 可以独立运行来验证MinIO配置
 */
public class ManualMinIOTest {

    public static void main(String[] args) {
        // 使用您的实际配置
        String endpoint = "https://qyb-minio.hzwangda.com";
        String accessKey = "minio";
        String secretKey = "p5S3A9nDrUAvCjof";
        String bucketName = "hky-hr";

        System.out.println("=== Manual MinIO Connection Test ===");
        System.out.println("Endpoint: " + endpoint);
        System.out.println("AccessKey: " + accessKey);
        System.out.println("BucketName: " + bucketName);
        System.out.println();

        try {
            // 创建MinIO客户端
            System.out.println("1. Creating MinIO client...");
            MinioClient minioClient = MinioClient.builder()
                    .endpoint(endpoint)
                    .credentials(accessKey, secretKey)
                    .build();
            System.out.println("   ✓ MinIO client created successfully");

            // 测试连接
            System.out.println("2. Testing connection...");
            minioClient.listBuckets();
            System.out.println("   ✓ Connection successful - can list buckets");

            // 检查存储桶
            System.out.println("3. Checking bucket existence...");
            boolean bucketExists = minioClient.bucketExists(
                BucketExistsArgs.builder().bucket(bucketName).build()
            );
            if (bucketExists) {
                System.out.println("   ✓ Bucket '" + bucketName + "' exists");
            } else {
                System.out.println("   ⚠ Bucket '" + bucketName + "' does not exist");
                System.out.println("   (This is normal - bucket will be created automatically)");
            }

            System.out.println("\n=== Test Result: SUCCESS ===");
            System.out.println("MinIO configuration is correct and service is accessible.");

        } catch (Exception e) {
            System.err.println("\n=== Test Result: FAILED ===");
            System.err.println("Error: " + e.getClass().getSimpleName());
            System.err.println("Message: " + e.getMessage());

            // 提供故障排除建议
            System.err.println("\nTroubleshooting suggestions:");
            if (e.getMessage() != null) {
                if (e.getMessage().contains("Connection refused") ||
                    e.getMessage().contains("UnknownHostException")) {
                    System.err.println("- Check if MinIO server is running");
                    System.err.println("- Verify the endpoint URL is correct");
                    System.err.println("- Check network connectivity");
                } else if (e.getMessage().contains("Access Denied") ||
                          e.getMessage().contains("InvalidAccessKeyId") ||
                          e.getMessage().contains("SignatureDoesNotMatch")) {
                    System.err.println("- Verify accessKey and secretKey are correct");
                    System.err.println("- Check if the user has proper permissions");
                } else if (e.getMessage().contains("SSL") ||
                          e.getMessage().contains("certificate")) {
                    System.err.println("- Check SSL certificate configuration");
                    System.err.println("- Try using HTTP instead of HTTPS for testing");
                } else if (e.getMessage().contains("executor rejected")) {
                    System.err.println("- This is a client-side thread pool issue");
                    System.err.println("- Usually indicates network connectivity problems");
                }
            }

            e.printStackTrace();
        }
    }
}
