# MinIO文件服务API文档

## 概述
本服务提供了完整的MinIO文件管理功能，包括基本文件上传下载、预签名URL和大文件分片上传功能。

## 基础配置
```yaml
minio:
  endpoint: http://hr-minio.hr-infra.svc.cluster.local:9000
  accessKey: rootUser
  secretKey: rootPassword
  bucketName: hr-system-files
```

## API接口

### 1. 基本文件上传
**POST** `/api/files/upload`

**参数：**
- `moduleCode` (String): 模块代码
- `bizEntityId` (String): 业务实体ID
- `file` (MultipartFile): 上传的文件

**响应：**
```json
{
  "result": "SUCCESS",
  "message": "操作成功",
  "data": {
    "id": 1,
    "fileName": "example.pdf",
    "contentType": "application/pdf",
    "size": 1024000,
    "objectName": "module1/entity1/uuid.pdf",
    "uploadTime": "2025-06-20T16:00:00"
  }
}
```

### 2. 文件下载
**GET** `/api/files/download/{fileId}`

直接返回文件流。

### 3. 预签名上传URL
**GET** `/api/files/upload/presigned-url`

**参数：**
- `moduleCode` (String): 模块代码
- `bizEntityId` (String): 业务实体ID
- `fileName` (String): 文件名

**响应：**
```json
{
  "result": "SUCCESS",
  "message": "操作成功",
  "data": {
    "url": "https://minio.example.com/bucket/object?signature=...",
    "objectName": "module1/entity1/uuid.pdf"
  }
}
```

### 4. 预签名下载URL
**GET** `/api/files/download/presigned-url/{fileId}`

**响应：**
```json
{
  "result": "SUCCESS",
  "message": "操作成功",
  "data": {
    "url": "https://minio.example.com/bucket/object?signature=...",
    "objectName": "module1/entity1/uuid.pdf"
  }
}
```

## 大文件分片上传

### 1. 初始化分片上传
**POST** `/api/files/upload/multipart/init`

**参数：**
- `moduleCode` (String): 模块代码
- `bizEntityId` (String): 业务实体ID
- `fileName` (String): 文件名

**响应：**
```json
{
  "result": "SUCCESS",
  "message": "操作成功",
  "data": {
    "uploadId": "uuid-upload-id",
    "objectName": "module1/entity1/uuid.pdf"
  }
}
```

### 2. 上传分片
**PUT** `/api/files/upload/multipart/part`

**参数：**
- `uploadId` (String): 上传ID
- `partNumber` (int): 分片号（1-10000）
- `objectName` (String): 对象名称
- `part` (MultipartFile): 分片文件

**响应：**
```json
{
  "result": "SUCCESS",
  "message": "操作成功",
  "data": {
    "partNumber": 1,
    "eTag": "etag-value"
  }
}
```

### 3. 完成分片上传
**POST** `/api/files/upload/multipart/complete`

**请求体：**
```json
{
  "uploadId": "uuid-upload-id",
  "objectName": "module1/entity1/uuid.pdf",
  "parts": [
    {
      "partNumber": 1,
      "eTag": "etag1"
    },
    {
      "partNumber": 2,
      "eTag": "etag2"
    }
  ]
}
```

**响应：**
```json
{
  "result": "SUCCESS",
  "message": "操作成功",
  "data": null
}
```

## 分片上传使用流程

1. **初始化分片上传**：调用 `/api/files/upload/multipart/init` 获取 `uploadId` 和 `objectName`
2. **分片上传**：将大文件分割成多个分片，逐个调用 `/api/files/upload/multipart/part` 上传
3. **完成上传**：收集所有分片的 `eTag`，调用 `/api/files/upload/multipart/complete` 完成合并

## 错误处理

所有API在出错时返回统一格式：
```json
{
  "result": "ERROR",
  "message": "错误描述",
  "data": null
}
```

常见错误：
- `File cannot be null or empty`: 文件为空
- `Module code cannot be null or empty`: 模块代码为空
- `Part number must be between 1 and 10000`: 分片号超出范围
- `Parts list cannot be empty`: 分片列表为空

## 技术实现说明

### 分片上传实现方式
由于MinIO Java客户端8.5.17版本的API限制，本实现采用了以下策略：

1. **分片存储**：每个分片作为临时对象存储（objectName + ".part." + partNumber）
2. **分片合并**：在完成上传时，读取所有分片并合并为最终对象
3. **清理临时文件**：合并完成后自动删除临时分片对象

### 性能考虑
- 建议分片大小：5MB - 100MB
- 最大分片数：10000
- 支持断点续传：可重新上传失败的分片

### 安全性
- 所有文件操作都有参数验证
- 使用UUID生成唯一对象名避免冲突
- 支持预签名URL减少服务器负载
