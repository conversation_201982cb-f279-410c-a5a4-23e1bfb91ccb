# edu-common-services

教育系统通用基础服务，基于Spring Boot 3.4.x + JDK 17

## 架构

### 服务和端口
- edu-auth-service (8001) - 基于 CAS 的认证和授权
- edu-file-service (8002) - 基于 MinIO 对象存储的文件管理
- edu-audit-service (8004) - 审计日志和合规跟踪
- edu-workflow-service (8006) - 使用 Flowable 的业务流程管理
- edu-notification-service (8007) - 多渠道通知（邮件、短信、微信、钉钉、WebSocket）
- edu-dictionary-service (8008) - 系统字典和配置管理

### 技术栈
- Java 17, Spring Boot 3.4.7, Spring Cloud 2024.0.1
- 服务注册中心: Nacos 2023.0.3.3
- 数据库: PostgreSQL 15+
- 缓存: Redis 7+
- 对象存储: MinIO
- 工作流引擎: Flowable 7.1.0
- 分布式事务: Seata 2.4.0

## 常用开发命令

### 构建命令
```bash
# 构建所有服务
mvn clean install

# 构建特定服务
cd edu-[service-name]
mvn clean package

# 运行特定服务（从服务目录）
mvn spring-boot:run

# 使用特定配置文件运行
mvn spring-boot:run -Dspring.profiles.active=dev
```

### 测试命令
```bash
# 运行所有测试
mvn test

# 运行特定服务的测试
cd edu-[service-name]
mvn test

# 运行特定测试类
mvn test -Dtest=ClassName

# 运行测试并生成覆盖率报告
mvn test jacoco:report
```

### 数据库操作
每个服务都有自己的数据库模式。SQL 脚本位于：
- `src/main/resources/db/migration/`（如果使用 Flyway）
- 每个服务的 `docs/sql/` 目录

## 代码组织

### 通用模块
- **edu-common-core**: 核心工具类、DTO、实体、业务逻辑（无 Web 依赖）
- **edu-common-web**: Web 特定工具类、异常处理器、安全过滤器

### 服务结构
每个服务遵循标准的 Spring Boot 结构：
```
edu-[service-name]/
├── src/main/java/com/edu/[service]/
│   ├── controller/    # REST 端点
│   ├── service/       # 业务逻辑
│   ├── model/         # 实体和 DTO
│   ├── repository/    # 数据访问
│   └── config/        # 配置类
└── src/main/resources/
    ├── application.yml
    └── application-dev.yml
```

## 关键开发模式

### API 文档
所有服务使用 SpringDoc OpenAPI。访问 Swagger UI：
- `http://localhost:[port]/swagger-ui.html`

### 安全
- 所有服务与 edu-auth-service 集成进行认证
- 使用 `@PreAuthorize` 注解进行方法级安全控制
- JWT 令牌用于无状态认证

### 异常处理
- edu-common-web 中的全局异常处理器
- 带有错误代码的标准化错误响应

### 数据库事务
- 使用 `@Transactional` 进行本地事务
- 使用 Seata `@GlobalTransactional` 进行跨服务分布式事务

### 缓存
- 默认启用 Redis 缓存
- 使用 `@Cacheable`、`@CacheEvict` 注解
- 缓存键遵循模式：`service:entity:id`

## CI/CD 流水线

GitLab CI/CD 配置为选择性部署：
```bash
# 通过流水线变量部署特定服务
# 设置 DEPLOY_SERVICE_NAME 变量为逗号分隔的服务名称
# 示例: "auth,file,workflow"
```

详细的流水线使用方法请参见 CI_CD_USAGE_GUIDE.md。