package com.hzwangda.edu.auth.controller;

import com.hzwangda.edu.auth.config.CasConfig;
import com.hzwangda.edu.auth.service.CasCallbackService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apereo.cas.client.validation.Assertion;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.view.RedirectView;

import java.io.IOException;

@RestController
@RequestMapping("/cas/auth")
@RequiredArgsConstructor
public class CasAuthCallbackController {

    private final CasCallbackService casCallbackService;
    private final CasConfig casConfig;

    /**
     * CAS登录成功后的回调接口
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 重定向到前端页面
     */
    @GetMapping("/login")
    public ResponseEntity<Void> casLoginCallback(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 获取当前认证信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        // 获取ticket
        String ticket = request.getParameter("ticket");

        System.out.println(ticket);

        // 获取service参数（回调地址）
        String service = request.getParameter("service");

        Assertion assertion = (Assertion) request.getSession().getAttribute("_const_cas_assertion_");

        // 处理CAS回调
        casCallbackService.handleLoginCallback(authentication, ticket, service);

        // 重定向到前端页面，可以携带token等信息
        response.sendRedirect(service);
        return ResponseEntity.noContent().build();
    }

    /**
     * CAS登出回调接口
     * @param request HTTP请求
     * @param response HTTP响应
     * @return 重定向到登录页面
     */
    @GetMapping("/logout")
    public RedirectView casLogoutCallback(HttpServletRequest request, HttpServletResponse response) {
        // 获取当前认证信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication != null ? authentication.getName() : null;

        // 处理登出回调
        if (username != null) {
            casCallbackService.handleLogoutCallback(username);
        }

        // 清除本地session
        request.getSession().invalidate();

        // 重定向到登录页面
        RedirectView redirectView = new RedirectView();
        redirectView.setUrl("/login");

        return redirectView;
    }
}