# 业务管理系统 - 消息通知服务

## 📋 服务概述

消息通知服务是业务管理系统的核心平台服务之一，负责系统内部消息、提醒、预警的发送，支持邮件、短信、站内信、微信、钉钉等多种通知方式，为各业务模块提供统一的消息通知支持。

## 🎯 核心功能

### 📨 多渠道消息发送
- **邮件通知**：支持HTML邮件、附件发送、批量发送
- **短信通知**：集成阿里云短信服务，支持模板短信
- **站内信通知**：系统内部消息，支持WebSocket实时推送
- **微信通知**：企业微信工作通知
- **钉钉通知**：钉钉工作通知
- **实时通知**：WebSocket实时推送通知

### 🎨 消息模板管理
- **模板设计**：支持FreeMarker和简单变量替换的模板引擎
- **模板分类**：按业务类型和通知渠道分类管理
- **变量替换**：支持动态变量替换和复杂表达式
- **模板验证**：模板语法验证和预览功能

### ⏰ 消息调度管理
- **即时发送**：同步和异步消息发送
- **定时发送**：支持指定时间的定时发送
- **批量发送**：支持大批量消息的分批发送
- **重试机制**：失败消息的自动重试机制

### 📊 消息状态跟踪
- **发送状态**：实时跟踪消息发送状态
- **送达确认**：支持消息送达状态确认
- **阅读状态**：站内信支持已读/未读状态
- **失败处理**：详细的失败原因记录和处理

### 🔔 消息订阅管理
- **订阅设置**：用户可自定义通知偏好
- **渠道选择**：支持多渠道订阅配置
- **频率控制**：防止消息轰炸的频率控制
- **黑名单管理**：支持用户和内容黑名单

### 📈 统计分析功能
- **发送统计**：按时间、渠道、状态统计发送情况
- **成功率分析**：各渠道发送成功率分析
- **用户活跃度**：接收人活跃度统计
- **性能监控**：消息处理性能监控

## 🏗️ 技术架构

### 技术栈
- **框架**：Spring Boot 3.0
- **数据库**：PostgreSQL（消息数据存储）
- **缓存**：Redis（消息队列和缓存）
- **消息队列**：RabbitMQ（异步消息处理）
- **模板引擎**：FreeMarker + 自定义简单模板
- **WebSocket**：Spring WebSocket（实时通知）
- **邮件服务**：Spring Mail
- **安全**：Spring Security
- **文档**：SpringDoc OpenAPI 3

### 架构特点
- **微服务架构**：独立部署，松耦合
- **多渠道支持**：统一接口，多渠道适配
- **异步处理**：高性能异步消息处理
- **可扩展性**：支持新增通知渠道
- **高可用性**：支持集群部署和故障转移

## 📊 数据模型

### 消息模板表 (notification_template)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| template_code | VARCHAR(64) | 模板编码 |
| template_name | VARCHAR(128) | 模板名称 |
| template_type | VARCHAR(32) | 模板类型 |
| channel_type | VARCHAR(32) | 通知渠道 |
| template_title | VARCHAR(255) | 模板标题 |
| template_content | TEXT | 模板内容 |
| template_description | VARCHAR(500) | 模板描述 |
| template_variables | TEXT | 模板变量JSON |
| status | VARCHAR(20) | 状态 |
| priority | INTEGER | 优先级 |
| is_default | BOOLEAN | 是否默认模板 |
| sort_order | INTEGER | 排序顺序 |

### 消息记录表 (notification_message)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| message_id | VARCHAR(64) | 消息ID |
| template_code | VARCHAR(64) | 模板编码 |
| channel_type | VARCHAR(32) | 通知渠道 |
| recipient | VARCHAR(255) | 接收人 |
| recipient_name | VARCHAR(64) | 接收人姓名 |
| recipient_id | BIGINT | 接收人ID |
| message_title | VARCHAR(255) | 消息标题 |
| message_content | TEXT | 消息内容 |
| message_variables | TEXT | 消息变量JSON |
| status | VARCHAR(20) | 消息状态 |
| priority | INTEGER | 优先级 |
| send_time | TIMESTAMP | 发送时间 |
| scheduled_time | TIMESTAMP | 计划发送时间 |
| delivered_time | TIMESTAMP | 送达时间 |
| read_time | TIMESTAMP | 阅读时间 |
| retry_count | INTEGER | 重试次数 |
| max_retry_count | INTEGER | 最大重试次数 |
| error_message | VARCHAR(500) | 错误信息 |
| business_type | VARCHAR(64) | 业务类型 |
| business_id | VARCHAR(64) | 业务ID |
| sender | VARCHAR(64) | 发送人 |
| sender_id | BIGINT | 发送人ID |
| attachments | VARCHAR(255) | 附件信息JSON |
| is_read | BOOLEAN | 是否已读 |
| expire_time | TIMESTAMP | 过期时间 |

### 消息订阅表 (notification_subscription)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| user_id | BIGINT | 用户ID |
| username | VARCHAR(64) | 用户名 |
| user_name | VARCHAR(64) | 用户姓名 |
| template_type | VARCHAR(32) | 模板类型 |
| template_code | VARCHAR(64) | 模板编码 |
| channel_type | VARCHAR(32) | 通知渠道 |
| recipient_address | VARCHAR(255) | 接收地址 |
| status | VARCHAR(20) | 订阅状态 |
| is_enabled | BOOLEAN | 是否启用 |
| priority | INTEGER | 优先级 |
| notification_settings | TEXT | 通知设置JSON |
| remark | VARCHAR(500) | 备注 |

## 🚀 API接口

### 核心接口

#### 1. 发送消息
```http
POST /api/v1/notifications/send

请求体:
{
  "templateCode": "LEAVE_APPROVAL_NOTIFY",
  "channelType": "EMAIL",
  "recipients": ["<EMAIL>"],
  "recipientIds": [1],
  "recipientNames": ["张三"],
  "messageTitle": "【业务系统】请假审批通知",
  "messageContent": "您的请假申请已提交，请等待审批。",
  "variables": {
    "userName": "张三",
    "leaveType": "事假",
    "leaveDays": 2
  },
  "priority": 50,
  "businessType": "WORKFLOW",
  "businessId": "LEAVE_20241219_001",
  "async": true
}
```

#### 2. 批量发送消息
```http
POST /api/v1/notifications/send/batch

请求体: [
  {
    "channelType": "EMAIL",
    "recipients": ["<EMAIL>"],
    "messageTitle": "通知1",
    "messageContent": "内容1"
  },
  {
    "channelType": "INTERNAL",
    "recipients": ["user2"],
    "messageTitle": "通知2",
    "messageContent": "内容2"
  }
]
```

#### 3. 根据模板发送消息
```http
POST /api/v1/notifications/send/template?templateCode=LEAVE_APPROVAL_NOTIFY&channelType=EMAIL&recipients=<EMAIL>

请求体:
{
  "userName": "张三",
  "leaveType": "事假",
  "leaveDays": 2,
  "startDate": "2024-12-20",
  "endDate": "2024-12-21"
}
```

#### 4. 查询未读消息
```http
GET /api/v1/notifications/messages/unread?recipientId=1

响应:
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "messageId": "MSG_20241219_001",
      "messageTitle": "【业务系统】请假审批通知",
      "messageContent": "您的请假申请已提交，请等待审批。",
      "status": "SENT",
      "isRead": false,
      "createTime": "2024-12-19T10:30:00"
    }
  ]
}
```

#### 5. 标记消息为已读
```http
POST /api/v1/notifications/messages/{messageId}/read?recipientId=1

响应:
{
  "code": 200,
  "message": "操作成功",
  "data": true
}
```

### 完整API文档
启动服务后访问: http://localhost:8007/swagger-ui.html

## 🎨 支持的通知渠道

### 邮件通知 (EMAIL)
- **功能**：HTML邮件、纯文本邮件、附件支持
- **配置**：SMTP服务器配置
- **特点**：支持富文本、附件、批量发送

### 短信通知 (SMS)
- **功能**：模板短信、验证码短信
- **服务商**：阿里云短信服务
- **特点**：快速送达、成本较高

### 站内信通知 (INTERNAL)
- **功能**：系统内部消息、实时推送
- **技术**：WebSocket + 数据库存储
- **特点**：实时性强、支持已读状态

### 企业微信通知 (WECHAT)
- **功能**：工作通知、应用消息
- **集成**：企业微信API
- **特点**：移动端友好、到达率高

### 钉钉通知 (DINGTALK)
- **功能**：工作通知、群消息
- **集成**：钉钉开放平台API
- **特点**：办公场景、即时到达

### WebSocket实时通知 (WEBSOCKET)
- **功能**：浏览器实时推送
- **技术**：Spring WebSocket + STOMP
- **特点**：实时性最强、仅限在线用户

## 🎯 支持的业务场景

### 工作流通知
- **流程启动通知**：流程发起时通知相关人员
- **任务分配通知**：任务分配给处理人时通知
- **审批结果通知**：审批完成后通知申请人
- **流程完成通知**：整个流程完成时通知

### 系统通知
- **系统维护通知**：系统升级维护通知
- **安全警告通知**：安全事件警告
- **功能更新通知**：新功能上线通知
- **重要公告通知**：重要公告发布

### 业务提醒
- **合同到期提醒**：合同即将到期提醒
- **考核截止提醒**：考核任务截止提醒
- **培训报名提醒**：培训活动报名提醒
- **生日祝福提醒**：员工生日祝福

### 异常告警
- **系统异常告警**：系统运行异常告警
- **数据异常告警**：数据异常情况告警
- **安全事件告警**：安全事件实时告警
- **性能告警**：系统性能异常告警

## 🚀 快速开始

### 1. 环境准备
```bash
# 启动基础设施服务
docker-compose up -d postgresql redis rabbitmq

# 等待服务启动完成
docker-compose ps
```

### 2. 编译运行
```bash
# 编译项目
mvn clean compile

# 运行服务
mvn spring-boot:run

# 或者打包后运行
mvn clean package
java -jar target/hky-notification-service-1.0.0-SNAPSHOT.jar
```

### 3. 验证服务
```bash
# 健康检查
curl http://localhost:8007/api/v1/notifications/health

# 查看API文档
open http://localhost:8007/swagger-ui.html

# 测试WebSocket连接
open http://localhost:8007/ws/notifications
```

## 🎨 消息模板设计

### 1. 简单变量替换
```text
标题: 【业务系统】${eventType}通知
内容: 
亲爱的${userName}，
您的${businessType}申请已${status}。
申请时间：${applyTime}
处理时间：${processTime}
```

### 2. FreeMarker模板
```html
<!DOCTYPE html>
<html>
<head>
    <title>${title}</title>
</head>
<body>
    <h2>【业务管理系统】${eventType}通知</h2>
    <p>亲爱的${userName}，</p>
    
    <#if status == "APPROVED">
        <p style="color: green;">恭喜！您的${businessType}申请已通过审批。</p>
    <#elseif status == "REJECTED">
        <p style="color: red;">很抱歉，您的${businessType}申请未通过审批。</p>
    <#else>
        <p>您的${businessType}申请正在处理中，请耐心等待。</p>
    </#if>
    
    <table border="1">
        <tr><td>申请类型</td><td>${businessType}</td></tr>
        <tr><td>申请时间</td><td>${applyTime}</td></tr>
        <tr><td>处理时间</td><td>${processTime}</td></tr>
        <#if comment??>
        <tr><td>审批意见</td><td>${comment}</td></tr>
        </#if>
    </table>
    
    <p>如有疑问，请联系人事处。</p>
</body>
</html>
```

## 🔍 监控与运维

### 健康检查
- **应用健康**: GET /api/v1/notifications/health
- **系统健康**: GET /actuator/health
- **渠道状态**: 各通知渠道可用性检查

### 消息监控
- **发送统计**: 实时监控消息发送情况
- **成功率监控**: 各渠道发送成功率
- **性能监控**: 消息处理性能指标

### 运维功能
- **Prometheus**: GET /actuator/prometheus
- **JVM指标**: 内存、GC、线程等
- **业务指标**: 消息发送数、成功率等

## ⚙️ 配置说明

### 核心配置
```yaml
hky:
  notification:
    default:
      priority: 50                    # 默认优先级
      max-retry-count: 3              # 最大重试次数
      expire-hours: 72                # 消息过期时间(小时)
    
    channels:
      email:
        enabled: true                 # 是否启用邮件通知
        timeout-seconds: 30           # 发送超时时间
      sms:
        enabled: false                # 是否启用短信通知
        provider: aliyun              # 短信服务商
      internal:
        enabled: true                 # 是否启用站内信
        websocket-enabled: true       # 是否启用WebSocket
```

### 邮件配置
```yaml
spring:
  mail:
    host: smtp.hky.edu.cn            # SMTP服务器
    port: 587                        # SMTP端口
    username: <EMAIL>     # 发件人邮箱
    password: your_email_password    # 邮箱密码
    personal: 业务管理系统          # 发件人名称
```

### 第三方服务配置
```yaml
third-party:
  aliyun:
    sms:
      access-key-id: your_access_key_id      # 阿里云AccessKey
      access-key-secret: your_access_key_secret
      region: cn-hangzhou                    # 地域
      sign-name: 杭州望达                      # 短信签名
  
  wechat:
    corp-id: your_corp_id                    # 企业微信CorpID
    corp-secret: your_corp_secret            # 企业微信Secret
    agent-id: your_agent_id                  # 应用AgentID
```

## 🔒 安全机制

### 权限控制
- `notification:send` - 发送消息权限
- `notification:view` - 查看消息权限
- `notification:read` - 标记已读权限
- `notification:manage` - 管理消息权限
- `notification:statistics` - 查看统计权限
- `notification:system` - 系统通知权限

### 数据安全
- **消息加密**: 敏感消息内容加密存储
- **访问控制**: 严格的消息访问权限控制
- **审计日志**: 完整的消息操作审计日志
- **防刷机制**: 防止消息轰炸的频率控制

## 📈 性能优化

### 异步处理
- **异步发送**: 支持异步消息发送
- **批量处理**: 大批量消息分批处理
- **队列缓冲**: 消息队列削峰填谷

### 缓存优化
- **模板缓存**: 消息模板缓存
- **配置缓存**: 系统配置缓存
- **结果缓存**: 发送结果缓存

### 数据库优化
- **索引优化**: 针对查询优化索引
- **分页查询**: 避免大数据量查询
- **历史数据**: 定期清理历史数据

## 🧪 测试

### 单元测试
```bash
mvn test
```

### 集成测试
```bash
mvn integration-test
```

### 消息发送测试
使用Swagger UI或Postman进行消息发送测试

## 📝 开发指南

### 新增通知渠道
1. 实现ChannelHandler接口
2. 注册为Spring Bean
3. 配置渠道参数
4. 添加渠道枚举

### 自定义消息模板
1. 设计模板结构
2. 定义模板变量
3. 配置模板引擎
4. 测试模板渲染

### 集成业务系统
1. 引入通知服务依赖
2. 配置服务地址
3. 调用发送接口
4. 处理发送结果

## 📋 版本历史

- **v1.0.0**: 初始版本，实现多渠道消息通知、模板管理、异步处理、状态跟踪等核心功能

## 📞 联系方式

- 项目负责人: HKY-HR-System
- 技术支持: <EMAIL>
- 文档更新: 2024-12-19
