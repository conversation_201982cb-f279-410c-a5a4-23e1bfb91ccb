package com.hzwangda.edu.notification.service;

import com.hzwangda.edu.notification.entity.SmsRecord;
import com.hzwangda.edu.notification.entity.SmsTemplate;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 短信服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public interface SmsService {

    /**
     * 发送短信
     *
     * @param phoneNumber 手机号码
     * @param content 短信内容
     * @return 发送记录
     */
    SmsRecord sendSms(String phoneNumber, String content);

    /**
     * 使用模板发送短信
     *
     * @param phoneNumber 手机号码
     * @param templateCode 模板编码
     * @param templateParams 模板参数
     * @return 发送记录
     */
    SmsRecord sendSmsWithTemplate(String phoneNumber, String templateCode, Map<String, Object> templateParams);

    /**
     * 批量发送短信
     *
     * @param phoneNumbers 手机号码列表
     * @param content 短信内容
     * @return 发送记录列表
     */
    List<SmsRecord> sendBatchSms(List<String> phoneNumbers, String content);

    /**
     * 批量使用模板发送短信
     *
     * @param phoneNumbers 手机号码列表
     * @param templateCode 模板编码
     * @param templateParams 模板参数
     * @return 发送记录列表
     */
    List<SmsRecord> sendBatchSmsWithTemplate(List<String> phoneNumbers, String templateCode, Map<String, Object> templateParams);

    /**
     * 查询发送记录
     *
     * @param recordId 记录ID
     * @return 发送记录
     */
    SmsRecord getSmsRecord(String recordId);

    /**
     * 查询发送记录列表
     *
     * @param phoneNumber 手机号码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发送记录列表
     */
    List<SmsRecord> getSmsRecords(String phoneNumber, String startTime, String endTime);

    /**
     * 重试发送失败的短信
     *
     * @param recordId 记录ID
     * @return 是否重试成功
     */
    boolean retrySms(String recordId);

    /**
     * 处理短信回调
     *
     * @param callbackData 回调数据
     * @return 处理结果
     */
    boolean handleSmsCallback(Map<String, Object> callbackData);

    /**
     * 检查发送限制
     *
     * @param phoneNumber 手机号码
     * @return 是否允许发送
     */
    boolean checkSendLimit(String phoneNumber);

    /**
     * 获取短信发送统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    Map<String, Object> getSmsStatistics(String startTime, String endTime);

    /**
     * 创建短信模板
     *
     * @param template 模板信息
     * @return 创建的模板
     */
    SmsTemplate createSmsTemplate(SmsTemplate template);

    /**
     * 更新短信模板
     *
     * @param template 模板信息
     * @return 更新的模板
     */
    SmsTemplate updateSmsTemplate(SmsTemplate template);

    /**
     * 删除短信模板
     *
     * @param templateCode 模板编码
     * @return 是否删除成功
     */
    boolean deleteSmsTemplate(String templateCode);

    /**
     * 获取短信模板
     *
     * @param templateCode 模板编码
     * @return 模板信息
     */
    SmsTemplate getSmsTemplate(String templateCode);

    /**
     * 获取短信模板列表
     *
     * @return 模板列表
     */
    List<SmsTemplate> getSmsTemplates();

    /**
     * 验证短信模板
     *
     * @param templateCode 模板编码
     * @param templateParams 模板参数
     * @return 验证结果
     */
    boolean validateSmsTemplate(String templateCode, Map<String, Object> templateParams);

    // ==================== 增强功能 ====================

    /**
     * 查询短信发送状态
     *
     * @param recordId 记录ID
     * @return 发送状态
     */
    String querySmsStatus(String recordId);

    /**
     * 定时发送短信
     *
     * @param phoneNumber 手机号
     * @param content 内容
     * @param sendTime 发送时间
     * @return 发送记录
     */
    SmsRecord scheduleSms(String phoneNumber, String content, LocalDateTime sendTime);

    /**
     * 定时发送模板短信
     *
     * @param phoneNumber 手机号
     * @param templateCode 模板编码
     * @param templateParams 模板参数
     * @param sendTime 发送时间
     * @return 发送记录
     */
    SmsRecord scheduleSmsWithTemplate(String phoneNumber, String templateCode,
                                    Map<String, Object> templateParams, LocalDateTime sendTime);

    /**
     * 取消定时短信
     *
     * @param recordId 记录ID
     * @return 是否取消成功
     */
    boolean cancelScheduledSms(String recordId);

    /**
     * 获取短信余额
     *
     * @return 余额信息
     */
    Map<String, Object> getSmsBalance();

    /**
     * 同步短信状态
     *
     * @param recordId 记录ID
     * @return 是否同步成功
     */
    boolean syncSmsStatus(String recordId);
}
