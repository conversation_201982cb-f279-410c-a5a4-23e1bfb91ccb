package com.hzwangda.edu.notification.dto;

import com.hzwangda.edu.notification.enums.MessageStatus;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 消息状态更新请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class MessageStatusUpdateRequest {

    /**
     * 消息ID
     */
    @NotNull(message = "消息ID不能为空")
    private Long messageId;

    /**
     * 新状态
     */
    @NotNull(message = "消息状态不能为空")
    private MessageStatus status;

    /**
     * 错误信息（可选）
     */
    private String errorMessage;

    /**
     * 错误码（可选）
     */
    private String errorCode;

    /**
     * 更新原因
     */
    private String reason;

    /**
     * 操作人（可选）
     */
    private String operator;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 外部消息ID
     */
    private String externalId;

    // 构造函数
    public MessageStatusUpdateRequest() {
    }

    public MessageStatusUpdateRequest(Long messageId, MessageStatus status) {
        this.messageId = messageId;
        this.status = status;
    }

    public MessageStatusUpdateRequest(Long messageId, MessageStatus status, String errorMessage) {
        this.messageId = messageId;
        this.status = status;
        this.errorMessage = errorMessage;
    }

    public MessageStatusUpdateRequest(Long messageId, MessageStatus status, String errorMessage, String reason) {
        this.messageId = messageId;
        this.status = status;
        this.errorMessage = errorMessage;
        this.reason = reason;
    }

    /**
     * 创建成功状态更新请求
     *
     * @param messageId 消息ID
     * @param status 状态
     * @return 更新请求
     */
    public static MessageStatusUpdateRequest success(Long messageId, MessageStatus status) {
        return new MessageStatusUpdateRequest(messageId, status);
    }

    /**
     * 创建失败状态更新请求
     *
     * @param messageId 消息ID
     * @param errorMessage 错误信息
     * @return 更新请求
     */
    public static MessageStatusUpdateRequest failed(Long messageId, String errorMessage) {
        return new MessageStatusUpdateRequest(messageId, MessageStatus.FAILED, errorMessage);
    }

    /**
     * 创建失败状态更新请求（带错误码）
     *
     * @param messageId 消息ID
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     * @return 更新请求
     */
    public static MessageStatusUpdateRequest failed(Long messageId, String errorCode, String errorMessage) {
        MessageStatusUpdateRequest request = new MessageStatusUpdateRequest(messageId, MessageStatus.FAILED, errorMessage);
        request.setErrorCode(errorCode);
        return request;
    }

    /**
     * 创建重试状态更新请求
     *
     * @param messageId 消息ID
     * @param retryCount 重试次数
     * @param reason 重试原因
     * @return 更新请求
     */
    public static MessageStatusUpdateRequest retry(Long messageId, Integer retryCount, String reason) {
        MessageStatusUpdateRequest request = new MessageStatusUpdateRequest(messageId, MessageStatus.PENDING, null, reason);
        request.setRetryCount(retryCount);
        return request;
    }

    @Override
    public String toString() {
        return "MessageStatusUpdateRequest{" +
                "messageId=" + messageId +
                ", status=" + status +
                ", errorMessage='" + errorMessage + '\'' +
                ", errorCode='" + errorCode + '\'' +
                ", reason='" + reason + '\'' +
                ", operator='" + operator + '\'' +
                ", channelType='" + channelType + '\'' +
                ", retryCount=" + retryCount +
                ", externalId='" + externalId + '\'' +
                '}';
    }
}
