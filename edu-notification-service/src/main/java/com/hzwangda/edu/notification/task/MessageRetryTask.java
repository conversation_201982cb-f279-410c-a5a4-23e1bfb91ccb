package com.hzwangda.edu.notification.task;

import com.hzwangda.edu.notification.config.MessageRetryConfig;
import com.hzwangda.edu.notification.service.MessageRetryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 消息重试定时任务
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "hky.notification.retry.enabled", havingValue = "true", matchIfMissing = true)
public class MessageRetryTask {

    @Autowired
    private MessageRetryService messageRetryService;

    @Autowired
    private MessageRetryConfig retryConfig;

    /**
     * 处理重试队列（根据配置的扫描间隔执行）
     */
    @Scheduled(fixedDelayString = "#{${hky.notification.retry.schedule.scan-interval-seconds:30} * 1000}")
    public void processRetryQueue() {
        if (!retryConfig.isEnabled() || !messageRetryService.isRetryProcessingActive()) {
            return;
        }

        try {
            log.debug("开始执行消息重试定时任务");

            Map<String, Object> result = messageRetryService.processRetryQueue();

            Integer processedCount = (Integer) result.get("processedCount");
            Integer successCount = (Integer) result.get("successCount");
            Integer failedCount = (Integer) result.get("failedCount");
            Integer deadLetterCount = (Integer) result.get("deadLetterCount");

            if (processedCount != null && processedCount > 0) {
                log.info("消息重试任务完成: processed={}, success={}, failed={}, deadLetter={}",
                        processedCount, successCount, failedCount, deadLetterCount);
            }

        } catch (Exception e) {
            log.error("消息重试定时任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 清理死信队列（每小时执行一次）
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void cleanupDeadLetterQueue() {
        if (!retryConfig.isEnabled() || !retryConfig.getDeadLetter().isEnabled() ||
            !retryConfig.getDeadLetter().isAutoCleanup()) {
            return;
        }

        try {
            log.debug("开始清理死信队列");

            // 清理超过保留期的死信消息
            LocalDateTime beforeTime = LocalDateTime.now().minusDays(retryConfig.getDeadLetter().getRetentionDays());
            long cleanedCount = messageRetryService.cleanupDeadLetterQueue(beforeTime);

            if (cleanedCount > 0) {
                log.info("死信队列清理完成: cleanedCount={}", cleanedCount);
            }

        } catch (Exception e) {
            log.error("死信队列清理任务执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 生成重试统计报告（每天凌晨1点执行）
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void generateRetryStatisticsReport() {
        if (!retryConfig.isEnabled()) {
            return;
        }

        try {
            log.debug("开始生成重试统计报告");

            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(1);

            Map<String, Object> statistics = messageRetryService.getRetryStatistics(startTime, endTime);

            log.info("重试统计报告: {}", statistics);

            // TODO: 可以将统计报告发送给管理员或保存到数据库

        } catch (Exception e) {
            log.error("生成重试统计报告失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 监控重试队列状态（每5分钟执行一次）
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void monitorRetryQueueStatus() {
        if (!retryConfig.isEnabled()) {
            return;
        }

        try {
            Map<String, Object> status = messageRetryService.getRetryQueueStatus();

            Integer retryQueueSize = (Integer) status.get("retryQueueSize");
            Integer deadLetterQueueSize = (Integer) status.get("deadLetterQueueSize");
            Long pendingRetryCount = (Long) status.get("pendingRetryCount");

            // 检查队列大小是否异常
            if (retryQueueSize != null && retryQueueSize > 1000) {
                log.warn("重试队列大小异常: retryQueueSize={}", retryQueueSize);
            }

            if (deadLetterQueueSize != null && deadLetterQueueSize > 500) {
                log.warn("死信队列大小异常: deadLetterQueueSize={}", deadLetterQueueSize);
            }

            if (pendingRetryCount != null && pendingRetryCount > 100) {
                log.warn("待重试消息数量较多: pendingRetryCount={}", pendingRetryCount);
            }

            // 记录详细状态（仅在DEBUG级别）
            log.debug("重试队列状态监控: retryQueue={}, deadLetter={}, pending={}",
                     retryQueueSize, deadLetterQueueSize, pendingRetryCount);

        } catch (Exception e) {
            log.error("重试队列状态监控失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 重试配置健康检查（每30分钟执行一次）
     */
    @Scheduled(fixedRate = 1800000) // 30分钟
    public void healthCheckRetryConfiguration() {
        if (!retryConfig.isEnabled()) {
            return;
        }

        try {
            log.debug("开始重试配置健康检查");

            // 检查重试处理是否正常
            if (!messageRetryService.isRetryProcessingActive()) {
                log.warn("重试处理已暂停");
            }

            // 检查各渠道重试策略配置
            Map<String, Map<String, Object>> allStrategies = messageRetryService.getAllRetryStrategies();

            for (Map.Entry<String, Map<String, Object>> entry : allStrategies.entrySet()) {
                String channelType = entry.getKey();
                Map<String, Object> strategy = entry.getValue();

                Integer maxRetryCount = (Integer) strategy.get("maxRetryCount");
                Long maxDelaySeconds = (Long) strategy.get("maxDelaySeconds");

                // 检查配置合理性
                if (maxRetryCount != null && maxRetryCount > 10) {
                    log.warn("渠道重试次数配置过高: channelType={}, maxRetryCount={}", channelType, maxRetryCount);
                }

                if (maxDelaySeconds != null && maxDelaySeconds > 7200) { // 2小时
                    log.warn("渠道最大延迟时间配置过长: channelType={}, maxDelaySeconds={}", channelType, maxDelaySeconds);
                }
            }

            log.debug("重试配置健康检查完成");

        } catch (Exception e) {
            log.error("重试配置健康检查失败: {}", e.getMessage(), e);
        }
    }
}
