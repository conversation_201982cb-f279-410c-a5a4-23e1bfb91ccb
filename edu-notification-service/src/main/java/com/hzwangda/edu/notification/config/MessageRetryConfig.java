package com.hzwangda.edu.notification.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 消息重试配置类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@Component
@ConfigurationProperties(prefix = "hky.notification.retry")
public class MessageRetryConfig {

    /**
     * 是否启用重试机制
     */
    private boolean enabled = true;

    /**
     * 默认重试配置
     */
    private RetryStrategy defaultStrategy = new RetryStrategy();

    /**
     * 按渠道类型配置的重试策略
     */
    private Map<String, RetryStrategy> channelStrategies = new HashMap<>();

    /**
     * 死信队列配置
     */
    private DeadLetterConfig deadLetter = new DeadLetterConfig();

    /**
     * 重试任务调度配置
     */
    private ScheduleConfig schedule = new ScheduleConfig();

    /**
     * 重试策略配置
     */
    @Data
    public static class RetryStrategy {
        /**
         * 最大重试次数
         */
        private int maxRetryCount = 3;

        /**
         * 初始延迟时间（秒）
         */
        private long initialDelaySeconds = 60;

        /**
         * 最大延迟时间（秒）
         */
        private long maxDelaySeconds = 1800; // 30分钟

        /**
         * 延迟倍数（指数退避）
         */
        private double delayMultiplier = 2.0;

        /**
         * 重试策略类型（FIXED, EXPONENTIAL, LINEAR）
         */
        private String strategyType = "EXPONENTIAL";

        /**
         * 是否启用随机抖动
         */
        private boolean enableJitter = true;

        /**
         * 抖动因子（0.0-1.0）
         */
        private double jitterFactor = 0.1;

        /**
         * 重试条件（错误码列表）
         */
        private String[] retryableErrorCodes = {"NETWORK_ERROR", "TIMEOUT", "RATE_LIMIT", "TEMPORARY_FAILURE"};

        /**
         * 不重试条件（错误码列表）
         */
        private String[] nonRetryableErrorCodes = {"INVALID_RECIPIENT", "INVALID_CONTENT", "PERMISSION_DENIED"};
    }

    /**
     * 死信队列配置
     */
    @Data
    public static class DeadLetterConfig {
        /**
         * 是否启用死信队列
         */
        private boolean enabled = true;

        /**
         * 死信消息保留时间（天）
         */
        private int retentionDays = 7;

        /**
         * 死信消息最大数量
         */
        private long maxMessages = 10000;

        /**
         * 是否自动清理过期死信
         */
        private boolean autoCleanup = true;

        /**
         * 死信处理策略（STORE, DISCARD, CALLBACK）
         */
        private String handleStrategy = "STORE";

        /**
         * 回调URL（当策略为CALLBACK时使用）
         */
        private String callbackUrl;
    }

    /**
     * 调度配置
     */
    @Data
    public static class ScheduleConfig {
        /**
         * 重试任务扫描间隔（秒）
         */
        private long scanIntervalSeconds = 30;

        /**
         * 每次处理的最大消息数量
         */
        private int batchSize = 100;

        /**
         * 重试任务线程池大小
         */
        private int threadPoolSize = 5;

        /**
         * 是否启用分布式锁
         */
        private boolean enableDistributedLock = true;

        /**
         * 分布式锁超时时间（秒）
         */
        private long lockTimeoutSeconds = 300;
    }

    /**
     * 获取指定渠道的重试策略
     */
    public RetryStrategy getRetryStrategy(String channelType) {
        return channelStrategies.getOrDefault(channelType, defaultStrategy);
    }

    /**
     * 设置渠道重试策略
     */
    public void setChannelRetryStrategy(String channelType, RetryStrategy strategy) {
        channelStrategies.put(channelType, strategy);
    }

    /**
     * 初始化默认渠道策略
     */
    public void initDefaultChannelStrategies() {
        // 邮件重试策略
        RetryStrategy emailStrategy = new RetryStrategy();
        emailStrategy.setMaxRetryCount(5);
        emailStrategy.setInitialDelaySeconds(120);
        emailStrategy.setMaxDelaySeconds(3600);
        channelStrategies.put("EMAIL", emailStrategy);

        // 短信重试策略
        RetryStrategy smsStrategy = new RetryStrategy();
        smsStrategy.setMaxRetryCount(3);
        smsStrategy.setInitialDelaySeconds(60);
        smsStrategy.setMaxDelaySeconds(900);
        channelStrategies.put("SMS", smsStrategy);

        // 微信重试策略
        RetryStrategy wechatStrategy = new RetryStrategy();
        wechatStrategy.setMaxRetryCount(3);
        wechatStrategy.setInitialDelaySeconds(30);
        wechatStrategy.setMaxDelaySeconds(600);
        channelStrategies.put("WECHAT", wechatStrategy);

        // 钉钉重试策略
        RetryStrategy dingtalkStrategy = new RetryStrategy();
        dingtalkStrategy.setMaxRetryCount(3);
        dingtalkStrategy.setInitialDelaySeconds(30);
        dingtalkStrategy.setMaxDelaySeconds(600);
        channelStrategies.put("DINGTALK", dingtalkStrategy);

        // 站内信重试策略
        RetryStrategy internalStrategy = new RetryStrategy();
        internalStrategy.setMaxRetryCount(2);
        internalStrategy.setInitialDelaySeconds(10);
        internalStrategy.setMaxDelaySeconds(300);
        channelStrategies.put("INTERNAL", internalStrategy);
    }
}
