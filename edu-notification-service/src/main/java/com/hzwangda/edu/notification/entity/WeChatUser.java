package com.hzwangda.edu.notification.entity;

import com.hzwangda.edu.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;

/**
 * 微信用户绑定实体
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "notification_wechat_user")
public class WeChatUser extends BaseEntity {

    /**
     * 系统用户ID
     */
    @Column(name = "user_id", nullable = false, length = 64)
    private String userId;

    /**
     * 微信类型（work, service, mini）
     */
    @Column(name = "wechat_type", nullable = false, length = 20)
    private String wechatType;

    /**
     * 微信OpenID
     */
    @Column(name = "open_id", length = 100)
    private String openId;

    /**
     * 微信UnionID
     */
    @Column(name = "union_id", length = 100)
    private String unionId;

    /**
     * 企业微信用户ID
     */
    @Column(name = "work_user_id", length = 100)
    private String workUserId;

    /**
     * 微信昵称
     */
    @Column(name = "nickname", length = 200)
    private String nickname;

    /**
     * 头像URL
     */
    @Column(name = "avatar_url", length = 500)
    private String avatarUrl;

    /**
     * 性别（0-未知，1-男，2-女）
     */
    @Column(name = "gender")
    private Integer gender;

    /**
     * 语言
     */
    @Column(name = "language", length = 20)
    private String language;

    /**
     * 城市
     */
    @Column(name = "city", length = 100)
    private String city;

    /**
     * 省份
     */
    @Column(name = "province", length = 100)
    private String province;

    /**
     * 国家
     */
    @Column(name = "country", length = 100)
    private String country;

    /**
     * 手机号码
     */
    @Column(name = "mobile", length = 20)
    private String mobile;

    /**
     * 邮箱
     */
    @Column(name = "email", length = 100)
    private String email;

    /**
     * 部门ID列表（JSON格式）
     */
    @Column(name = "department_ids", columnDefinition = "TEXT")
    private String departmentIds;

    /**
     * 职位
     */
    @Column(name = "position", length = 100)
    private String position;

    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;

    /**
     * 是否关注/加入
     */
    @Column(name = "subscribed", nullable = false)
    private Boolean subscribed = true;

    /**
     * 关注/加入时间
     */
    @Column(name = "subscribe_time")
    private Long subscribeTime;

    /**
     * 扩展信息（JSON格式）
     */
    @Column(name = "extra_info", columnDefinition = "TEXT")
    private String extraInfo;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
}
