package com.hzwangda.edu.notification.enums;

/**
 * 模板类型枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum TemplateType {

    // 工作流相关通知
    WORKFLOW("工作流", "工作流相关的通知消息"),

    // 系统通知
    SYSTEM("系统通知", "系统级别的通知消息"),

    // 考勤管理通知
    ATTENDANCE("考勤管理", "考勤相关的通知消息"),

    // 人事变动通知
    PERSONNEL("人事变动", "人事变动相关的通知消息"),

    // 合同管理通知
    CONTRACT("合同管理", "合同相关的通知消息"),

    // 薪酬福利通知
    COMPENSATION("薪酬福利", "薪酬福利相关的通知消息"),

    // 招聘管理通知
    RECRUITMENT("招聘管理", "招聘相关的通知消息"),

    // 师资发展通知
    FACULTY("师资发展", "师资发展相关的通知消息"),

    // 考核管理通知
    APPRAISAL("考核管理", "考核相关的通知消息"),

    // 职称评聘通知
    TITLE("职称评聘", "职称评聘相关的通知消息"),

    // 组织管理通知
    ORGANIZATION("组织管理", "组织架构相关的通知消息"),

    // 员工信息通知
    EMPLOYEE("员工信息", "员工信息相关的通知消息"),

    // 安全通知
    SECURITY("安全通知", "系统安全相关的通知消息"),

    // 提醒通知
    REMINDER("提醒通知", "各类提醒通知消息"),

    // 公告通知
    ANNOUNCEMENT("公告通知", "系统公告通知消息"),

    // 营销通知
    MARKETING("营销通知", "营销推广相关的通知消息"),

    // 其他通知
    OTHER("其他", "其他类型的通知消息");

    private final String description;
    private final String detail;

    TemplateType(String description, String detail) {
        this.description = description;
        this.detail = detail;
    }

    public String getDescription() {
        return description;
    }

    public String getDetail() {
        return detail;
    }

    public String getCode() {
        return this.name();
    }

    /**
     * 根据代码获取枚举
     */
    public static TemplateType fromCode(String code) {
        for (TemplateType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return OTHER;
    }

    /**
     * 判断是否为业务流程类型
     */
    public boolean isBusinessProcess() {
        return this == WORKFLOW || this == ATTENDANCE || this == PERSONNEL ||
               this == CONTRACT || this == COMPENSATION || this == RECRUITMENT ||
               this == FACULTY || this == APPRAISAL || this == TITLE;
    }

    /**
     * 判断是否为系统管理类型
     */
    public boolean isSystemManagement() {
        return this == SYSTEM || this == ORGANIZATION || this == EMPLOYEE || this == SECURITY;
    }

    /**
     * 判断是否为用户交互类型
     */
    public boolean isUserInteraction() {
        return this == REMINDER || this == ANNOUNCEMENT || this == MARKETING;
    }

    /**
     * 获取默认优先级
     */
    public int getDefaultPriority() {
        switch (this) {
            case SECURITY:
                return 90;
            case SYSTEM:
                return 80;
            case WORKFLOW:
                return 70;
            case REMINDER:
                return 60;
            case ANNOUNCEMENT:
                return 50;
            case ATTENDANCE:
            case PERSONNEL:
            case CONTRACT:
            case COMPENSATION:
                return 40;
            case RECRUITMENT:
            case FACULTY:
            case APPRAISAL:
            case TITLE:
                return 30;
            case ORGANIZATION:
            case EMPLOYEE:
                return 20;
            case MARKETING:
                return 10;
            default:
                return 50;
        }
    }
}
