package com.hzwangda.edu.notification.service.channel.impl;

import com.hzwangda.edu.notification.entity.NotificationMessage;
import com.hzwangda.edu.notification.enums.ChannelType;
import com.hzwangda.edu.notification.service.channel.NotificationChannelService;
import com.hzwangda.edu.notification.service.channel.handler.ChannelHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 通知渠道服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class NotificationChannelServiceImpl implements NotificationChannelService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationChannelServiceImpl.class);

    @Autowired
    private Map<String, ChannelHandler> channelHandlers;

    @Override
    public boolean send(NotificationMessage message) {
        logger.debug("同步发送消息: 消息ID={}, 渠道={}", message.getMessageId(), message.getChannelType());

        try {
            ChannelHandler handler = getChannelHandler(message.getChannelType());
            if (handler == null) {
                logger.error("未找到渠道处理器: 渠道={}", message.getChannelType());
                return false;
            }

            return handler.send(message);

        } catch (Exception e) {
            logger.error("同步发送消息失败: 消息ID={}, 错误={}", message.getMessageId(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Async("notificationExecutor")
    public void sendAsync(NotificationMessage message) {
        logger.debug("异步发送消息: 消息ID={}, 渠道={}", message.getMessageId(), message.getChannelType());

        try {
            ChannelHandler handler = getChannelHandler(message.getChannelType());
            if (handler == null) {
                logger.error("未找到渠道处理器: 渠道={}", message.getChannelType());
                return;
            }

            handler.sendAsync(message);

        } catch (Exception e) {
            logger.error("异步发送消息失败: 消息ID={}, 错误={}", message.getMessageId(), e.getMessage(), e);
        }
    }

    @Override
    public boolean isChannelAvailable(String channelType) {
        try {
            ChannelHandler handler = getChannelHandler(channelType);
            return handler != null && handler.isAvailable();
        } catch (Exception e) {
            logger.error("检查渠道可用性失败: 渠道={}, 错误={}", channelType, e.getMessage());
            return false;
        }
    }

    @Override
    public String getChannelStatus(String channelType) {
        try {
            ChannelHandler handler = getChannelHandler(channelType);
            if (handler == null) {
                return "UNAVAILABLE";
            }
            return handler.getStatus();
        } catch (Exception e) {
            logger.error("获取渠道状态失败: 渠道={}, 错误={}", channelType, e.getMessage());
            return "ERROR";
        }
    }

    /**
     * 获取渠道处理器
     */
    private ChannelHandler getChannelHandler(String channelType) {
        ChannelType type = ChannelType.fromCode(channelType);
        String handlerName = channelType.toLowerCase() + "ChannelHandler";
        return channelHandlers.get(handlerName);
    }
}
