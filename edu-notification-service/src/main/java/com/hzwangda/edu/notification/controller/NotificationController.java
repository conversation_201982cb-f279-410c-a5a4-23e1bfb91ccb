package com.hzwangda.edu.notification.controller;

import com.hzwangda.edu.common.dto.PageResult;
import com.hzwangda.edu.common.result.Result;
import com.hzwangda.edu.notification.dto.NotificationMessageResponse;
import com.hzwangda.edu.notification.dto.NotificationSendRequest;
import com.hzwangda.edu.notification.dto.ScheduleSmsRequest;
import com.hzwangda.edu.notification.dto.ScheduleTemplateSmsRequest;
import com.hzwangda.edu.notification.entity.MessageStatusHistory;
import com.hzwangda.edu.notification.entity.NotificationMessage;
import com.hzwangda.edu.notification.entity.SmsRecord;
import com.hzwangda.edu.notification.entity.SmsTemplate;
import com.hzwangda.edu.notification.enums.MessageStatus;
import com.hzwangda.edu.notification.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 消息通知控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/v1/notifications")
@Tag(name = "消息通知管理", description = "消息通知相关功能，包括消息发送、查询、管理等")
public class NotificationController {

    private static final Logger logger = LoggerFactory.getLogger(NotificationController.class);

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private MessageRetryService messageRetryService;

    @Autowired
    private MessageStatusTrackingService statusTrackingService;

    @Autowired
    private SmsService smsService;

    @Autowired
    private WeChatService weChatService;

    @PostMapping("/send")
    @Operation(summary = "发送消息", description = "发送单个或批量消息")
    @PreAuthorize("hasAuthority('notification:send')")
    public Result<NotificationMessageResponse> sendMessage(@Valid @RequestBody NotificationSendRequest request) {
        logger.info("发送消息: 渠道={}, 接收人数={}", request.getChannelType(), request.getRecipients().size());
        NotificationMessageResponse response = notificationService.sendMessage(request);
        return Result.success(response);
    }

    @PostMapping("/send/batch")
    @Operation(summary = "批量发送消息", description = "批量发送多个消息")
    @PreAuthorize("hasAuthority('notification:send')")
    public Result<List<NotificationMessageResponse>> batchSendMessages(@Valid @RequestBody List<NotificationSendRequest> requests) {
        logger.info("批量发送消息: 数量={}", requests.size());
        List<NotificationMessageResponse> responses = notificationService.batchSendMessages(requests);
        return Result.success(responses);
    }

    @PostMapping("/send/async")
    @Operation(summary = "异步发送消息", description = "异步发送消息，立即返回消息ID")
    @PreAuthorize("hasAuthority('notification:send')")
    public Result<String> sendMessageAsync(@Valid @RequestBody NotificationSendRequest request) {
        logger.info("异步发送消息: 渠道={}, 接收人数={}", request.getChannelType(), request.getRecipients().size());
        String messageId = notificationService.sendMessageAsync(request);
        return Result.success(messageId);
    }

    @PostMapping("/send/schedule")
    @Operation(summary = "定时发送消息", description = "设置消息的定时发送")
    @PreAuthorize("hasAuthority('notification:send')")
    public Result<NotificationMessageResponse> scheduleMessage(
            @Valid @RequestBody NotificationSendRequest request,
            @Parameter(description = "计划发送时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime scheduledTime) {
        logger.info("定时发送消息: 渠道={}, 计划时间={}", request.getChannelType(), scheduledTime);
        NotificationMessageResponse response = notificationService.scheduleMessage(request, scheduledTime);
        return Result.success(response);
    }

    @PostMapping("/send/template")
    @Operation(summary = "根据模板发送消息", description = "使用预定义模板发送消息")
    @PreAuthorize("hasAuthority('notification:send')")
    public Result<List<NotificationMessageResponse>> sendByTemplate(
            @Parameter(description = "模板编码") @RequestParam String templateCode,
            @Parameter(description = "通知渠道") @RequestParam String channelType,
            @Parameter(description = "接收人列表") @RequestParam List<String> recipients,
            @RequestBody(required = false) Map<String, Object> variables) {
        logger.info("根据模板发送消息: 模板={}, 渠道={}, 接收人数={}", templateCode, channelType, recipients.size());
        List<NotificationMessageResponse> responses = notificationService.sendByTemplate(templateCode, channelType, recipients, variables);
        return Result.success(responses);
    }

    @PostMapping("/messages/{messageId}/cancel")
    @Operation(summary = "取消消息发送", description = "取消待发送的消息")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<Boolean> cancelMessage(@Parameter(description = "消息ID") @PathVariable String messageId) {
        logger.info("取消消息发送: 消息ID={}", messageId);
        Boolean success = notificationService.cancelMessage(messageId);
        return Result.success(success);
    }

    @PostMapping("/messages/{messageId}/retry")
    @Operation(summary = "重试发送消息", description = "重试发送失败的消息")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<NotificationMessageResponse> retryMessage(@Parameter(description = "消息ID") @PathVariable String messageId) {
        logger.info("重试发送消息: 消息ID={}", messageId);
        NotificationMessageResponse response = notificationService.retryMessage(messageId);
        return Result.success(response);
    }

    @GetMapping("/messages/{messageId}")
    @Operation(summary = "查询消息详情", description = "根据ID查询消息详情")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<NotificationMessageResponse> getMessage(@Parameter(description = "消息ID") @PathVariable String messageId) {
        logger.debug("查询消息: 消息ID={}", messageId);
        NotificationMessageResponse response = notificationService.getMessage(messageId);
        return Result.success(response);
    }

    @GetMapping("/messages")
    @Operation(summary = "分页查询消息", description = "分页查询消息列表")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<PageResult<NotificationMessageResponse>> getMessages(
            @Parameter(description = "接收人") @RequestParam(required = false) String recipient,
            @Parameter(description = "通知渠道") @RequestParam(required = false) String channelType,
            @Parameter(description = "消息状态") @RequestParam(required = false) String status,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        logger.debug("分页查询消息: 接收人={}, 渠道={}, 状态={}, 页码={}, 大小={}", recipient, channelType, status, page, size);
        PageResult<NotificationMessageResponse> result = notificationService.getMessages(recipient, channelType, status, page, size);
        return Result.success(result);
    }

    @GetMapping("/messages/unread")
    @Operation(summary = "查询未读消息", description = "查询用户的未读消息列表")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<List<NotificationMessageResponse>> getUnreadMessages(
            @Parameter(description = "接收人ID") @RequestParam Long recipientId) {
        logger.debug("查询未读消息: 用户ID={}", recipientId);
        List<NotificationMessageResponse> messages = notificationService.getUnreadMessages(recipientId);
        return Result.success(messages);
    }

    @GetMapping("/messages/unread/count")
    @Operation(summary = "查询未读消息数量", description = "查询用户的未读消息数量")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<Long> getUnreadMessageCount(
            @Parameter(description = "接收人ID") @RequestParam Long recipientId) {
        logger.debug("查询未读消息数量: 用户ID={}", recipientId);
        Long count = notificationService.getUnreadMessageCount(recipientId);
        return Result.success(count);
    }

    @PostMapping("/messages/{messageId}/read")
    @Operation(summary = "标记消息为已读", description = "标记指定消息为已读")
    @PreAuthorize("hasAuthority('notification:read')")
    public Result<Boolean> markAsRead(
            @Parameter(description = "消息ID") @PathVariable String messageId,
            @Parameter(description = "接收人ID") @RequestParam Long recipientId) {
        logger.info("标记消息为已读: 消息ID={}, 用户ID={}", messageId, recipientId);
        Boolean success = notificationService.markAsRead(messageId, recipientId);
        return Result.success(success);
    }

    @PostMapping("/messages/read/batch")
    @Operation(summary = "批量标记消息为已读", description = "批量标记多个消息为已读")
    @PreAuthorize("hasAuthority('notification:read')")
    public Result<Integer> batchMarkAsRead(
            @Parameter(description = "消息ID列表") @RequestBody List<String> messageIds,
            @Parameter(description = "接收人ID") @RequestParam Long recipientId) {
        logger.info("批量标记消息为已读: 消息数量={}, 用户ID={}", messageIds.size(), recipientId);
        Integer successCount = notificationService.batchMarkAsRead(messageIds, recipientId);
        return Result.success(successCount);
    }

    @GetMapping("/messages/business")
    @Operation(summary = "查询业务相关消息", description = "查询指定业务的相关消息")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<List<NotificationMessageResponse>> getBusinessMessages(
            @Parameter(description = "业务类型") @RequestParam String businessType,
            @Parameter(description = "业务ID") @RequestParam String businessId) {
        logger.debug("查询业务相关消息: 业务类型={}, 业务ID={}", businessType, businessId);
        List<NotificationMessageResponse> messages = notificationService.getBusinessMessages(businessType, businessId);
        return Result.success(messages);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取消息统计信息", description = "获取指定时间范围内的消息统计信息")
    @PreAuthorize("hasAuthority('notification:statistics')")
    public Result<Map<String, Object>> getMessageStatistics(
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        logger.debug("获取消息统计信息: 开始时间={}, 结束时间={}", startTime, endTime);
        Map<String, Object> statistics = notificationService.getMessageStatistics(startTime, endTime);
        return Result.success(statistics);
    }

    @PostMapping("/system/send")
    @Operation(summary = "发送系统通知", description = "发送系统级别的通知消息")
    @PreAuthorize("hasAuthority('notification:system')")
    public Result<Void> sendSystemNotification(
            @Parameter(description = "标题") @RequestParam String title,
            @Parameter(description = "内容") @RequestParam String content,
            @Parameter(description = "接收人列表") @RequestParam List<String> recipients,
            @Parameter(description = "通知渠道列表") @RequestParam List<String> channelTypes) {
        logger.info("发送系统通知: 标题={}, 接收人数={}, 渠道数={}", title, recipients.size(), channelTypes.size());
        notificationService.sendSystemNotification(title, content, recipients, channelTypes);
        return Result.success();
    }

    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "消息通知服务健康检查")
    public Result<String> health() {
        return Result.success("消息通知服务运行正常");
    }

    // ==================== 多渠道管理接口 ====================

    @PostMapping("/channels/{channelType}/send")
    @Operation(summary = "指定渠道发送消息", description = "通过指定渠道发送消息")
    @PreAuthorize("hasAuthority('notification:send')")
    public Result<NotificationMessageResponse> sendByChannel(
            @Parameter(description = "渠道类型") @PathVariable String channelType,
            @Valid @RequestBody NotificationSendRequest request) {
        logger.info("指定渠道发送消息: 渠道={}, 接收人数={}", channelType, request.getRecipients().size());
        request.setChannelType(channelType);
        NotificationMessageResponse response = notificationService.sendMessage(request);
        return Result.success(response);
    }

    @GetMapping("/channels/status")
    @Operation(summary = "获取渠道状态", description = "获取所有通知渠道的状态信息")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<Map<String, Object>> getChannelStatus() {
        logger.debug("获取渠道状态");
        // TODO: 没有方法
        Map<String, Object> channelStatus = null;//notificationService.getChannelStatus();
        return Result.success(channelStatus);
    }

    @PostMapping("/channels/{channelType}/enable")
    @Operation(summary = "启用渠道", description = "启用指定的通知渠道")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<Boolean> enableChannel(@Parameter(description = "渠道类型") @PathVariable String channelType) {
        logger.info("启用渠道: {}", channelType);
        // TODO: 没有方法
        Boolean success = true;//notificationService.enableChannel(channelType);
        return Result.success(success);
    }

    @PostMapping("/channels/{channelType}/disable")
    @Operation(summary = "禁用渠道", description = "禁用指定的通知渠道")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<Boolean> disableChannel(@Parameter(description = "渠道类型") @PathVariable String channelType) {
        logger.info("禁用渠道: {}", channelType);
        // TODO: 没有方法
        Boolean success = true;//notificationService.disableChannel(channelType);
        return Result.success(success);
    }

    @GetMapping("/channels/{channelType}/config")
    @Operation(summary = "获取渠道配置", description = "获取指定渠道的配置信息")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<Map<String, Object>> getChannelConfig(@Parameter(description = "渠道类型") @PathVariable String channelType) {
        logger.debug("获取渠道配置: {}", channelType);
        // TODO: 没有方法
        Map<String, Object> config = null;//notificationService.getChannelConfig(channelType);
        return Result.success(config);
    }

    @PostMapping("/channels/{channelType}/config")
    @Operation(summary = "更新渠道配置", description = "更新指定渠道的配置信息")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<Boolean> updateChannelConfig(
            @Parameter(description = "渠道类型") @PathVariable String channelType,
            @RequestBody Map<String, Object> config) {
        logger.info("更新渠道配置: channelType={}", channelType);
        // TODO: 没有方法
        Boolean success = true;//notificationService.updateChannelConfig(channelType, config);
        return Result.success(success);
    }

    // ==================== 消息重试管理接口 ====================

    @PostMapping("/retry/queue/process")
    @Operation(summary = "处理重试队列", description = "手动触发重试队列处理")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<Map<String, Object>> processRetryQueue() {
        logger.info("手动处理重试队列");
        Map<String, Object> result = messageRetryService.processRetryQueue();
        return Result.success(result);
    }

    @PostMapping("/retry/messages/{messageId}")
    @Operation(summary = "重试指定消息", description = "重试发送指定的失败消息")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<Boolean> retrySpecificMessage(@Parameter(description = "消息ID") @PathVariable String messageId) {
        logger.info("重试指定消息: messageId={}", messageId);
        Boolean success = messageRetryService.retryMessage(messageId);
        return Result.success(success);
    }

    @PostMapping("/retry/messages/batch")
    @Operation(summary = "批量重试消息", description = "批量重试多个失败的消息")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<Map<String, Object>> batchRetryMessages(@RequestBody List<String> messageIds) {
        logger.info("批量重试消息: messageIds={}", messageIds.size());
        Map<String, Object> result = messageRetryService.batchRetryMessages(messageIds);
        return Result.success(result);
    }

    @GetMapping("/retry/queue/status")
    @Operation(summary = "获取重试队列状态", description = "获取重试队列的状态信息")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<Map<String, Object>> getRetryQueueStatus() {
        logger.debug("获取重试队列状态");
        Map<String, Object> status = messageRetryService.getRetryQueueStatus();
        return Result.success(status);
    }

    @GetMapping("/retry/statistics")
    @Operation(summary = "获取重试统计", description = "获取消息重试的统计信息")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<Map<String, Object>> getRetryStatistics(
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        logger.debug("获取重试统计: startTime={}, endTime={}", startTime, endTime);
        Map<String, Object> statistics = messageRetryService.getRetryStatistics(startTime, endTime);
        return Result.success(statistics);
    }

    @PostMapping("/retry/processing/pause")
    @Operation(summary = "暂停重试处理", description = "暂停自动重试处理")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<Void> pauseRetryProcessing() {
        logger.info("暂停重试处理");
        messageRetryService.pauseRetryProcessing();
        return Result.success();
    }

    @PostMapping("/retry/processing/resume")
    @Operation(summary = "恢复重试处理", description = "恢复自动重试处理")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<Void> resumeRetryProcessing() {
        logger.info("恢复重试处理");
        messageRetryService.resumeRetryProcessing();
        return Result.success();
    }

    // ==================== 消息状态跟踪接口 ====================

    @GetMapping("/messages/{messageId}/status")
    @Operation(summary = "获取消息状态", description = "获取指定消息的当前状态")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<MessageStatus> getMessageStatus(@Parameter(description = "消息ID") @PathVariable String messageId) {
        logger.debug("获取消息状态: messageId={}", messageId);
        MessageStatus status = statusTrackingService.getCurrentStatus(messageId);
        return Result.success(status);
    }

    @GetMapping("/messages/{messageId}/status/history")
    @Operation(summary = "获取消息状态历史", description = "获取指定消息的状态变更历史")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<List<MessageStatusHistory>> getMessageStatusHistory(@Parameter(description = "消息ID") @PathVariable String messageId) {
        logger.debug("获取消息状态历史: messageId={}", messageId);
        List<MessageStatusHistory> history = statusTrackingService.getMessageStatusHistory(messageId);
        return Result.success(history);
    }

    @PostMapping("/messages/status/batch")
    @Operation(summary = "批量获取消息状态", description = "批量获取多个消息的状态")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<Map<String, MessageStatus>> getBatchMessageStatus(@RequestBody List<String> messageIds) {
        logger.debug("批量获取消息状态: messageIds={}", messageIds.size());
        Map<String, MessageStatus> statusMap = statusTrackingService.getBatchMessageStatus(messageIds);
        return Result.success(statusMap);
    }

    @GetMapping("/messages/status/{status}")
    @Operation(summary = "按状态查询消息", description = "查询指定状态的消息列表")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<List<NotificationMessageResponse>> getMessagesByStatus(
            @Parameter(description = "消息状态") @PathVariable String status,
            @Parameter(description = "渠道类型") @RequestParam(required = false) String channelType,
            @Parameter(description = "开始时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") int pageSize) {
        logger.debug("按状态查询消息: status={}, channelType={}", status, channelType);
        MessageStatus messageStatus = MessageStatus.valueOf(status);
        List<NotificationMessage> messages = statusTrackingService.getMessagesByStatus(messageStatus, channelType, startTime, endTime, pageNum, pageSize);
        // TODO: 转换为Response对象
        return Result.success(null);
    }

    @GetMapping("/status/distribution")
    @Operation(summary = "获取状态分布统计", description = "获取消息状态的分布统计")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<Map<String, Long>> getMessageStatusDistribution(
            @Parameter(description = "渠道类型") @RequestParam(required = false) String channelType,
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        logger.debug("获取状态分布统计: channelType={}, startTime={}, endTime={}", channelType, startTime, endTime);
        Map<String, Long> distribution = statusTrackingService.getMessageStatusDistribution(channelType, startTime, endTime);
        return Result.success(distribution);
    }

    @GetMapping("/status/success-rate")
    @Operation(summary = "获取消息成功率", description = "获取指定时间段的消息发送成功率")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<Double> getMessageSuccessRate(
            @Parameter(description = "渠道类型") @RequestParam(required = false) String channelType,
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        logger.debug("获取消息成功率: channelType={}, startTime={}, endTime={}", channelType, startTime, endTime);
        double successRate = statusTrackingService.getMessageSuccessRate(channelType, startTime, endTime);
        return Result.success(successRate);
    }

    @GetMapping("/status/monitoring")
    @Operation(summary = "获取实时状态监控", description = "获取消息状态的实时监控数据")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<Map<String, Object>> getRealTimeStatusMonitoring() {
        logger.debug("获取实时状态监控");
        Map<String, Object> monitoring = statusTrackingService.getRealTimeStatusMonitoring();
        return Result.success(monitoring);
    }

    @PostMapping("/messages/{messageId}/status")
    @Operation(summary = "更新消息状态", description = "手动更新消息状态")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<Boolean> updateMessageStatus(
            @Parameter(description = "消息ID") @PathVariable String messageId,
            @Parameter(description = "新状态") @RequestParam String newStatus,
            @Parameter(description = "变更原因") @RequestParam String reason) {
        logger.info("更新消息状态: messageId={}, newStatus={}, reason={}", messageId, newStatus, reason);
        // TODO: 实现消息状态更新逻辑
        return Result.success(true);
    }

    // ==================== 短信模板管理接口 ====================

    @PostMapping("/sms/templates")
    @Operation(summary = "创建短信模板", description = "创建新的短信模板")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<SmsTemplate> createSmsTemplate(@Valid @RequestBody SmsTemplate template) {
        logger.info("创建短信模板: templateCode={}", template.getTemplateCode());
        SmsTemplate created = smsService.createSmsTemplate(template);
        return Result.success(created);
    }

    @PutMapping("/sms/templates")
    @Operation(summary = "更新短信模板", description = "更新现有的短信模板")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<SmsTemplate> updateSmsTemplate(@Valid @RequestBody SmsTemplate template) {
        logger.info("更新短信模板: templateCode={}", template.getTemplateCode());
        SmsTemplate updated = smsService.updateSmsTemplate(template);
        return Result.success(updated);
    }

    @DeleteMapping("/sms/templates/{templateCode}")
    @Operation(summary = "删除短信模板", description = "删除指定的短信模板")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<Boolean> deleteSmsTemplate(@Parameter(description = "模板编码") @PathVariable String templateCode) {
        logger.info("删除短信模板: templateCode={}", templateCode);
        Boolean success = smsService.deleteSmsTemplate(templateCode);
        return Result.success(success);
    }

    @GetMapping("/sms/templates/{templateCode}")
    @Operation(summary = "获取短信模板", description = "获取指定的短信模板")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<SmsTemplate> getSmsTemplate(@Parameter(description = "模板编码") @PathVariable String templateCode) {
        logger.debug("获取短信模板: templateCode={}", templateCode);
        SmsTemplate template = smsService.getSmsTemplate(templateCode);
        return Result.success(template);
    }

    @GetMapping("/sms/templates")
    @Operation(summary = "获取短信模板列表", description = "获取所有短信模板列表")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<List<SmsTemplate>> getSmsTemplates() {
        logger.debug("获取短信模板列表");
        List<SmsTemplate> templates = smsService.getSmsTemplates();
        return Result.success(templates);
    }

    // ==================== SMS增强功能接口 ====================

    @GetMapping("/sms/records/{recordId}/status")
    @Operation(summary = "查询短信发送状态", description = "查询指定短信记录的发送状态")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<String> querySmsStatus(@PathVariable String recordId) {
        logger.info("查询短信状态: recordId={}", recordId);
        String status = smsService.querySmsStatus(recordId);
        return Result.success(status);
    }

    @PostMapping("/sms/schedule")
    @Operation(summary = "定时发送短信", description = "安排在指定时间发送短信")
    @PreAuthorize("hasAuthority('notification:send')")
    public Result<SmsRecord> scheduleSms(@RequestBody @Valid ScheduleSmsRequest request) {
        logger.info("定时发送短信: phoneNumber={}, sendTime={}", request.getPhoneNumber(), request.getSendTime());
        SmsRecord record = smsService.scheduleSms(request.getPhoneNumber(), request.getContent(), request.getSendTime());
        return Result.success(record);
    }

    @PostMapping("/sms/schedule/template")
    @Operation(summary = "定时发送模板短信", description = "安排在指定时间发送模板短信")
    @PreAuthorize("hasAuthority('notification:send')")
    public Result<SmsRecord> scheduleSmsWithTemplate(@RequestBody @Valid ScheduleTemplateSmsRequest request) {
        logger.info("定时发送模板短信: phoneNumber={}, templateCode={}, sendTime={}",
                   request.getPhoneNumber(), request.getTemplateCode(), request.getSendTime());
        SmsRecord record = smsService.scheduleSmsWithTemplate(
                request.getPhoneNumber(),
                request.getTemplateCode(),
                request.getTemplateParams(),
                request.getSendTime()
        );
        return Result.success(record);
    }

    @DeleteMapping("/sms/schedule/{recordId}")
    @Operation(summary = "取消定时短信", description = "取消已安排的定时短信")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<Boolean> cancelScheduledSms(@PathVariable String recordId) {
        logger.info("取消定时短信: recordId={}", recordId);
        boolean success = smsService.cancelScheduledSms(recordId);
        return Result.success(success);
    }

    @GetMapping("/sms/balance")
    @Operation(summary = "获取短信余额", description = "获取当前短信服务商的余额信息")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<Map<String, Object>> getSmsBalance() {
        logger.info("获取短信余额");
        Map<String, Object> balance = smsService.getSmsBalance();
        return Result.success(balance);
    }

    @PostMapping("/sms/records/{recordId}/sync")
    @Operation(summary = "同步短信状态", description = "从服务商同步短信发送状态")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<Boolean> syncSmsStatus(@PathVariable String recordId) {
        logger.info("同步短信状态: recordId={}", recordId);
        boolean success = smsService.syncSmsStatus(recordId);
        return Result.success(success);
    }

    @GetMapping("/sms/statistics")
    @Operation(summary = "获取短信统计", description = "获取短信发送统计信息")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<Map<String, Object>> getSmsStatistics(
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") String startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") String endTime) {
        logger.debug("获取短信统计: startTime={}, endTime={}", startTime, endTime);
        Map<String, Object> statistics = smsService.getSmsStatistics(startTime, endTime);
        return Result.success(statistics);
    }

    // ==================== 死信队列管理接口 ====================

    @GetMapping("/deadletter/messages")
    @Operation(summary = "获取死信消息", description = "获取死信队列中的消息列表")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<List<NotificationMessage>> getDeadLetterMessages(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "20") int pageSize) {
        logger.debug("获取死信消息: pageNum={}, pageSize={}", pageNum, pageSize);
        List<NotificationMessage> messages = messageRetryService.getDeadLetterMessages(pageNum, pageSize);
        return Result.success(messages);
    }

    @PostMapping("/deadletter/messages/{messageId}/recover")
    @Operation(summary = "恢复死信消息", description = "从死信队列恢复消息并重新发送")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<Boolean> recoverDeadLetterMessage(@Parameter(description = "消息ID") @PathVariable String messageId) {
        logger.info("恢复死信消息: messageId={}", messageId);
        Boolean success = messageRetryService.recoverFromDeadLetter(messageId);
        return Result.success(success);
    }

    @DeleteMapping("/deadletter/cleanup")
    @Operation(summary = "清理死信队列", description = "清理指定时间之前的死信消息")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<Long> cleanupDeadLetterQueue(
            @Parameter(description = "清理时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime beforeTime) {
        logger.info("清理死信队列: beforeTime={}", beforeTime);
        Long cleanedCount = messageRetryService.cleanupDeadLetterQueue(beforeTime);
        return Result.success(cleanedCount);
    }

    // ==================== 重试策略配置接口 ====================

    @GetMapping("/retry/strategies")
    @Operation(summary = "获取所有重试策略", description = "获取所有渠道的重试策略配置")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<Map<String, Map<String, Object>>> getAllRetryStrategies() {
        logger.debug("获取所有重试策略");
        Map<String, Map<String, Object>> strategies = messageRetryService.getAllRetryStrategies();
        return Result.success(strategies);
    }

    @GetMapping("/retry/strategies/{channelType}")
    @Operation(summary = "获取渠道重试策略", description = "获取指定渠道的重试策略配置")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<Map<String, Object>> getRetryStrategy(@Parameter(description = "渠道类型") @PathVariable String channelType) {
        logger.debug("获取渠道重试策略: channelType={}", channelType);
        Map<String, Object> strategy = messageRetryService.getRetryStrategy(channelType);
        return Result.success(strategy);
    }

    @PostMapping("/retry/strategies/{channelType}")
    @Operation(summary = "更新重试策略", description = "更新指定渠道的重试策略配置")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<Boolean> updateRetryStrategy(
            @Parameter(description = "渠道类型") @PathVariable String channelType,
            @RequestBody Map<String, Object> strategyConfig) {
        logger.info("更新重试策略: channelType={}", channelType);
        Boolean success = messageRetryService.updateRetryStrategy(channelType, strategyConfig);
        return Result.success(success);
    }

    @PostMapping("/retry/strategies/{channelType}/reset")
    @Operation(summary = "重置重试策略", description = "重置指定渠道的重试策略为默认值")
    @PreAuthorize("hasAuthority('notification:manage')")
    public Result<Boolean> resetRetryStrategy(@Parameter(description = "渠道类型") @PathVariable String channelType) {
        logger.info("重置重试策略: channelType={}", channelType);
        Boolean success = messageRetryService.resetRetryStrategy(channelType);
        return Result.success(success);
    }

    // ==================== 监控和报表接口 ====================

    @GetMapping("/monitoring/retry")
    @Operation(summary = "获取重试监控数据", description = "获取重试相关的监控数据")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<Map<String, Object>> getRetryMonitoringData() {
        logger.debug("获取重试监控数据");
        Map<String, Object> monitoring = messageRetryService.getRetryMonitoringData();
        return Result.success(monitoring);
    }

    @GetMapping("/reports/status")
    @Operation(summary = "导出状态报告", description = "导出消息状态报告")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<Object> exportStatusReport(
            @Parameter(description = "渠道类型") @RequestParam(required = false) String channelType,
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @Parameter(description = "导出格式") @RequestParam(defaultValue = "JSON") String format) {
        logger.info("导出状态报告: channelType={}, startTime={}, endTime={}, format={}", channelType, startTime, endTime, format);
        Object report = statusTrackingService.exportStatusReport(channelType, startTime, endTime, format);
        return Result.success(report);
    }

    @GetMapping("/reports/retry")
    @Operation(summary = "导出重试日志", description = "导出重试相关的日志数据")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<List<Map<String, Object>>> exportRetryLogs(
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @Parameter(description = "渠道类型") @RequestParam(required = false) String channelType) {
        logger.info("导出重试日志: startTime={}, endTime={}, channelType={}", startTime, endTime, channelType);
        List<Map<String, Object>> logs = messageRetryService.exportRetryLogs(startTime, endTime, channelType);
        return Result.success(logs);
    }

    @GetMapping("/statistics/trend")
    @Operation(summary = "获取发送趋势", description = "获取消息发送趋势数据")
    @PreAuthorize("hasAuthority('notification:view')")
    public Result<List<Map<String, Object>>> getMessageSendingTrend(
            @Parameter(description = "渠道类型") @RequestParam(required = false) String channelType,
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @Parameter(description = "时间间隔") @RequestParam(defaultValue = "HOUR") String interval) {
        logger.debug("获取发送趋势: channelType={}, startTime={}, endTime={}, interval={}", channelType, startTime, endTime, interval);
        List<Map<String, Object>> trend = statusTrackingService.getMessageSendingTrend(channelType, startTime, endTime, interval);
        return Result.success(trend);
    }
}
