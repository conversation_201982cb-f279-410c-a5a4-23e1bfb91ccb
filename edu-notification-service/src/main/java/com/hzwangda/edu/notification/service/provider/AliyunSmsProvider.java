package com.hzwangda.edu.notification.service.provider;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.hzwangda.edu.notification.config.SmsConfig;
import com.hzwangda.edu.notification.entity.SmsRecord;
import com.hzwangda.edu.notification.entity.SmsTemplate;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * 阿里云短信服务提供商
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class AliyunSmsProvider {

    @Autowired
    private SmsConfig smsConfig;

    private Client client;

    @PostConstruct
    public void init() {
        try {
            if (smsConfig.isEnabled() && "aliyun".equals(smsConfig.getProvider())) {
                SmsConfig.AliyunSmsConfig aliyunConfig = smsConfig.getAliyun();

                Config config = new Config()
                        .setAccessKeyId(aliyunConfig.getAccessKeyId())
                        .setAccessKeySecret(aliyunConfig.getAccessKeySecret())
                        .setEndpoint(aliyunConfig.getEndpoint());

                this.client = new Client(config);
                log.info("阿里云短信客户端初始化成功");
            }
        } catch (Exception e) {
            log.error("阿里云短信客户端初始化失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送普通短信
     */
    public boolean sendSms(SmsRecord record) {
        if (client == null) {
            log.error("阿里云短信客户端未初始化");
            return false;
        }

        try {
            SmsConfig.AliyunSmsConfig aliyunConfig = smsConfig.getAliyun();

            SendSmsRequest request = new SendSmsRequest()
                    .setPhoneNumbers(record.getPhoneNumber())
                    .setSignName(aliyunConfig.getSignName())
                    // TODO: 模板Code获取问题
                    .setTemplateCode("")
                    .setTemplateParam("{\"content\":\"" + record.getContent() + "\"}");

            SendSmsResponse response = client.sendSms(request);

            if (response.getBody() != null) {
                SendSmsResponseBody body = response.getBody();
                record.setProviderMessageId(body.getBizId());

                if ("OK".equals(body.getCode())) {
                    log.info("阿里云短信发送成功: phoneNumber={}, bizId={}",
                            record.getPhoneNumber(), body.getBizId());
                    return true;
                } else {
                    log.error("阿里云短信发送失败: phoneNumber={}, code={}, message={}",
                            record.getPhoneNumber(), body.getCode(), body.getMessage());
                    record.setErrorCode(body.getCode());
                    record.setErrorMessage(body.getMessage());
                    return false;
                }
            }

            return false;

        } catch (Exception e) {
            log.error("阿里云短信发送异常: phoneNumber={}, error={}",
                    record.getPhoneNumber(), e.getMessage(), e);
            record.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 发送模板短信
     */
    public boolean sendSmsWithTemplate(SmsRecord record, SmsTemplate template, Map<String, Object> templateParams) {
        if (client == null) {
            log.error("阿里云短信客户端未初始化");
            return false;
        }

        try {
            // 构建模板参数JSON
            String templateParamJson = buildTemplateParamJson(templateParams);

            SendSmsRequest request = new SendSmsRequest()
                    .setPhoneNumbers(record.getPhoneNumber())
                    .setSignName(template.getSignName())
                    .setTemplateCode(template.getProviderTemplateId())
                    .setTemplateParam(templateParamJson);

            SendSmsResponse response = client.sendSms(request);

            if (response.getBody() != null) {
                SendSmsResponseBody body = response.getBody();
                record.setProviderMessageId(body.getBizId());

                if ("OK".equals(body.getCode())) {
                    log.info("阿里云模板短信发送成功: phoneNumber={}, templateCode={}, bizId={}",
                            record.getPhoneNumber(), template.getTemplateCode(), body.getBizId());
                    return true;
                } else {
                    log.error("阿里云模板短信发送失败: phoneNumber={}, templateCode={}, code={}, message={}",
                            record.getPhoneNumber(), template.getTemplateCode(), body.getCode(), body.getMessage());
                    record.setErrorCode(body.getCode());
                    record.setErrorMessage(body.getMessage());
                    return false;
                }
            }

            return false;

        } catch (Exception e) {
            log.error("阿里云模板短信发送异常: phoneNumber={}, templateCode={}, error={}",
                    record.getPhoneNumber(), template.getTemplateCode(), e.getMessage(), e);
            record.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 批量发送短信
     */
    public boolean sendBatchSms(String[] phoneNumbers, String signName, String templateCode, String templateParam) {
        if (client == null) {
            log.error("阿里云短信客户端未初始化");
            return false;
        }

        try {
            SendBatchSmsRequest request = new SendBatchSmsRequest()
                    .setPhoneNumberJson("[\"" + String.join("\",\"", phoneNumbers) + "\"]")
                    .setSignNameJson("[\"" + signName + "\"]")
                    .setTemplateCode(templateCode)
                    .setTemplateParamJson("[" + templateParam + "]");

            SendBatchSmsResponse response = client.sendBatchSms(request);

            if (response.getBody() != null) {
                SendBatchSmsResponseBody body = response.getBody();

                if ("OK".equals(body.getCode())) {
                    log.info("阿里云批量短信发送成功: phoneNumbers={}, bizId={}",
                            phoneNumbers.length, body.getBizId());
                    return true;
                } else {
                    log.error("阿里云批量短信发送失败: phoneNumbers={}, code={}, message={}",
                            phoneNumbers.length, body.getCode(), body.getMessage());
                    return false;
                }
            }

            return false;

        } catch (Exception e) {
            log.error("阿里云批量短信发送异常: phoneNumbers={}, error={}",
                    phoneNumbers.length, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 查询短信发送状态
     */
    public String querySmsStatus(String phoneNumber, String bizId, String sendDate) {
        if (client == null) {
            log.error("阿里云短信客户端未初始化");
            return "UNKNOWN";
        }

        try {
            QuerySendDetailsRequest request = new QuerySendDetailsRequest()
                    .setPhoneNumber(phoneNumber)
                    .setBizId(bizId)
                    .setSendDate(sendDate)
                    .setPageSize(10L)
                    .setCurrentPage(1L);

            QuerySendDetailsResponse response = client.querySendDetails(request);

            if (response.getBody() != null) {
                QuerySendDetailsResponseBody body = response.getBody();

                if ("OK".equals(body.getCode()) && body.getSmsSendDetailDTOs() != null
                        && !body.getSmsSendDetailDTOs().getSmsSendDetailDTO().isEmpty()) {

                    QuerySendDetailsResponseBody.QuerySendDetailsResponseBodySmsSendDetailDTOsSmsSendDetailDTO detail =
                            body.getSmsSendDetailDTOs().getSmsSendDetailDTO().get(0);

                    // 阿里云状态映射
                    // 1: 等待回执, 2: 发送失败, 3: 发送成功
                    Long sendStatus = detail.getSendStatus();
                    if (sendStatus == null) {
                        // 可根据业务需求决定是否抛异常或直接返回 UNKNOWN
                        return "UNKNOWN";
                    }

                    switch (sendStatus.intValue()) {
                        case 1:
                            return "PENDING";
                        case 2:
                            return "FAILED";
                        case 3:
                            return "SUCCESS";
                        default:
                            // 可选：记录日志提醒未知状态
                            // logger.warn("Unknown send status: {}", sendStatus);
                            return "UNKNOWN";
                    }
                }
            }

            return "UNKNOWN";

        } catch (Exception e) {
            log.error("查询阿里云短信状态异常: phoneNumber={}, bizId={}, error={}",
                    phoneNumber, bizId, e.getMessage(), e);
            return "ERROR";
        }
    }

    /**
     * 查询短信模板状态
     */
    public String queryTemplateStatus(String templateCode) {
        if (client == null) {
            log.error("阿里云短信客户端未初始化");
            return "UNKNOWN";
        }

        try {
            QuerySmsTemplateRequest request = new QuerySmsTemplateRequest()
                    .setTemplateCode(templateCode);

            QuerySmsTemplateResponse response = client.querySmsTemplate(request);

            if (response.getBody() != null) {
                QuerySmsTemplateResponseBody body = response.getBody();

                if ("OK".equals(body.getCode())) {
                    // 阿里云模板状态：0-审核中，1-审核通过，2-审核失败
                    switch (body.getTemplateStatus()) {
                        case 0:
                            return "PENDING";
                        case 1:
                            return "APPROVED";
                        case 2:
                            return "REJECTED";
                        default:
                            return "UNKNOWN";
                    }
                }
            }

            return "UNKNOWN";

        } catch (Exception e) {
            log.error("查询阿里云短信模板状态异常: templateCode={}, error={}",
                    templateCode, e.getMessage(), e);
            return "ERROR";
        }
    }

    /**
     * 检查服务可用性
     */
    public boolean isAvailable() {
        if (client == null) {
            return false;
        }

        try {
            SmsConfig.AliyunSmsConfig aliyunConfig = smsConfig.getAliyun();
            return StringUtils.hasText(aliyunConfig.getAccessKeyId()) &&
                   StringUtils.hasText(aliyunConfig.getAccessKeySecret()) &&
                   StringUtils.hasText(aliyunConfig.getEndpoint());
        } catch (Exception e) {
            log.error("检查阿里云短信服务可用性失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 构建模板参数JSON
     */
    private String buildTemplateParamJson(Map<String, Object> templateParams) {
        if (templateParams == null || templateParams.isEmpty()) {
            return "{}";
        }

        StringBuilder json = new StringBuilder("{");
        boolean first = true;
        for (Map.Entry<String, Object> entry : templateParams.entrySet()) {
            if (!first) {
                json.append(",");
            }
            json.append("\"").append(entry.getKey()).append("\":\"")
                .append(entry.getValue()).append("\"");
            first = false;
        }
        json.append("}");

        return json.toString();
    }
}
