package com.hzwangda.edu.notification.service.impl;

import com.hzwangda.edu.common.exception.BusinessException;
import com.hzwangda.edu.common.result.ResultCode;
import com.hzwangda.edu.notification.config.WeChatConfig;
import com.hzwangda.edu.notification.entity.WeChatRecord;
import com.hzwangda.edu.notification.entity.WeChatUser;
import com.hzwangda.edu.notification.service.WeChatService;
import com.hzwangda.edu.notification.service.provider.WeChatWorkProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 微信服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class WeChatServiceImpl implements WeChatService {

    @Autowired
    private WeChatConfig weChatConfig;

    @Autowired
    private WeChatWorkProvider weChatWorkProvider;

    // 内存存储（实际项目中应该使用数据库）
    private final Map<String, WeChatRecord> weChatRecords = new ConcurrentHashMap<>();
    private final Map<String, WeChatUser> weChatUsers = new ConcurrentHashMap<>();
    private final Map<String, List<LocalDateTime>> sendLimitTracker = new ConcurrentHashMap<>();

    @Override
    public WeChatRecord sendTextMessage(String toUser, String content, String wechatType) {
        log.info("发送微信文本消息: toUser={}, wechatType={}", toUser, wechatType);

        try {
            // 检查发送限制
            if (!checkSendLimit(toUser, wechatType)) {
                throw new BusinessException(ResultCode.TOO_MANY_REQUESTS, "微信消息发送频率超限");
            }

            // 创建发送记录
            WeChatRecord record = WeChatRecord.builder()
                    .toUser(toUser)
                    .content(content)
                    .msgType("text")
                    .wechatType(wechatType)
                    .sendStatus("PENDING")
                    .sendTime(LocalDateTime.now())
                    .retryCount(0)
                    .maxRetryCount(3)
                    .build();

            // 调用具体的微信服务商发送
            boolean success = sendWeChatMessage(record, content, null);

            if (success) {
                record.setSendStatus("SUCCESS");
                log.info("微信文本消息发送成功: recordId={}", record.getId());
            } else {
                record.setSendStatus("FAILED");
                log.error("微信文本消息发送失败: recordId={}", record.getId());
            }

            // 保存记录
            weChatRecords.put(record.getId().toString(), record);

            // 记录发送限制
            recordSendLimit(toUser, wechatType);

            return record;

        } catch (Exception e) {
            log.error("微信文本消息发送异常: toUser={}, error={}", toUser, e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "微信文本消息发送失败: " + e.getMessage());
        }
    }

    @Override
    public WeChatRecord sendTemplateMessage(String toUser, String templateId, Map<String, Object> templateData, String wechatType) {
        log.info("发送微信模板消息: toUser={}, templateId={}, wechatType={}", toUser, templateId, wechatType);

        try {
            // 检查发送限制
            if (!checkSendLimit(toUser, wechatType)) {
                throw new BusinessException(ResultCode.TOO_MANY_REQUESTS, "微信消息发送频率超限");
            }

            // 创建发送记录
            WeChatRecord record = WeChatRecord.builder()
                    .toUser(toUser)
                    .templateId(templateId)
                    .templateData(templateData != null ? templateData.toString() : null)
                    .msgType("template")
                    .wechatType(wechatType)
                    .sendStatus("PENDING")
                    .sendTime(LocalDateTime.now())
                    .retryCount(0)
                    .maxRetryCount(3)
                    .build();

            // 调用具体的微信服务商发送
            boolean success = sendWeChatMessage(record, null, templateData);

            if (success) {
                record.setSendStatus("SUCCESS");
                log.info("微信模板消息发送成功: recordId={}, templateId={}", record.getId(), templateId);
            } else {
                record.setSendStatus("FAILED");
                log.error("微信模板消息发送失败: recordId={}, templateId={}", record.getId(), templateId);
            }

            // 保存记录
            weChatRecords.put(record.getId().toString(), record);

            // 记录发送限制
            recordSendLimit(toUser, wechatType);

            return record;

        } catch (Exception e) {
            log.error("微信模板消息发送异常: toUser={}, templateId={}, error={}",
                     toUser, templateId, e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "微信模板消息发送失败: " + e.getMessage());
        }
    }

    @Override
    public WeChatRecord sendTextCardMessage(String toUser, String title, String description, String url, String btnText) {
        log.info("发送微信卡片消息: toUser={}, title={}", toUser, title);

        try {
            // 检查发送限制
            if (!checkSendLimit(toUser, "work")) {
                throw new BusinessException(ResultCode.TOO_MANY_REQUESTS, "微信消息发送频率超限");
            }

            // 创建发送记录
            WeChatRecord record = WeChatRecord.builder()
                    .toUser(toUser)
                    .content(title + "|" + description)
                    .msgType("textcard")
                    .wechatType("work")
                    .sendStatus("PENDING")
                    .sendTime(LocalDateTime.now())
                    .retryCount(0)
                    .maxRetryCount(3)
                    .build();

            // 调用企业微信发送卡片消息
            boolean success = weChatWorkProvider.sendTextCardMessage(record, title, description, url, btnText);

            if (success) {
                record.setSendStatus("SUCCESS");
                log.info("微信卡片消息发送成功: recordId={}", record.getId());
            } else {
                record.setSendStatus("FAILED");
                log.error("微信卡片消息发送失败: recordId={}", record.getId());
            }

            // 保存记录
            weChatRecords.put(record.getId().toString(), record);

            // 记录发送限制
            recordSendLimit(toUser, "work");

            return record;

        } catch (Exception e) {
            log.error("微信卡片消息发送异常: toUser={}, error={}", toUser, e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "微信卡片消息发送失败: " + e.getMessage());
        }
    }

    @Override
    public WeChatRecord sendNewsMessage(String toUser, List<Map<String, Object>> articles, String wechatType) {
        log.info("发送微信图文消息: toUser={}, articles={}, wechatType={}", toUser, articles.size(), wechatType);

        try {
            // 检查发送限制
            if (!checkSendLimit(toUser, wechatType)) {
                throw new BusinessException(ResultCode.TOO_MANY_REQUESTS, "微信消息发送频率超限");
            }

            // 创建发送记录
            WeChatRecord record = WeChatRecord.builder()
                    .toUser(toUser)
                    .content("图文消息(" + articles.size() + "条)")
                    .msgType("news")
                    .wechatType(wechatType)
                    .sendStatus("PENDING")
                    .sendTime(LocalDateTime.now())
                    .retryCount(0)
                    .maxRetryCount(3)
                    .build();

            // 调用具体的微信服务商发送
            boolean success = false;
            if ("work".equals(wechatType)) {
                success = weChatWorkProvider.sendNewsMessage(record, articles);
            }

            if (success) {
                record.setSendStatus("SUCCESS");
                log.info("微信图文消息发送成功: recordId={}", record.getId());
            } else {
                record.setSendStatus("FAILED");
                log.error("微信图文消息发送失败: recordId={}", record.getId());
            }

            // 保存记录
            weChatRecords.put(record.getId().toString(), record);

            // 记录发送限制
            recordSendLimit(toUser, wechatType);

            return record;

        } catch (Exception e) {
            log.error("微信图文消息发送异常: toUser={}, error={}", toUser, e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "微信图文消息发送失败: " + e.getMessage());
        }
    }

    @Override
    public WeChatRecord sendMarkdownMessage(String toUser, String content) {
        log.info("发送微信Markdown消息: toUser={}", toUser);

        try {
            // 检查发送限制
            if (!checkSendLimit(toUser, "work")) {
                throw new BusinessException(ResultCode.TOO_MANY_REQUESTS, "微信消息发送频率超限");
            }

            // 创建发送记录
            WeChatRecord record = WeChatRecord.builder()
                    .toUser(toUser)
                    .content(content)
                    .msgType("markdown")
                    .wechatType("work")
                    .sendStatus("PENDING")
                    .sendTime(LocalDateTime.now())
                    .retryCount(0)
                    .maxRetryCount(3)
                    .build();

            // 调用企业微信发送Markdown消息
            boolean success = weChatWorkProvider.sendMarkdownMessage(record, content);

            if (success) {
                record.setSendStatus("SUCCESS");
                log.info("微信Markdown消息发送成功: recordId={}", record.getId());
            } else {
                record.setSendStatus("FAILED");
                log.error("微信Markdown消息发送失败: recordId={}", record.getId());
            }

            // 保存记录
            weChatRecords.put(record.getId().toString(), record);

            // 记录发送限制
            recordSendLimit(toUser, "work");

            return record;

        } catch (Exception e) {
            log.error("微信Markdown消息发送异常: toUser={}, error={}", toUser, e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "微信Markdown消息发送失败: " + e.getMessage());
        }
    }

    @Override
    public WeChatRecord sendGroupMessage(String chatId, String content, String msgType, String wechatType) {
        log.info("发送微信群消息: chatId={}, msgType={}, wechatType={}", chatId, msgType, wechatType);

        try {
            // 创建发送记录
            WeChatRecord record = WeChatRecord.builder()
                    .toUser(chatId)
                    .content(content)
                    .msgType(msgType)
                    .wechatType(wechatType)
                    .sendStatus("PENDING")
                    .sendTime(LocalDateTime.now())
                    .retryCount(0)
                    .maxRetryCount(3)
                    .build();

            // TODO: 实现群消息发送逻辑
            record.setSendStatus("SUCCESS");
            log.info("微信群消息发送成功: recordId={}", record.getId());

            // 保存记录
            weChatRecords.put(record.getId().toString(), record);

            return record;

        } catch (Exception e) {
            log.error("微信群消息发送异常: chatId={}, error={}", chatId, e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "微信群消息发送失败: " + e.getMessage());
        }
    }

    @Override
    public List<WeChatRecord> sendBatchMessage(List<String> toUsers, String content, String msgType, String wechatType) {
        log.info("批量发送微信消息: toUsers={}, msgType={}, wechatType={}", toUsers.size(), msgType, wechatType);

        return toUsers.stream()
                .map(toUser -> {
                    try {
                        if ("text".equals(msgType)) {
                            return sendTextMessage(toUser, content, wechatType);
                        } else if ("markdown".equals(msgType) && "work".equals(wechatType)) {
                            return sendMarkdownMessage(toUser, content);
                        } else {
                            return sendTextMessage(toUser, content, wechatType);
                        }
                    } catch (Exception e) {
                        log.error("批量发送微信消息失败: toUser={}, error={}", toUser, e.getMessage());
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 调用具体的微信服务商发送消息
     */
    private boolean sendWeChatMessage(WeChatRecord record, String content, Map<String, Object> templateData) {
        try {
            String wechatType = record.getWechatType();
            String msgType = record.getMsgType();

            if ("work".equals(wechatType)) {
                if ("text".equals(msgType)) {
                    return weChatWorkProvider.sendTextMessage(record, content);
                } else if ("template".equals(msgType)) {
                    return weChatWorkProvider.sendTemplateMessage(record, record.getTemplateId(), templateData);
                }
            }

            // TODO: 实现其他微信类型的发送逻辑
            log.warn("不支持的微信类型或消息类型: wechatType={}, msgType={}", wechatType, msgType);
            return false;

        } catch (Exception e) {
            log.error("调用微信服务商发送消息失败: {}", e.getMessage(), e);
            record.setErrorMessage(e.getMessage());
            return false;
        }
    }

    @Override
    public String getAccessToken(String wechatType) {
        log.info("获取微信Access Token: wechatType={}", wechatType);

        try {
            if ("work".equals(wechatType)) {
                return weChatWorkProvider.getAccessToken();
            }

            // TODO: 实现其他微信类型的Token获取
            log.warn("不支持的微信类型: {}", wechatType);
            return null;

        } catch (Exception e) {
            log.error("获取微信Access Token失败: wechatType={}, error={}", wechatType, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String refreshAccessToken(String wechatType) {
        log.info("刷新微信Access Token: wechatType={}", wechatType);

        try {
            // 清除缓存，强制重新获取
            if ("work".equals(wechatType)) {
                return weChatWorkProvider.getAccessToken();
            }

            return null;

        } catch (Exception e) {
            log.error("刷新微信Access Token失败: wechatType={}, error={}", wechatType, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public WeChatUser getWeChatUserInfo(String openId, String wechatType) {
        log.info("获取微信用户信息: openId={}, wechatType={}", openId, wechatType);

        try {
            if ("work".equals(wechatType)) {
                Map<String, Object> userInfo = weChatWorkProvider.getUserInfo(openId);
                if (userInfo != null) {
                    WeChatUser weChatUser = new WeChatUser();
                    weChatUser.setOpenId(openId);
                    weChatUser.setWechatType(wechatType);
                    weChatUser.setNickname((String) userInfo.get("name"));
                    weChatUser.setDepartmentIds((String) userInfo.get("department"));
                    weChatUser.setPosition((String) userInfo.get("position"));
                    weChatUser.setMobile((String) userInfo.get("mobile"));
                    weChatUser.setEmail((String) userInfo.get("email"));
                    return weChatUser;
                }
            }

            return null;

        } catch (Exception e) {
            log.error("获取微信用户信息失败: openId={}, wechatType={}, error={}",
                     openId, wechatType, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean bindWeChatUser(String userId, WeChatUser wechatUser) {
        log.info("绑定微信用户: userId={}, openId={}, wechatType={}",
                userId, wechatUser.getOpenId(), wechatUser.getWechatType());

        try {
            String key = userId + "_" + wechatUser.getWechatType();
            wechatUser.setUserId(userId);
            wechatUser.setUpdateTime(LocalDateTime.now());
            weChatUsers.put(key, wechatUser);

            log.info("微信用户绑定成功: userId={}, openId={}", userId, wechatUser.getOpenId());
            return true;

        } catch (Exception e) {
            log.error("绑定微信用户失败: userId={}, error={}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean unbindWeChatUser(String userId, String wechatType) {
        log.info("解绑微信用户: userId={}, wechatType={}", userId, wechatType);

        try {
            String key = userId + "_" + wechatType;
            WeChatUser removed = weChatUsers.remove(key);

            if (removed != null) {
                log.info("微信用户解绑成功: userId={}, openId={}", userId, removed.getOpenId());
                return true;
            } else {
                log.warn("未找到要解绑的微信用户: userId={}, wechatType={}", userId, wechatType);
                return false;
            }

        } catch (Exception e) {
            log.error("解绑微信用户失败: userId={}, error={}", userId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public WeChatUser getWeChatUserByUserId(String userId, String wechatType) {
        String key = userId + "_" + wechatType;
        return weChatUsers.get(key);
    }

    @Override
    public WeChatUser getWeChatUserByOpenId(String openId, String wechatType) {
        return weChatUsers.values().stream()
                .filter(user -> openId.equals(user.getOpenId()) && wechatType.equals(user.getWechatType()))
                .findFirst()
                .orElse(null);
    }

    @Override
    public WeChatRecord getWeChatRecord(String recordId) {
        return weChatRecords.get(recordId);
    }

    @Override
    public List<WeChatRecord> getWeChatRecords(String toUser, String wechatType, String startTime, String endTime) {
        return weChatRecords.values().stream()
                .filter(record -> toUser == null || toUser.equals(record.getToUser()))
                .filter(record -> wechatType == null || wechatType.equals(record.getWechatType()))
                .collect(Collectors.toList());
    }

    @Override
    public boolean retryWeChatMessage(String recordId) {
        log.info("重试微信消息: recordId={}", recordId);

        WeChatRecord record = weChatRecords.get(recordId);
        if (record == null) {
            log.warn("微信消息记录不存在: recordId={}", recordId);
            return false;
        }

        if (record.getRetryCount() >= record.getMaxRetryCount()) {
            log.warn("微信消息重试次数已达上限: recordId={}, retryCount={}", recordId, record.getRetryCount());
            return false;
        }

        try {
            record.setRetryCount(record.getRetryCount() + 1);
            record.setSendStatus("PENDING");
            record.setSendTime(LocalDateTime.now());

            boolean success = sendWeChatMessage(record, record.getContent(), null);

            if (success) {
                record.setSendStatus("SUCCESS");
                log.info("微信消息重试发送成功: recordId={}", recordId);
            } else {
                record.setSendStatus("FAILED");
                log.error("微信消息重试发送失败: recordId={}", recordId);
            }

            weChatRecords.put(recordId, record);
            return success;

        } catch (Exception e) {
            log.error("微信消息重试发送异常: recordId={}, error={}", recordId, e.getMessage(), e);
            record.setSendStatus("FAILED");
            record.setErrorMessage(e.getMessage());
            weChatRecords.put(recordId, record);
            return false;
        }
    }

    @Override
    public Map<String, Object> getWeChatStatistics(String wechatType, String startTime, String endTime) {
        Map<String, Object> statistics = new HashMap<>();

        List<WeChatRecord> records = weChatRecords.values().stream()
                .filter(record -> wechatType == null || wechatType.equals(record.getWechatType()))
                .collect(Collectors.toList());

        long totalCount = records.size();
        long successCount = records.stream()
                .filter(r -> "SUCCESS".equals(r.getSendStatus()))
                .count();
        long failedCount = records.stream()
                .filter(r -> "FAILED".equals(r.getSendStatus()))
                .count();

        statistics.put("totalCount", totalCount);
        statistics.put("successCount", successCount);
        statistics.put("failedCount", failedCount);
        statistics.put("successRate", totalCount > 0 ? (double) successCount / totalCount * 100 : 0);

        return statistics;
    }

    @Override
    public boolean checkSendLimit(String toUser, String wechatType) {
        if (!StringUtils.hasText(toUser)) {
            return false;
        }

        String key = toUser + "_" + wechatType;
        List<LocalDateTime> sendTimes = sendLimitTracker.getOrDefault(key, new ArrayList<>());
        LocalDateTime now = LocalDateTime.now();

        // 清理过期记录
        sendTimes.removeIf(time -> time.isBefore(now.minusHours(1)));

        // 检查每分钟限制（最多5条）
        long countPerMinute = sendTimes.stream()
                .filter(time -> time.isAfter(now.minusMinutes(1)))
                .count();
        if (countPerMinute >= 5) {
            return false;
        }

        // 检查每小时限制（最多100条）
        long countPerHour = sendTimes.stream()
                .filter(time -> time.isAfter(now.minusHours(1)))
                .count();
        if (countPerHour >= 100) {
            return false;
        }

        return true;
    }

    @Override
    public boolean verifySignature(String signature, String timestamp, String nonce, String token) {
        try {
            String[] arr = {token, timestamp, nonce};
            Arrays.sort(arr);

            StringBuilder content = new StringBuilder();
            for (String s : arr) {
                content.append(s);
            }

            MessageDigest md = MessageDigest.getInstance("SHA-1");
            byte[] digest = md.digest(content.toString().getBytes());

            StringBuilder hexStr = new StringBuilder();
            for (byte b : digest) {
                String shaHex = Integer.toHexString(b & 0xFF);
                if (shaHex.length() < 2) {
                    hexStr.append(0);
                }
                hexStr.append(shaHex);
            }

            return hexStr.toString().equals(signature);

        } catch (Exception e) {
            log.error("验证微信签名失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String handleWeChatCallback(Map<String, Object> callbackData, String wechatType) {
        log.info("处理微信回调: wechatType={}, data={}", wechatType, callbackData);

        try {
            // TODO: 根据不同的微信类型处理回调
            if ("work".equals(wechatType)) {
                // 处理企业微信回调
                return handleWorkWeChatCallback(callbackData);
            }

            return "success";

        } catch (Exception e) {
            log.error("处理微信回调失败: {}", e.getMessage(), e);
            return "error";
        }
    }

    /**
     * 处理企业微信回调
     */
    private String handleWorkWeChatCallback(Map<String, Object> callbackData) {
        // TODO: 实现企业微信回调处理逻辑
        log.info("处理企业微信回调: {}", callbackData);
        return "success";
    }

    /**
     * 记录发送限制
     */
    private void recordSendLimit(String toUser, String wechatType) {
        String key = toUser + "_" + wechatType;
        List<LocalDateTime> sendTimes = sendLimitTracker.computeIfAbsent(key, k -> new ArrayList<>());
        sendTimes.add(LocalDateTime.now());

        // 清理过期记录（保留最近1小时的记录）
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        sendTimes.removeIf(time -> time.isBefore(oneHourAgo));
    }
}
