package com.hzwangda.edu.notification.service.channel.handler;

import com.hzwangda.edu.notification.entity.NotificationMessage;

/**
 * 通知渠道处理器接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ChannelHandler {

    /**
     * 同步发送消息
     *
     * @param message 消息实体
     * @return 是否发送成功
     */
    boolean send(NotificationMessage message);

    /**
     * 异步发送消息
     *
     * @param message 消息实体
     */
    void sendAsync(NotificationMessage message);

    /**
     * 检查渠道是否可用
     *
     * @return 是否可用
     */
    boolean isAvailable();

    /**
     * 获取渠道状态
     *
     * @return 状态信息
     */
    String getStatus();

    /**
     * 获取支持的渠道类型
     *
     * @return 渠道类型
     */
    String getSupportedChannelType();
}
