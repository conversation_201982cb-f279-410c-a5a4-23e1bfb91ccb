package com.hzwangda.edu.notification.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hzwangda.edu.notification.dto.*;
import com.hzwangda.edu.notification.dto.NotificationMessageResponse;
import com.hzwangda.edu.notification.dto.NotificationSendRequest;
import com.hzwangda.edu.notification.dto.NotificationSendResult;
import com.hzwangda.edu.notification.entity.NotificationMessage;
import com.hzwangda.edu.notification.entity.NotificationTemplate;
import com.hzwangda.edu.notification.enums.ChannelType;
import com.hzwangda.edu.notification.enums.MessageStatus;
import com.hzwangda.edu.notification.repository.NotificationMessageRepository;
import com.hzwangda.edu.notification.repository.NotificationTemplateRepository;
import com.hzwangda.edu.notification.service.NotificationService;
import com.hzwangda.edu.notification.service.MultiChannelNotificationManager;
import com.hzwangda.edu.notification.service.channel.NotificationChannelService;
import com.hzwangda.edu.notification.util.NotificationMapper;
import com.hzwangda.edu.notification.util.TemplateEngine;
import com.hzwangda.edu.common.dto.PageResult;
import com.hzwangda.edu.common.exception.BusinessException;
import com.hzwangda.edu.common.result.ResultCode;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 消息通知服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Transactional
public class NotificationServiceImpl implements NotificationService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationServiceImpl.class);

    @Autowired
    private NotificationMessageRepository messageRepository;

    @Autowired
    private NotificationTemplateRepository templateRepository;

    @Autowired
    private NotificationChannelService channelService;

    @Autowired
    private MultiChannelNotificationManager multiChannelManager;

    @Autowired
    private NotificationMapper notificationMapper;

    @Autowired
    private TemplateEngine templateEngine;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String CACHE_KEY_PREFIX = "notification:";
    private static final String MESSAGE_ID_PREFIX = "MSG_";
    private static final int CACHE_EXPIRE_HOURS = 2;

    @Override
    public NotificationMessageResponse sendMessage(NotificationSendRequest request) {
        logger.info("发送消息: 渠道={}, 接收人数={}", request.getChannelType(), request.getRecipients().size());

        try {
            // 验证请求参数
            validateSendRequest(request);

            // 如果是异步发送
            if (Boolean.TRUE.equals(request.getAsync())) {
                String messageId = sendMessageAsync(request);
                NotificationMessageResponse response = new NotificationMessageResponse();
                response.setMessageId(messageId);
                response.setStatus(MessageStatus.PENDING.getCode());
                return response;
            }

            // 如果是批量发送
            if (Boolean.TRUE.equals(request.getBatch()) && request.getRecipients().size() > 1) {
                List<NotificationSendRequest> requests = splitBatchRequest(request);
                List<NotificationMessageResponse> responses = batchSendMessages(requests);
                return responses.get(0); // 返回第一个结果
            }

            // 单个消息发送
            return sendSingleMessage(request);

        } catch (Exception e) {
            logger.error("发送消息失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "发送消息失败: " + e.getMessage());
        }
    }

    @Override
    public List<NotificationMessageResponse> batchSendMessages(List<NotificationSendRequest> requests) {
        logger.info("批量发送消息: 数量={}", requests.size());

        List<NotificationMessageResponse> responses = new ArrayList<>();

        for (NotificationSendRequest request : requests) {
            try {
                NotificationMessageResponse response = sendSingleMessage(request);
                responses.add(response);
            } catch (Exception e) {
                logger.error("批量发送消息失败: {}", e.getMessage(), e);
                // 创建失败响应
                NotificationMessageResponse failedResponse = new NotificationMessageResponse();
                failedResponse.setStatus(MessageStatus.FAILED.getCode());
                failedResponse.setErrorMessage(e.getMessage());
                responses.add(failedResponse);
            }
        }

        return responses;
    }

    @Override
    @Async("notificationExecutor")
    public String sendMessageAsync(NotificationSendRequest request) {
        logger.info("异步发送消息: 渠道={}, 接收人数={}", request.getChannelType(), request.getRecipients().size());

        try {
            // 生成消息ID
            String messageId = generateMessageId();

            // 创建消息记录
            NotificationMessage message = createMessageFromRequest(request, messageId);
            message = messageRepository.save(message);

            // 异步发送
            channelService.sendAsync(message);

            return messageId;

        } catch (Exception e) {
            logger.error("异步发送消息失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "异步发送消息失败: " + e.getMessage());
        }
    }

    @Override
    public NotificationMessageResponse scheduleMessage(NotificationSendRequest request, LocalDateTime scheduledTime) {
        logger.info("定时发送消息: 渠道={}, 计划时间={}", request.getChannelType(), scheduledTime);

        try {
            // 验证计划时间
            if (scheduledTime.isBefore(LocalDateTime.now())) {
                throw new BusinessException(ResultCode.BUSINESS_ERROR, "计划发送时间不能早于当前时间");
            }

            // 设置计划时间
            request.setScheduledTime(scheduledTime);

            // 创建消息记录
            String messageId = generateMessageId();
            NotificationMessage message = createMessageFromRequest(request, messageId);
            message.setScheduledTime(scheduledTime);
            message = messageRepository.save(message);

            return notificationMapper.toMessageResponse(message);

        } catch (Exception e) {
            logger.error("定时发送消息失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "定时发送消息失败: " + e.getMessage());
        }
    }

    @Override
    public List<NotificationMessageResponse> sendByTemplate(String templateCode, String channelType,
                                                           List<String> recipients, Map<String, Object> variables) {
        logger.info("根据模板发送消息: 模板={}, 渠道={}, 接收人数={}", templateCode, channelType, recipients.size());

        try {
            // 查询模板
            NotificationTemplate template = templateRepository
                    .findByTemplateCodeAndChannelTypeAndDeletedFalse(templateCode, channelType)
                    .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "消息模板不存在"));

            // 构建发送请求
            NotificationSendRequest request = new NotificationSendRequest();
            request.setTemplateCode(templateCode);
            request.setChannelType(channelType);
            request.setRecipients(recipients);
            request.setVariables(variables);

            // 渲染模板
            String title = templateEngine.render(template.getTemplateTitle(), variables);
            String content = templateEngine.render(template.getTemplateContent(), variables);

            request.setMessageTitle(title);
            request.setMessageContent(content);
            request.setPriority(template.getPriority());

            // 批量发送
            List<NotificationSendRequest> requests = splitBatchRequest(request);
            return batchSendMessages(requests);

        } catch (Exception e) {
            logger.error("根据模板发送消息失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "根据模板发送消息失败: " + e.getMessage());
        }
    }

    @Override
    public Boolean cancelMessage(String messageId) {
        logger.info("取消消息发送: 消息ID={}", messageId);

        try {
            NotificationMessage message = messageRepository.findByMessageIdAndDeletedFalse(messageId)
                    .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "消息不存在"));

            MessageStatus status = MessageStatus.fromCode(message.getStatus());
            if (!status.canCancel()) {
                throw new BusinessException(ResultCode.BUSINESS_ERROR, "消息状态不允许取消: " + status.getDescription());
            }

            message.setStatus(MessageStatus.CANCELLED.getCode());
            messageRepository.save(message);

            return true;

        } catch (Exception e) {
            logger.error("取消消息发送失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "取消消息发送失败: " + e.getMessage());
        }
    }

    @Override
    public NotificationMessageResponse retryMessage(String messageId) {
        logger.info("重试发送消息: 消息ID={}", messageId);

        try {
            NotificationMessage message = messageRepository.findByMessageIdAndDeletedFalse(messageId)
                    .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "消息不存在"));

            MessageStatus status = MessageStatus.fromCode(message.getStatus());
            if (!status.canRetry()) {
                throw new BusinessException(ResultCode.BUSINESS_ERROR, "消息状态不允许重试: " + status.getDescription());
            }

            if (message.getRetryCount() >= message.getMaxRetryCount()) {
                throw new BusinessException(ResultCode.BUSINESS_ERROR, "已达到最大重试次数");
            }

            // 重置状态并重试
            message.setStatus(MessageStatus.PENDING.getCode());
            message.setRetryCount(message.getRetryCount() + 1);
            message.setErrorMessage(null);
            message = messageRepository.save(message);

            // 异步发送
            channelService.sendAsync(message);

            return notificationMapper.toMessageResponse(message);

        } catch (Exception e) {
            logger.error("重试发送消息失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "重试发送消息失败: " + e.getMessage());
        }
    }

    /**
     * 验证发送请求
     */
    private void validateSendRequest(NotificationSendRequest request) {
        if (StringUtils.isBlank(request.getChannelType())) {
            throw new BusinessException(ResultCode.BUSINESS_ERROR, "通知渠道不能为空");
        }

        if (request.getRecipients() == null || request.getRecipients().isEmpty()) {
            throw new BusinessException(ResultCode.BUSINESS_ERROR, "接收人列表不能为空");
        }

        ChannelType channelType = ChannelType.fromCode(request.getChannelType());
        if (channelType == null) {
            throw new BusinessException(ResultCode.BUSINESS_ERROR, "不支持的通知渠道: " + request.getChannelType());
        }

        // 验证模板或内容
        if (StringUtils.isBlank(request.getTemplateCode()) && StringUtils.isBlank(request.getMessageContent())) {
            throw new BusinessException(ResultCode.BUSINESS_ERROR, "模板编码或消息内容不能同时为空");
        }
    }

    /**
     * 发送单个消息
     */
    private NotificationMessageResponse sendSingleMessage(NotificationSendRequest request) {
        // 生成消息ID
        String messageId = generateMessageId();

        // 创建消息记录
        NotificationMessage message = createMessageFromRequest(request, messageId);
        message = messageRepository.save(message);

        // 发送消息
        boolean success = channelService.send(message);

        // 更新状态
        if (success) {
            message.setStatus(MessageStatus.SENT.getCode());
            message.setSendTime(LocalDateTime.now());
        } else {
            message.setStatus(MessageStatus.FAILED.getCode());
            message.setErrorMessage("发送失败");
        }

        message = messageRepository.save(message);

        return notificationMapper.toMessageResponse(message);
    }

    /**
     * 生成消息ID
     */
    private String generateMessageId() {
        return MESSAGE_ID_PREFIX + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }

    /**
     * 从请求创建消息实体
     */
    private NotificationMessage createMessageFromRequest(NotificationSendRequest request, String messageId) {
        NotificationMessage message = new NotificationMessage();

        message.setMessageId(messageId);
        message.setTemplateCode(request.getTemplateCode());
        message.setChannelType(request.getChannelType());
        message.setRecipient(request.getRecipients().get(0)); // 单个接收人

        if (request.getRecipientIds() != null && !request.getRecipientIds().isEmpty()) {
            message.setRecipientId(request.getRecipientIds().get(0));
        }

        if (request.getRecipientNames() != null && !request.getRecipientNames().isEmpty()) {
            message.setRecipientName(request.getRecipientNames().get(0));
        }

        message.setMessageTitle(request.getMessageTitle());
        message.setMessageContent(request.getMessageContent());
        message.setStatus(MessageStatus.PENDING.getCode());
        message.setPriority(request.getPriority());
        message.setScheduledTime(request.getScheduledTime());
        message.setMaxRetryCount(request.getMaxRetryCount());
        message.setBusinessType(request.getBusinessType());
        message.setBusinessId(request.getBusinessId());
        message.setSender(request.getSender());
        message.setSenderId(request.getSenderId());
        message.setExpireTime(request.getExpireTime());

        // 序列化变量和附件
        if (request.getVariables() != null) {
            try {
                message.setMessageVariables(objectMapper.writeValueAsString(request.getVariables()));
            } catch (JsonProcessingException e) {
                logger.warn("序列化消息变量失败: {}", e.getMessage());
            }
        }

        if (request.getAttachments() != null) {
            try {
                message.setAttachments(objectMapper.writeValueAsString(request.getAttachments()));
            } catch (JsonProcessingException e) {
                logger.warn("序列化附件信息失败: {}", e.getMessage());
            }
        }

        return message;
    }

    /**
     * 拆分批量请求
     */
    private List<NotificationSendRequest> splitBatchRequest(NotificationSendRequest request) {
        List<NotificationSendRequest> requests = new ArrayList<>();

        for (int i = 0; i < request.getRecipients().size(); i++) {
            NotificationSendRequest singleRequest = new NotificationSendRequest();

            // 复制基本信息
            singleRequest.setTemplateCode(request.getTemplateCode());
            singleRequest.setChannelType(request.getChannelType());
            singleRequest.setMessageTitle(request.getMessageTitle());
            singleRequest.setMessageContent(request.getMessageContent());
            singleRequest.setVariables(request.getVariables());
            singleRequest.setPriority(request.getPriority());
            singleRequest.setScheduledTime(request.getScheduledTime());
            singleRequest.setMaxRetryCount(request.getMaxRetryCount());
            singleRequest.setBusinessType(request.getBusinessType());
            singleRequest.setBusinessId(request.getBusinessId());
            singleRequest.setSender(request.getSender());
            singleRequest.setSenderId(request.getSenderId());
            singleRequest.setAttachments(request.getAttachments());
            singleRequest.setExpireTime(request.getExpireTime());
            singleRequest.setAsync(false);
            singleRequest.setBatch(false);

            // 设置单个接收人
            singleRequest.setRecipients(Arrays.asList(request.getRecipients().get(i)));

            if (request.getRecipientIds() != null && i < request.getRecipientIds().size()) {
                singleRequest.setRecipientIds(Arrays.asList(request.getRecipientIds().get(i)));
            }

            if (request.getRecipientNames() != null && i < request.getRecipientNames().size()) {
                singleRequest.setRecipientNames(Arrays.asList(request.getRecipientNames().get(i)));
            }

            requests.add(singleRequest);
        }

        return requests;
    }

    @Override
    @Transactional(readOnly = true)
    public NotificationMessageResponse getMessage(String messageId) {
        logger.debug("查询消息: 消息ID={}", messageId);

        NotificationMessage message = messageRepository.findByMessageIdAndDeletedFalse(messageId)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "消息不存在"));

        return notificationMapper.toMessageResponse(message);
    }

    @Override
    @Transactional(readOnly = true)
    public PageResult<NotificationMessageResponse> getMessages(String recipient, String channelType, String status,
                                                              Integer page, Integer size) {
        logger.debug("分页查询消息: 接收人={}, 渠道={}, 状态={}", recipient, channelType, status);

        Pageable pageable = PageRequest.of(page - 1, size, Sort.by(Sort.Direction.DESC, "createTime"));

        Page<NotificationMessage> messagePage;

        if (StringUtils.isNotBlank(recipient)) {
            messagePage = messageRepository.findByRecipientAndDeletedFalseOrderByCreateTimeDesc(recipient, pageable);
        } else if (StringUtils.isNotBlank(channelType)) {
            messagePage = messageRepository.findByChannelTypeAndDeletedFalse(channelType, pageable);
        } else if (StringUtils.isNotBlank(status)) {
            messagePage = messageRepository.findByStatusAndDeletedFalse(status, pageable);
        } else {
            messagePage = messageRepository.findAll(pageable);
        }

        List<NotificationMessageResponse> responses = messagePage.getContent().stream()
                .map(notificationMapper::toMessageResponse)
                .collect(Collectors.toList());

        return new PageResult<>(responses, messagePage.getTotalElements(), page, size);
    }

    @Override
    @Transactional(readOnly = true)
    public List<NotificationMessageResponse> getUnreadMessages(Long recipientId) {
        logger.debug("查询用户未读消息: 用户ID={}", recipientId);

        List<NotificationMessage> messages = messageRepository.findUnreadMessages(recipientId);

        return messages.stream()
                .map(notificationMapper::toMessageResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Long getUnreadMessageCount(Long recipientId) {
        logger.debug("查询用户未读消息数量: 用户ID={}", recipientId);

        return messageRepository.countUnreadMessages(recipientId);
    }

    @Override
    public Boolean markAsRead(String messageId, Long recipientId) {
        logger.info("标记消息为已读: 消息ID={}, 用户ID={}", messageId, recipientId);

        try {
            NotificationMessage message = messageRepository.findByMessageIdAndDeletedFalse(messageId)
                    .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "消息不存在"));

            if (!recipientId.equals(message.getRecipientId())) {
                throw new BusinessException(ResultCode.FORBIDDEN, "无权限操作此消息");
            }

            if (!message.getIsRead()) {
                message.setIsRead(true);
                message.setReadTime(LocalDateTime.now());
                messageRepository.save(message);
            }

            return true;

        } catch (Exception e) {
            logger.error("标记消息为已读失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "标记消息为已读失败: " + e.getMessage());
        }
    }

    @Override
    public Integer batchMarkAsRead(List<String> messageIds, Long recipientId) {
        logger.info("批量标记消息为已读: 消息数量={}, 用户ID={}", messageIds.size(), recipientId);

        try {
            int successCount = 0;

            for (String messageId : messageIds) {
                try {
                    if (markAsRead(messageId, recipientId)) {
                        successCount++;
                    }
                } catch (Exception e) {
                    logger.warn("标记消息为已读失败: 消息ID={}, 错误={}", messageId, e.getMessage());
                }
            }

            return successCount;

        } catch (Exception e) {
            logger.error("批量标记消息为已读失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "批量标记消息为已读失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<NotificationMessageResponse> getBusinessMessages(String businessType, String businessId) {
        logger.debug("查询业务相关消息: 业务类型={}, 业务ID={}", businessType, businessId);

        List<NotificationMessage> messages = messageRepository.findByBusinessTypeAndBusinessIdAndDeletedFalse(businessType, businessId);

        return messages.stream()
                .map(notificationMapper::toMessageResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getMessageStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        logger.debug("获取消息统计信息: 开始时间={}, 结束时间={}", startTime, endTime);

        Map<String, Object> statistics = new HashMap<>();

        // 基础统计
        Long totalMessages = messageRepository.countByTimeRange(startTime, endTime);
        Long sentMessages = messageRepository.countSentByTimeRange(startTime, endTime);
        Long failedMessages = messageRepository.countFailedByTimeRange(startTime, endTime);

        statistics.put("totalMessages", totalMessages);
        statistics.put("sentMessages", sentMessages);
        statistics.put("failedMessages", failedMessages);
        statistics.put("successRate", totalMessages > 0 ? (double) sentMessages / totalMessages * 100 : 0);

        // 按状态统计
        List<Object[]> statusStats = messageRepository.countByStatus();
        Map<String, Long> statusMap = new HashMap<>();
        for (Object[] stat : statusStats) {
            statusMap.put((String) stat[0], (Long) stat[1]);
        }
        statistics.put("statusStats", statusMap);

        // 按渠道统计
        List<Object[]> channelStats = messageRepository.countByChannelType();
        Map<String, Long> channelMap = new HashMap<>();
        for (Object[] stat : channelStats) {
            channelMap.put((String) stat[0], (Long) stat[1]);
        }
        statistics.put("channelStats", channelMap);

        // 按日期统计
        List<Object[]> dateStats = messageRepository.countByDate(startTime, endTime);
        statistics.put("dateStats", dateStats);

        // 按小时统计
        List<Object[]> hourStats = messageRepository.countByHour(startTime, endTime);
        statistics.put("hourStats", hourStats);

        // TOP接收人
        Pageable topPageable = PageRequest.of(0, 10);
        List<Object[]> topRecipients = messageRepository.findTopRecipients(startTime, endTime, topPageable);
        statistics.put("topRecipients", topRecipients);

        return statistics;
    }

    @Override
    public void processPendingMessages() {
        logger.debug("处理待发送消息");

        try {
            List<NotificationMessage> pendingMessages = messageRepository.findPendingMessages(LocalDateTime.now());

            for (NotificationMessage message : pendingMessages) {
                try {
                    // 更新状态为发送中
                    message.setStatus(MessageStatus.SENDING.getCode());
                    messageRepository.save(message);

                    // 发送消息
                    boolean success = channelService.send(message);

                    // 更新状态
                    if (success) {
                        message.setStatus(MessageStatus.SENT.getCode());
                        message.setSendTime(LocalDateTime.now());
                    } else {
                        message.setStatus(MessageStatus.FAILED.getCode());
                        message.setErrorMessage("发送失败");
                    }

                    messageRepository.save(message);

                } catch (Exception e) {
                    logger.error("处理待发送消息失败: 消息ID={}, 错误={}", message.getMessageId(), e.getMessage());

                    message.setStatus(MessageStatus.FAILED.getCode());
                    message.setErrorMessage(e.getMessage());
                    messageRepository.save(message);
                }
            }

        } catch (Exception e) {
            logger.error("处理待发送消息失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void processRetryMessages() {
        logger.debug("处理重试消息");

        try {
            List<NotificationMessage> retryMessages = messageRepository.findRetryableMessages();

            for (NotificationMessage message : retryMessages) {
                try {
                    // 更新重试次数和状态
                    message.setRetryCount(message.getRetryCount() + 1);
                    message.setStatus(MessageStatus.RETRYING.getCode());
                    message.setErrorMessage(null);
                    messageRepository.save(message);

                    // 发送消息
                    boolean success = channelService.send(message);

                    // 更新状态
                    if (success) {
                        message.setStatus(MessageStatus.SENT.getCode());
                        message.setSendTime(LocalDateTime.now());
                    } else {
                        message.setStatus(MessageStatus.FAILED.getCode());
                        message.setErrorMessage("重试发送失败");
                    }

                    messageRepository.save(message);

                } catch (Exception e) {
                    logger.error("处理重试消息失败: 消息ID={}, 错误={}", message.getMessageId(), e.getMessage());

                    message.setStatus(MessageStatus.FAILED.getCode());
                    message.setErrorMessage(e.getMessage());
                    messageRepository.save(message);
                }
            }

        } catch (Exception e) {
            logger.error("处理重试消息失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void cleanupExpiredMessages() {
        logger.debug("清理过期消息");

        try {
            List<NotificationMessage> expiredMessages = messageRepository.findExpiredMessages(LocalDateTime.now());

            for (NotificationMessage message : expiredMessages) {
                message.setStatus(MessageStatus.EXPIRED.getCode());
                messageRepository.save(message);
            }

            logger.info("清理过期消息完成: 数量={}", expiredMessages.size());

        } catch (Exception e) {
            logger.error("清理过期消息失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void sendWorkflowNotification(String processInstanceId, String taskId, String eventType, Map<String, Object> variables) {
        logger.info("发送工作流通知: 流程实例ID={}, 任务ID={}, 事件类型={}", processInstanceId, taskId, eventType);

        try {
            // 根据事件类型确定模板
            String templateCode = getWorkflowTemplateCode(eventType);
            if (StringUtils.isBlank(templateCode)) {
                logger.warn("未找到工作流事件对应的模板: 事件类型={}", eventType);
                return;
            }

            // 获取接收人列表
            List<String> recipients = getWorkflowRecipients(processInstanceId, taskId, eventType, variables);
            if (recipients.isEmpty()) {
                logger.warn("未找到工作流通知接收人: 流程实例ID={}", processInstanceId);
                return;
            }

            // 构建通知变量
            Map<String, Object> notificationVariables = new HashMap<>(variables);
            notificationVariables.put("processInstanceId", processInstanceId);
            notificationVariables.put("taskId", taskId);
            notificationVariables.put("eventType", eventType);

            // 发送通知
            List<String> channelTypes = Arrays.asList("EMAIL", "INTERNAL");
            for (String channelType : channelTypes) {
                try {
                    sendByTemplate(templateCode, channelType, recipients, notificationVariables);
                } catch (Exception e) {
                    logger.error("发送工作流通知失败: 渠道={}, 错误={}", channelType, e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.error("发送工作流通知失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void sendSystemNotification(String title, String content, List<String> recipients, List<String> channelTypes) {
        logger.info("发送系统通知: 标题={}, 接收人数={}, 渠道数={}", title, recipients.size(), channelTypes.size());

        try {
            for (String channelType : channelTypes) {
                NotificationSendRequest request = new NotificationSendRequest();
                request.setChannelType(channelType);
                request.setRecipients(recipients);
                request.setMessageTitle(title);
                request.setMessageContent(content);
                request.setBusinessType("SYSTEM");
                request.setSender("system");
                request.setSenderId(0L);
                request.setPriority(80); // 系统通知高优先级
                request.setAsync(true);

                sendMessage(request);
            }

        } catch (Exception e) {
            logger.error("发送系统通知失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送多渠道通知（新增方法）
     */
    public List<NotificationMessageResponse> sendMultiChannelNotification(String title, String content,
                                                                         List<String> recipients,
                                                                         List<String> channelTypes) {
        logger.info("发送多渠道通知: 标题={}, 接收人数={}, 渠道数={}", title, recipients.size(), channelTypes.size());

        List<NotificationMessageResponse> responses = new ArrayList<>();

        try {
            // 转换渠道类型
            List<ChannelType> channels = channelTypes.stream()
                .map(ChannelType::fromCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            if (channels.isEmpty()) {
                logger.warn("没有有效的通知渠道");
                return responses;
            }

            // 为每个接收人发送多渠道通知
            for (String recipient : recipients) {
                // 构建通知请求
                NotificationSendRequest request =
                    new NotificationSendRequest();
                request.setRecipient(recipient);
                request.setMessageTitle(title);
                request.setMessageContent(content);

                // 发送多渠道通知
                List<NotificationSendResult> results =
                    multiChannelManager.sendMultiChannelNotification(channelTypes, request);

                // 转换结果
                for (NotificationSendResult result : results) {
                    NotificationMessageResponse response = convertToMessageResponse(result);
                    responses.add(response);

                    // 保存通知记录
                    saveNotificationRecord(result, title, content, recipient);
                }
            }

        } catch (Exception e) {
            logger.error("发送多渠道通知失败: {}", e.getMessage(), e);
        }

        return responses;
    }

    /**
     * 获取渠道状态（新增方法）
     */
    public Map<String, Object> getChannelStatus() {
        logger.debug("获取通知渠道状态");

        try {
            return multiChannelManager.getChannelStatus().entrySet().stream()
                .collect(Collectors.toMap(
                    entry -> entry.getKey(),
                    Map.Entry::getValue
                ));
        } catch (Exception e) {
            logger.error("获取渠道状态失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 测试渠道连接（新增方法）
     */
    public Map<String, Object> testAllChannelConnections() {
        logger.debug("测试所有渠道连接");

        try {
            // 直接构造新 Map，避免不必要的 Stream 操作，提高性能
            return new HashMap<>(multiChannelManager.testAllChannelConnections());
        } catch (Exception e) { // 替换 SomeSpecificException 为实际可能抛出的异常类
            logger.error("测试渠道连接失败", e);
            return new HashMap<>();
        }
    }


    /**
     * 获取渠道统计信息（新增方法）
     */
    public Map<String, Object> getChannelStatistics() {
        logger.debug("获取渠道统计信息");

        try {
            return multiChannelManager.getAllChannelStatistics().entrySet().stream()
                .collect(Collectors.toMap(
                    entry -> entry.getKey(),
                    Map.Entry::getValue
                ));
        } catch (Exception e) {
            logger.error("获取渠道统计信息失败: {}", e.getMessage(), e);
            return new HashMap<>();
        }
    }

    /**
     * 转换为消息响应对象
     */
    private NotificationMessageResponse convertToMessageResponse(NotificationSendResult result) {
        NotificationMessageResponse response = new NotificationMessageResponse();
        response.setMessageId(result.getMessageId());
        response.setChannelType(result.getChannelType());
        response.setRecipient(result.getRecipient());
        response.setStatus(result.isSuccess() ? MessageStatus.SENT.getCode() : MessageStatus.FAILED.getCode());
        response.setErrorMessage(result.getErrorMessage());
        response.setSendTime(result.getSendTime());
        return response;
    }

    /**
     * 保存通知记录
     */
    private void saveNotificationRecord(NotificationSendResult result,
                                        String title, String content, String recipient) {
        try {
            NotificationMessage message = new NotificationMessage();
            message.setMessageId(result.getMessageId());
            message.setChannelType(result.getChannelType());
            message.setRecipient(recipient);
            message.setMessageTitle(title);
            message.setMessageContent(content);
            message.setStatus(result.isSuccess() ? MessageStatus.SENT.getCode() : MessageStatus.FAILED.getCode());
            message.setErrorMessage(result.getErrorMessage());
            message.setSendTime(result.getSendTime());
            message.setBusinessType("MULTI_CHANNEL");
            message.setSender("system");
            message.setSenderId(0L);
            message.setPriority(50);
            message.setMaxRetryCount(3);
            message.setRetryCount(0);
            message.setIsRead(false);
            message.setDeleted(false);
            message.setCreateTime(LocalDateTime.now());
            message.setCreateBy("system");

            messageRepository.save(message);

        } catch (Exception e) {
            logger.error("保存通知记录失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取工作流模板编码
     */
    private String getWorkflowTemplateCode(String eventType) {
        switch (eventType) {
            case "PROCESS_STARTED":
                return "WORKFLOW_PROCESS_STARTED";
            case "TASK_CREATED":
                return "WORKFLOW_TASK_CREATED";
            case "TASK_ASSIGNED":
                return "WORKFLOW_TASK_ASSIGNED";
            case "TASK_COMPLETED":
                return "WORKFLOW_TASK_COMPLETED";
            case "PROCESS_COMPLETED":
                return "WORKFLOW_PROCESS_COMPLETED";
            case "PROCESS_CANCELLED":
                return "WORKFLOW_PROCESS_CANCELLED";
            default:
                return null;
        }
    }

    /**
     * 获取工作流通知接收人
     */
    private List<String> getWorkflowRecipients(String processInstanceId, String taskId, String eventType, Map<String, Object> variables) {
        List<String> recipients = new ArrayList<>();

        try {
            // 根据事件类型和变量确定接收人
            switch (eventType) {
                case "PROCESS_STARTED":
                    // 流程发起人
                    String initiator = (String) variables.get("initiatorUsername");
                    if (StringUtils.isNotBlank(initiator)) {
                        recipients.add(initiator);
                    }
                    break;
                case "TASK_CREATED":
                case "TASK_ASSIGNED":
                    // 任务指派人
                    String assignee = (String) variables.get("assignee");
                    if (StringUtils.isNotBlank(assignee)) {
                        recipients.add(assignee);
                    }
                    break;
                case "TASK_COMPLETED":
                    // 流程发起人和下一个任务指派人
                    String processInitiator = (String) variables.get("initiatorUsername");
                    if (StringUtils.isNotBlank(processInitiator)) {
                        recipients.add(processInitiator);
                    }
                    String nextAssignee = (String) variables.get("nextAssignee");
                    if (StringUtils.isNotBlank(nextAssignee)) {
                        recipients.add(nextAssignee);
                    }
                    break;
                case "PROCESS_COMPLETED":
                case "PROCESS_CANCELLED":
                    // 流程发起人
                    String finalInitiator = (String) variables.get("initiatorUsername");
                    if (StringUtils.isNotBlank(finalInitiator)) {
                        recipients.add(finalInitiator);
                    }
                    break;
            }

        } catch (Exception e) {
            logger.error("获取工作流通知接收人失败: {}", e.getMessage(), e);
        }

        return recipients;
    }
}
