package com.hzwangda.edu.notification.enums;

/**
 * 消息状态枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum MessageStatus {

    // 待发送
    PENDING("待发送", "消息已创建，等待发送"),

    // 发送中
    SENDING("发送中", "消息正在发送"),

    // 发送成功
    SENT("发送成功", "消息已成功发送"),

    // 成功（兼容性别名）
    SUCCESS("发送成功", "消息已成功发送"),

    // 已送达
    DELIVERED("已送达", "消息已送达到接收方"),

    // 已读
    READ("已读", "消息已被接收方阅读"),

    // 发送失败
    FAILED("发送失败", "消息发送失败"),

    // 已取消
    CANCELLED("已取消", "消息发送已取消"),

    // 已过期
    EXPIRED("已过期", "消息已过期"),

    // 重试中
    RETRYING("重试中", "消息正在重试发送"),

    // 已暂停
    PAUSED("已暂停", "消息发送已暂停"),

    // 已拒绝
    REJECTED("已拒绝", "消息被接收方拒绝"),

    // 死信
    DEAD_LETTER("死信", "消息重试失败，已移入死信队列"),

    // 未知状态
    UNKNOWN("未知", "消息状态未知");

    private final String description;
    private final String detail;

    MessageStatus(String description, String detail) {
        this.description = description;
        this.detail = detail;
    }

    public String getDescription() {
        return description;
    }

    public String getDetail() {
        return detail;
    }

    public String getCode() {
        return this.name();
    }

    /**
     * 根据代码获取枚举
     */
    public static MessageStatus fromCode(String code) {
        for (MessageStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return UNKNOWN;
    }

    /**
     * 判断是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == SENT || this == SUCCESS || this == DELIVERED || this == READ ||
               this == FAILED || this == CANCELLED || this == EXPIRED || this == REJECTED || this == DEAD_LETTER;
    }

    /**
     * 判断是否为成功状态
     */
    public boolean isSuccessStatus() {
        return this == SENT || this == SUCCESS || this == DELIVERED || this == READ;
    }

    /**
     * 判断是否为失败状态
     */
    public boolean isFailureStatus() {
        return this == FAILED || this == CANCELLED || this == EXPIRED || this == REJECTED || this == DEAD_LETTER;
    }

    /**
     * 判断是否为处理中状态
     */
    public boolean isProcessingStatus() {
        return this == PENDING || this == SENDING || this == RETRYING || this == PAUSED;
    }

    /**
     * 判断是否可以重试
     */
    public boolean canRetry() {
        return this == FAILED || this == EXPIRED;
    }

    /**
     * 判断是否可以取消
     */
    public boolean canCancel() {
        return this == PENDING || this == PAUSED;
    }
}
