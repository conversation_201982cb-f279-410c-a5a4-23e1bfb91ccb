package com.hzwangda.edu.notification.entity;

import com.hzwangda.edu.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 短信发送记录实体
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "notification_sms_record")
public class SmsRecord extends BaseEntity {

    /**
     * 关联的通知消息ID
     */
    @Column(name = "message_id", length = 64)
    private String messageId;

    /**
     * 手机号码
     */
    @Column(name = "phone_number", nullable = false, length = 20)
    private String phoneNumber;

    /**
     * 短信内容
     */
    @Column(name = "content", nullable = false, columnDefinition = "TEXT")
    private String content;

    /**
     * 模板编码
     */
    @Column(name = "template_code", length = 100)
    private String templateCode;

    /**
     * 服务商模板ID（阿里云、腾讯云等）
     */
    private String providerTemplateId;

    /**
     * 模板参数（JSON格式）
     */
    @Column(name = "template_params", columnDefinition = "TEXT")
    private String templateParams;

    /**
     * 服务商类型
     */
    @Column(name = "provider_type", length = 50)
    private String providerType;

    /**
     * 服务商返回的消息ID
     */
    @Column(name = "provider_message_id", length = 100)
    private String providerMessageId;

    /**
     * 发送状态（PENDING, SENDING, SUCCESS, FAILED）
     */
    @Column(name = "send_status", nullable = false, length = 50)
    private String sendStatus;

    /**
     * 发送时间
     */
    @Column(name = "send_time")
    private LocalDateTime sendTime;

    /**
     * 定时发送时间
     */
    @Column(name = "scheduled_time")
    private LocalDateTime scheduledTime;

    /**
     * 接收时间（回调时间）
     */
    @Column(name = "receive_time")
    private LocalDateTime receiveTime;

    /**
     * 错误码
     */
    @Column(name = "error_code", length = 50)
    private String errorCode;

    /**
     * 错误信息
     */
    @Column(name = "error_message", length = 500)
    private String errorMessage;

    /**
     * 重试次数
     */
    @Column(name = "retry_count")
    private Integer retryCount = 0;

    /**
     * 最大重试次数
     */
    @Column(name = "max_retry_count")
    private Integer maxRetryCount = 3;

    /**
     * 下次重试时间
     */
    @Column(name = "next_retry_time")
    private LocalDateTime nextRetryTime;

    /**
     * 费用（分）
     */
    @Column(name = "cost")
    private Integer cost;

    /**
     * 签名名称
     */
    @Column(name = "sign_name", length = 100)
    private String signName;

    /**
     * 扩展信息（JSON格式）
     */
    @Column(name = "extra_info", columnDefinition = "TEXT")
    private String extraInfo;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
}
