package com.hzwangda.edu.notification.service.impl;

import com.hzwangda.edu.common.exception.BusinessException;
import com.hzwangda.edu.common.result.ResultCode;
import com.hzwangda.edu.notification.config.SmsConfig;
import com.hzwangda.edu.notification.entity.SmsRecord;
import com.hzwangda.edu.notification.entity.SmsTemplate;
import com.hzwangda.edu.notification.service.SmsService;
import com.hzwangda.edu.notification.service.provider.AliyunSmsProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 短信服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class SmsServiceImpl implements SmsService {

    @Autowired
    private SmsConfig smsConfig;

    @Autowired
    private AliyunSmsProvider aliyunSmsProvider;

    // 内存存储（实际项目中应该使用数据库）
    private final Map<String, SmsRecord> smsRecords = new ConcurrentHashMap<>();
    private final Map<String, SmsTemplate> smsTemplates = new ConcurrentHashMap<>();
    private final Map<String, List<LocalDateTime>> sendLimitTracker = new ConcurrentHashMap<>();

    @Override
    public SmsRecord sendSms(String phoneNumber, String content) {
        log.info("发送短信: phoneNumber={}, content={}", phoneNumber, content);

        try {
            // 检查发送限制
            if (!checkSendLimit(phoneNumber)) {
                throw new BusinessException(ResultCode.TOO_MANY_REQUESTS, "短信发送频率超限");
            }

            // 创建发送记录
            SmsRecord record = SmsRecord.builder()
                    .phoneNumber(phoneNumber)
                    .content(content)
                    .providerType(smsConfig.getProvider())
                    .sendStatus("PENDING")
                    .sendTime(LocalDateTime.now())
                    .retryCount(0)
                    .maxRetryCount(3)
                    .build();

            // 调用具体的短信服务商发送
            boolean success = sendSmsToProvider(record);

            if (success) {
                record.setSendStatus("SUCCESS");
                log.info("短信发送成功: recordId={}", record.getId());
            } else {
                record.setSendStatus("FAILED");
                log.error("短信发送失败: recordId={}", record.getId());
            }

            // 保存记录
            smsRecords.put(record.getId().toString(), record);

            // 记录发送限制
            recordSendLimit(phoneNumber);

            return record;

        } catch (Exception e) {
            log.error("短信发送异常: phoneNumber={}, error={}", phoneNumber, e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "短信发送失败: " + e.getMessage());
        }
    }

    @Override
    public SmsRecord sendSmsWithTemplate(String phoneNumber, String templateCode, Map<String, Object> templateParams) {
        log.info("使用模板发送短信: phoneNumber={}, templateCode={}", phoneNumber, templateCode);

        try {
            // 获取模板
            SmsTemplate template = getSmsTemplate(templateCode);
            if (template == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "短信模板不存在: " + templateCode);
            }

            if (!template.getEnabled()) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "短信模板已禁用: " + templateCode);
            }

            // 验证模板参数
            if (!validateSmsTemplate(templateCode, templateParams)) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "模板参数验证失败");
            }

            // 替换模板内容
            String content = replaceTemplateContent(template.getTemplateContent(), templateParams);

            // 检查发送限制
            if (!checkSendLimit(phoneNumber)) {
                throw new BusinessException(ResultCode.TOO_MANY_REQUESTS, "短信发送频率超限");
            }

            // 创建发送记录
            SmsRecord record = SmsRecord.builder()
                    .phoneNumber(phoneNumber)
                    .content(content)
                    .templateCode(templateCode)
                    .templateParams(templateParams != null ? templateParams.toString() : null)
                    .providerType(smsConfig.getProvider())
                    .providerTemplateId(template.getProviderTemplateId())
                    .signName(template.getSignName())
                    .sendStatus("PENDING")
                    .sendTime(LocalDateTime.now())
                    .retryCount(0)
                    .maxRetryCount(3)
                    .build();

            // 调用具体的短信服务商发送
            boolean success = sendSmsToProviderWithTemplate(record, template, templateParams);

            if (success) {
                record.setSendStatus("SUCCESS");
                template.setSuccessCount(template.getSuccessCount() + 1);
                log.info("模板短信发送成功: recordId={}, templateCode={}", record.getId(), templateCode);
            } else {
                record.setSendStatus("FAILED");
                template.setFailureCount(template.getFailureCount() + 1);
                log.error("模板短信发送失败: recordId={}, templateCode={}", record.getId(), templateCode);
            }

            // 更新使用次数
            template.setUsageCount(template.getUsageCount() + 1);
            smsTemplates.put(templateCode, template);

            // 保存记录
            smsRecords.put(record.getId().toString(), record);

            // 记录发送限制
            recordSendLimit(phoneNumber);

            return record;

        } catch (Exception e) {
            log.error("模板短信发送异常: phoneNumber={}, templateCode={}, error={}",
                     phoneNumber, templateCode, e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "模板短信发送失败: " + e.getMessage());
        }
    }

    @Override
    public List<SmsRecord> sendBatchSms(List<String> phoneNumbers, String content) {
        log.info("批量发送短信: phoneNumbers={}, content={}", phoneNumbers.size(), content);

        return phoneNumbers.stream()
                .map(phoneNumber -> {
                    try {
                        return sendSms(phoneNumber, content);
                    } catch (Exception e) {
                        log.error("批量发送短信失败: phoneNumber={}, error={}", phoneNumber, e.getMessage());
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public List<SmsRecord> sendBatchSmsWithTemplate(List<String> phoneNumbers, String templateCode, Map<String, Object> templateParams) {
        log.info("批量使用模板发送短信: phoneNumbers={}, templateCode={}", phoneNumbers.size(), templateCode);

        return phoneNumbers.stream()
                .map(phoneNumber -> {
                    try {
                        return sendSmsWithTemplate(phoneNumber, templateCode, templateParams);
                    } catch (Exception e) {
                        log.error("批量模板短信发送失败: phoneNumber={}, error={}", phoneNumber, e.getMessage());
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public SmsRecord getSmsRecord(String recordId) {
        return smsRecords.get(recordId);
    }

    @Override
    public List<SmsRecord> getSmsRecords(String phoneNumber, String startTime, String endTime) {
        return smsRecords.values().stream()
                .filter(record -> phoneNumber == null || phoneNumber.equals(record.getPhoneNumber()))
                .collect(Collectors.toList());
    }

    @Override
    public boolean retrySms(String recordId) {
        log.info("重试发送短信: recordId={}", recordId);

        SmsRecord record = smsRecords.get(recordId);
        if (record == null) {
            log.warn("短信记录不存在: recordId={}", recordId);
            return false;
        }

        if (record.getRetryCount() >= record.getMaxRetryCount()) {
            log.warn("短信重试次数已达上限: recordId={}, retryCount={}", recordId, record.getRetryCount());
            return false;
        }

        try {
            record.setRetryCount(record.getRetryCount() + 1);
            record.setSendStatus("PENDING");
            record.setSendTime(LocalDateTime.now());

            boolean success = sendSmsToProvider(record);

            if (success) {
                record.setSendStatus("SUCCESS");
                log.info("短信重试发送成功: recordId={}", recordId);
            } else {
                record.setSendStatus("FAILED");
                record.setNextRetryTime(LocalDateTime.now().plusMinutes(5)); // 5分钟后重试
                log.error("短信重试发送失败: recordId={}", recordId);
            }

            smsRecords.put(recordId, record);
            return success;

        } catch (Exception e) {
            log.error("短信重试发送异常: recordId={}, error={}", recordId, e.getMessage(), e);
            record.setSendStatus("FAILED");
            record.setErrorMessage(e.getMessage());
            smsRecords.put(recordId, record);
            return false;
        }
    }

    @Override
    public boolean handleSmsCallback(Map<String, Object> callbackData) {
        log.info("处理短信回调: {}", callbackData);

        try {
            String providerMessageId = (String) callbackData.get("messageId");
            String status = (String) callbackData.get("status");
            String errorCode = (String) callbackData.get("errorCode");
            String errorMessage = (String) callbackData.get("errorMessage");

            // 根据服务商消息ID查找记录
            SmsRecord record = smsRecords.values().stream()
                    .filter(r -> providerMessageId.equals(r.getProviderMessageId()))
                    .findFirst()
                    .orElse(null);

            if (record == null) {
                log.warn("未找到对应的短信记录: providerMessageId={}", providerMessageId);
                return false;
            }

            // 更新记录状态
            record.setSendStatus(status);
            record.setReceiveTime(LocalDateTime.now());
            if (StringUtils.hasText(errorCode)) {
                record.setErrorCode(errorCode);
            }
            if (StringUtils.hasText(errorMessage)) {
                record.setErrorMessage(errorMessage);
            }

            smsRecords.put(record.getId().toString(), record);

            log.info("短信回调处理成功: recordId={}, status={}", record.getId(), status);
            return true;

        } catch (Exception e) {
            log.error("处理短信回调失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean checkSendLimit(String phoneNumber) {
        if (!StringUtils.hasText(phoneNumber)) {
            return false;
        }

        SmsConfig.SmsLimitConfig limitConfig = smsConfig.getLimit();
        List<LocalDateTime> sendTimes = sendLimitTracker.getOrDefault(phoneNumber, new ArrayList<>());
        LocalDateTime now = LocalDateTime.now();

        // 清理过期记录
        sendTimes.removeIf(time -> time.isBefore(now.minusDays(1)));

        // 检查每分钟限制
        long countPerMinute = sendTimes.stream()
                .filter(time -> time.isAfter(now.minusMinutes(1)))
                .count();
        if (countPerMinute >= limitConfig.getMaxPerPhonePerMinute()) {
            return false;
        }

        // 检查每小时限制
        long countPerHour = sendTimes.stream()
                .filter(time -> time.isAfter(now.minusHours(1)))
                .count();
        if (countPerHour >= limitConfig.getMaxPerPhonePerHour()) {
            return false;
        }

        // 检查每天限制
        long countPerDay = sendTimes.stream()
                .filter(time -> time.isAfter(now.minusDays(1)))
                .count();
        if (countPerDay >= limitConfig.getMaxPerPhonePerDay()) {
            return false;
        }

        return true;
    }

    @Override
    public Map<String, Object> getSmsStatistics(String startTime, String endTime) {
        Map<String, Object> statistics = new HashMap<>();

        List<SmsRecord> records = new ArrayList<>(smsRecords.values());

        long totalCount = records.size();
        long successCount = records.stream()
                .filter(r -> "SUCCESS".equals(r.getSendStatus()))
                .count();
        long failedCount = records.stream()
                .filter(r -> "FAILED".equals(r.getSendStatus()))
                .count();

        statistics.put("totalCount", totalCount);
        statistics.put("successCount", successCount);
        statistics.put("failedCount", failedCount);
        statistics.put("successRate", totalCount > 0 ? (double) successCount / totalCount * 100 : 0);

        return statistics;
    }

    @Override
    public SmsTemplate createSmsTemplate(SmsTemplate template) {
        log.info("创建短信模板: templateCode={}", template.getTemplateCode());

        if (smsTemplates.containsKey(template.getTemplateCode())) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "模板编码已存在: " + template.getTemplateCode());
        }

        template.setUsageCount(0L);
        template.setSuccessCount(0L);
        template.setFailureCount(0L);
        template.setAuditStatus("PENDING");

        smsTemplates.put(template.getTemplateCode(), template);
        return template;
    }

    @Override
    public SmsTemplate updateSmsTemplate(SmsTemplate template) {
        log.info("更新短信模板: templateCode={}", template.getTemplateCode());

        if (!smsTemplates.containsKey(template.getTemplateCode())) {
            throw new BusinessException(ResultCode.NOT_FOUND, "模板不存在: " + template.getTemplateCode());
        }

        smsTemplates.put(template.getTemplateCode(), template);
        return template;
    }

    @Override
    public boolean deleteSmsTemplate(String templateCode) {
        log.info("删除短信模板: templateCode={}", templateCode);
        return smsTemplates.remove(templateCode) != null;
    }

    @Override
    public SmsTemplate getSmsTemplate(String templateCode) {
        return smsTemplates.get(templateCode);
    }

    @Override
    public List<SmsTemplate> getSmsTemplates() {
        return new ArrayList<>(smsTemplates.values());
    }

    @Override
    public boolean validateSmsTemplate(String templateCode, Map<String, Object> templateParams) {
        SmsTemplate template = getSmsTemplate(templateCode);
        if (template == null) {
            return false;
        }

        // 验证必需参数
        if (template.getTemplateParams() != null && !template.getTemplateParams().isEmpty()) {
//            for (String requiredParam : template.getTemplateParams()) {
//                if (templateParams == null || !templateParams.containsKey(requiredParam)) {
//                    log.warn("模板参数验证失败: 缺少必需参数 {}", requiredParam);
//                    return false;
//                }
//            }
        }

        return true;
    }

    // ==================== 增强功能实现 ====================

    @Override
    public String querySmsStatus(String recordId) {
        log.info("查询短信状态: recordId={}", recordId);

        SmsRecord record = smsRecords.get(recordId);
        if (record == null) {
            log.warn("短信记录不存在: recordId={}", recordId);
            return "NOT_FOUND";
        }

        // 如果是阿里云且有bizId，查询实时状态
        if ("aliyun".equals(record.getProviderType()) && StringUtils.hasText(record.getProviderMessageId())) {
            try {
                String sendDate = record.getSendTime().toLocalDate().toString().replace("-", "");
                String status = aliyunSmsProvider.querySmsStatus(record.getPhoneNumber(),
                        record.getProviderMessageId(), sendDate);

                // 更新记录状态
                if (!"UNKNOWN".equals(status) && !"ERROR".equals(status)) {
                    record.setSendStatus(status);
                    smsRecords.put(recordId, record);
                }

                return status;
            } catch (Exception e) {
                log.error("查询阿里云短信状态失败: recordId={}, error={}", recordId, e.getMessage(), e);
            }
        }

        return record.getSendStatus();
    }

    @Override
    public SmsRecord scheduleSms(String phoneNumber, String content, LocalDateTime sendTime) {
        log.info("定时发送短信: phoneNumber={}, sendTime={}", phoneNumber, sendTime);

        try {
            // 创建定时发送记录
            SmsRecord record = SmsRecord.builder()
                    .phoneNumber(phoneNumber)
                    .content(content)
                    .providerType(smsConfig.getProvider())
                    .sendStatus("SCHEDULED")
                    .scheduledTime(sendTime)
                    .sendTime(LocalDateTime.now())
                    .retryCount(0)
                    .maxRetryCount(3)
                    .build();

            // 保存记录
            smsRecords.put(record.getId().toString(), record);

            // TODO: 集成定时任务调度器（如Quartz）
            log.info("定时短信已安排: recordId={}, scheduledTime={}", record.getId(), sendTime);

            return record;

        } catch (Exception e) {
            log.error("定时短信安排失败: phoneNumber={}, error={}", phoneNumber, e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "定时短信安排失败: " + e.getMessage());
        }
    }

    @Override
    public SmsRecord scheduleSmsWithTemplate(String phoneNumber, String templateCode,
                                           Map<String, Object> templateParams, LocalDateTime sendTime) {
        log.info("定时发送模板短信: phoneNumber={}, templateCode={}, sendTime={}",
                phoneNumber, templateCode, sendTime);

        try {
            // 验证模板
            SmsTemplate template = getSmsTemplate(templateCode);
            if (template == null) {
                throw new BusinessException(ResultCode.NOT_FOUND, "短信模板不存在: " + templateCode);
            }

            if (!validateSmsTemplate(templateCode, templateParams)) {
                throw new BusinessException(ResultCode.BAD_REQUEST, "模板参数验证失败");
            }

            // 创建定时发送记录
            SmsRecord record = SmsRecord.builder()
                    .phoneNumber(phoneNumber)
                    .templateCode(templateCode)
                    .templateParams(templateParams != null ? templateParams.toString() : null)
                    .providerType(smsConfig.getProvider())
                    .providerTemplateId(template.getProviderTemplateId())
                    .signName(template.getSignName())
                    .sendStatus("SCHEDULED")
                    .scheduledTime(sendTime)
                    .sendTime(LocalDateTime.now())
                    .retryCount(0)
                    .maxRetryCount(3)
                    .build();

            // 保存记录
            smsRecords.put(record.getId().toString(), record);

            // TODO: 集成定时任务调度器（如Quartz）
            log.info("定时模板短信已安排: recordId={}, templateCode={}, scheduledTime={}",
                    record.getId(), templateCode, sendTime);

            return record;

        } catch (Exception e) {
            log.error("定时模板短信安排失败: phoneNumber={}, templateCode={}, error={}",
                     phoneNumber, templateCode, e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "定时模板短信安排失败: " + e.getMessage());
        }
    }

    @Override
    public boolean cancelScheduledSms(String recordId) {
        log.info("取消定时短信: recordId={}", recordId);

        SmsRecord record = smsRecords.get(recordId);
        if (record == null) {
            log.warn("短信记录不存在: recordId={}", recordId);
            return false;
        }

        if (!"SCHEDULED".equals(record.getSendStatus())) {
            log.warn("短信不是定时状态，无法取消: recordId={}, status={}", recordId, record.getSendStatus());
            return false;
        }

        // 更新状态为已取消
        record.setSendStatus("CANCELLED");
        smsRecords.put(recordId, record);

        // TODO: 取消定时任务
        log.info("定时短信已取消: recordId={}", recordId);
        return true;
    }

    @Override
    public Map<String, Object> getSmsBalance() {
        log.info("获取短信余额");

        Map<String, Object> balance = new HashMap<>();

        // TODO: 调用服务商API获取真实余额
        // 这里返回模拟数据
        balance.put("provider", smsConfig.getProvider());
        balance.put("balance", 10000);
        balance.put("unit", "条");
        balance.put("queryTime", LocalDateTime.now());

        return balance;
    }

    @Override
    public boolean syncSmsStatus(String recordId) {
        log.info("同步短信状态: recordId={}", recordId);

        try {
            String status = querySmsStatus(recordId);
            return !"ERROR".equals(status) && !"NOT_FOUND".equals(status);
        } catch (Exception e) {
            log.error("同步短信状态失败: recordId={}, error={}", recordId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 调用服务商发送短信
     */
    private boolean sendSmsToProvider(SmsRecord record) {
        log.debug("调用服务商发送短信: provider={}, phoneNumber={}", record.getProviderType(), record.getPhoneNumber());

        try {
            String provider = record.getProviderType();
            switch (provider) {
                case "aliyun":
                    return sendSmsViaAliyun(record);
                case "tencent":
                    return sendSmsViaTencent(record);
                case "huawei":
                    return sendSmsViaHuawei(record);
                default:
                    log.error("不支持的短信服务商: {}", provider);
                    return false;
            }
        } catch (Exception e) {
            log.error("调用服务商发送短信失败: {}", e.getMessage(), e);
            record.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 使用模板调用服务商发送短信
     */
    private boolean sendSmsToProviderWithTemplate(SmsRecord record, SmsTemplate template, Map<String, Object> templateParams) {
        log.debug("使用模板调用服务商发送短信: provider={}, templateCode={}", record.getProviderType(), template.getTemplateCode());

        try {
            String provider = record.getProviderType();
            switch (provider) {
                case "aliyun":
                    return sendSmsViaAliyunWithTemplate(record, template, templateParams);
                case "tencent":
                    return sendSmsViaTencentWithTemplate(record, template, templateParams);
                case "huawei":
                    return sendSmsViaHuaweiWithTemplate(record, template, templateParams);
                default:
                    log.error("不支持的短信服务商: {}", provider);
                    return false;
            }
        } catch (Exception e) {
            log.error("使用模板调用服务商发送短信失败: {}", e.getMessage(), e);
            record.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 阿里云短信发送
     */
    private boolean sendSmsViaAliyun(SmsRecord record) {
        log.info("通过阿里云发送短信: phoneNumber={}", record.getPhoneNumber());

        try {
            return aliyunSmsProvider.sendSms(record);
        } catch (Exception e) {
            log.error("阿里云短信发送失败: phoneNumber={}, error={}", record.getPhoneNumber(), e.getMessage(), e);
            record.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 阿里云模板短信发送
     */
    private boolean sendSmsViaAliyunWithTemplate(SmsRecord record, SmsTemplate template, Map<String, Object> templateParams) {
        log.info("通过阿里云发送模板短信: phoneNumber={}, templateCode={}", record.getPhoneNumber(), template.getTemplateCode());

        try {
            return aliyunSmsProvider.sendSmsWithTemplate(record, template, templateParams);
        } catch (Exception e) {
            log.error("阿里云模板短信发送失败: phoneNumber={}, templateCode={}, error={}",
                     record.getPhoneNumber(), template.getTemplateCode(), e.getMessage(), e);
            record.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 腾讯云短信发送
     */
    private boolean sendSmsViaTencent(SmsRecord record) {
        log.info("通过腾讯云发送短信: phoneNumber={}", record.getPhoneNumber());

        // TODO: 集成腾讯云短信SDK
        // 这里模拟发送成功
        record.setProviderMessageId("tencent_" + UUID.randomUUID().toString());
        return true;
    }

    /**
     * 腾讯云模板短信发送
     */
    private boolean sendSmsViaTencentWithTemplate(SmsRecord record, SmsTemplate template, Map<String, Object> templateParams) {
        log.info("通过腾讯云发送模板短信: phoneNumber={}, templateCode={}", record.getPhoneNumber(), template.getTemplateCode());

        // TODO: 集成腾讯云短信SDK
        // 这里模拟发送成功
        record.setProviderMessageId("tencent_template_" + UUID.randomUUID().toString());
        return true;
    }

    /**
     * 华为云短信发送
     */
    private boolean sendSmsViaHuawei(SmsRecord record) {
        log.info("通过华为云发送短信: phoneNumber={}", record.getPhoneNumber());

        // TODO: 集成华为云短信SDK
        // 这里模拟发送成功
        record.setProviderMessageId("huawei_" + UUID.randomUUID().toString());
        return true;
    }

    /**
     * 华为云模板短信发送
     */
    private boolean sendSmsViaHuaweiWithTemplate(SmsRecord record, SmsTemplate template, Map<String, Object> templateParams) {
        log.info("通过华为云发送模板短信: phoneNumber={}, templateCode={}", record.getPhoneNumber(), template.getTemplateCode());

        // TODO: 集成华为云短信SDK
        // 这里模拟发送成功
        record.setProviderMessageId("huawei_template_" + UUID.randomUUID().toString());
        return true;
    }

    /**
     * 替换模板内容
     */
    private String replaceTemplateContent(String templateContent, Map<String, Object> templateParams) {
        if (!StringUtils.hasText(templateContent) || templateParams == null || templateParams.isEmpty()) {
            return templateContent;
        }

        String result = templateContent;
        for (Map.Entry<String, Object> entry : templateParams.entrySet()) {
            String placeholder = "${" + entry.getKey() + "}";
            String value = entry.getValue() != null ? entry.getValue().toString() : "";
            result = result.replace(placeholder, value);
        }

        return result;
    }

    /**
     * 记录发送限制
     */
    private void recordSendLimit(String phoneNumber) {
        List<LocalDateTime> sendTimes = sendLimitTracker.computeIfAbsent(phoneNumber, k -> new ArrayList<>());
        sendTimes.add(LocalDateTime.now());

        // 清理过期记录，保持内存使用合理
        LocalDateTime oneDayAgo = LocalDateTime.now().minusDays(1);
        sendTimes.removeIf(time -> time.isBefore(oneDayAgo));
    }
}
