package com.hzwangda.edu.notification.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hzwangda.edu.notification.dto.NotificationMessageResponse;
import com.hzwangda.edu.notification.dto.NotificationSendRequest;
import com.hzwangda.edu.notification.entity.NotificationMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 消息通知映射工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class NotificationMapper {

    private static final Logger logger = LoggerFactory.getLogger(NotificationMapper.class);

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 将消息实体转换为响应DTO
     */
    public NotificationMessageResponse toMessageResponse(NotificationMessage message) {
        if (message == null) {
            return null;
        }

        NotificationMessageResponse response = new NotificationMessageResponse();

        response.setId(message.getId());
        response.setMessageId(message.getMessageId());
        response.setTemplateCode(message.getTemplateCode());
        response.setChannelType(message.getChannelType());
        response.setRecipient(message.getRecipient());
        response.setRecipientName(message.getRecipientName());
        response.setRecipientId(message.getRecipientId());
        response.setMessageTitle(message.getMessageTitle());
        response.setMessageContent(message.getMessageContent());
        response.setStatus(message.getStatus());
        response.setPriority(message.getPriority());
        response.setSendTime(message.getSendTime());
        response.setScheduledTime(message.getScheduledTime());
        response.setDeliveredTime(message.getDeliveredTime());
        response.setReadTime(message.getReadTime());
        response.setRetryCount(message.getRetryCount());
        response.setMaxRetryCount(message.getMaxRetryCount());
        response.setErrorMessage(message.getErrorMessage());
        response.setBusinessType(message.getBusinessType());
        response.setBusinessId(message.getBusinessId());
        response.setSender(message.getSender());
        response.setSenderId(message.getSenderId());
        response.setIsRead(message.getIsRead());
        response.setExpireTime(message.getExpireTime());
        response.setCreateTime(message.getCreateTime());
        response.setUpdateTime(message.getUpdateTime());

        // 反序列化变量
        if (message.getMessageVariables() != null) {
            try {
                Map<String, Object> variables = objectMapper.readValue(
                    message.getMessageVariables(),
                    new TypeReference<Map<String, Object>>() {}
                );
                response.setVariables(variables);
            } catch (JsonProcessingException e) {
                logger.warn("反序列化消息变量失败: {}", e.getMessage());
            }
        }

        // 反序列化附件
        if (message.getAttachments() != null) {
            try {
                List<NotificationMessageResponse.AttachmentInfo> attachments = objectMapper.readValue(
                    message.getAttachments(),
                    new TypeReference<List<NotificationMessageResponse.AttachmentInfo>>() {}
                );
                response.setAttachments(attachments);
            } catch (JsonProcessingException e) {
                logger.warn("反序列化附件信息失败: {}", e.getMessage());
            }
        }

        return response;
    }

    /**
     * 将发送请求转换为消息实体
     */
    public NotificationMessage toMessageEntity(NotificationSendRequest request, String messageId) {
        if (request == null) {
            return null;
        }

        NotificationMessage message = new NotificationMessage();

        message.setMessageId(messageId);
        message.setTemplateCode(request.getTemplateCode());
        message.setChannelType(request.getChannelType());
        message.setMessageTitle(request.getMessageTitle());
        message.setMessageContent(request.getMessageContent());
        message.setPriority(request.getPriority());
        message.setScheduledTime(request.getScheduledTime());
        message.setMaxRetryCount(request.getMaxRetryCount());
        message.setBusinessType(request.getBusinessType());
        message.setBusinessId(request.getBusinessId());
        message.setSender(request.getSender());
        message.setSenderId(request.getSenderId());
        message.setExpireTime(request.getExpireTime());
        message.setStatus("PENDING");
        message.setRetryCount(0);
        message.setIsRead(false);

        // 设置接收人信息（取第一个）
        if (request.getRecipients() != null && !request.getRecipients().isEmpty()) {
            message.setRecipient(request.getRecipients().get(0));
        }

        if (request.getRecipientIds() != null && !request.getRecipientIds().isEmpty()) {
            message.setRecipientId(request.getRecipientIds().get(0));
        }

        if (request.getRecipientNames() != null && !request.getRecipientNames().isEmpty()) {
            message.setRecipientName(request.getRecipientNames().get(0));
        }

        // 序列化变量
        if (request.getVariables() != null) {
            try {
                message.setMessageVariables(objectMapper.writeValueAsString(request.getVariables()));
            } catch (JsonProcessingException e) {
                logger.warn("序列化消息变量失败: {}", e.getMessage());
            }
        }

        // 序列化附件
        if (request.getAttachments() != null) {
            try {
                message.setAttachments(objectMapper.writeValueAsString(request.getAttachments()));
            } catch (JsonProcessingException e) {
                logger.warn("序列化附件信息失败: {}", e.getMessage());
            }
        }

        return message;
    }
}
