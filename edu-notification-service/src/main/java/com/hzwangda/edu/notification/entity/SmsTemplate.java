package com.hzwangda.edu.notification.entity;

import com.hzwangda.edu.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;

/**
 * 短信模板实体
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "notification_sms_template")
public class SmsTemplate extends BaseEntity {

    /**
     * 模板编码
     */
    @Column(name = "template_code", nullable = false, unique = true, length = 100)
    private String templateCode;

    /**
     * 模板名称
     */
    @Column(name = "template_name", nullable = false, length = 200)
    private String templateName;

    /**
     * 模板内容
     */
    @Column(name = "template_content", nullable = false, columnDefinition = "TEXT")
    private String templateContent;

    /**
     * 服务商模板ID（阿里云、腾讯云等）
     */
    @Column(name = "provider_template_id", length = 100)
    private String providerTemplateId;

    /**
     * 服务商类型（aliyun, tencent, huawei）
     */
    @Column(name = "provider_type", length = 50)
    private String providerType;

    /**
     * 模板参数（JSON格式）
     */
    @Column(name = "template_params", columnDefinition = "TEXT")
    private String templateParams;

    /**
     * 签名名称
     */
    @Column(name = "sign_name", length = 100)
    private String signName;

    /**
     * 模板类型（验证码、通知、营销）
     */
    @Column(name = "template_type", length = 50)
    private String templateType;

    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;

    /**
     * 审核状态（PENDING, APPROVED, REJECTED）
     */
    @Column(name = "audit_status", length = 50)
    private String auditStatus;

    /**
     * 审核备注
     */
    @Column(name = "audit_remark", length = 500)
    private String auditRemark;

    /**
     * 使用次数
     */
    @Column(name = "usage_count")
    private Long usageCount = 0L;

    /**
     * 成功次数
     */
    @Column(name = "success_count")
    private Long successCount = 0L;

    /**
     * 失败次数
     */
    @Column(name = "failure_count")
    private Long failureCount = 0L;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
}
