package com.hzwangda.edu.notification.service;

import com.hzwangda.edu.notification.entity.MessageStatusHistory;
import com.hzwangda.edu.notification.entity.NotificationMessage;
import com.hzwangda.edu.notification.enums.MessageStatus;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 消息状态跟踪服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public interface MessageStatusTrackingService {

    /**
     * 记录消息状态变更
     *
     * @param messageId 消息ID
     * @param fromStatus 原状态
     * @param toStatus 新状态
     * @param reason 变更原因
     * @param channelType 渠道类型
     * @return 状态历史记录
     */
    MessageStatusHistory recordStatusChange(String messageId, MessageStatus fromStatus, MessageStatus toStatus,
                                          String reason, String channelType);

    /**
     * 记录带错误信息的状态变更
     *
     * @param messageId 消息ID
     * @param fromStatus 原状态
     * @param toStatus 新状态
     * @param reason 变更原因
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     * @param channelType 渠道类型
     * @param retryCount 重试次数
     * @return 状态历史记录
     */
    MessageStatusHistory recordStatusChangeWithError(String messageId, MessageStatus fromStatus, MessageStatus toStatus,
                                                    String reason, String errorCode, String errorMessage,
                                                    String channelType, Integer retryCount);

    /**
     * 记录手动操作的状态变更
     *
     * @param messageId 消息ID
     * @param fromStatus 原状态
     * @param toStatus 新状态
     * @param reason 变更原因
     * @param operator 操作人
     * @param channelType 渠道类型
     * @return 状态历史记录
     */
    MessageStatusHistory recordManualStatusChange(String messageId, MessageStatus fromStatus, MessageStatus toStatus,
                                                 String reason, String operator, String channelType);

    /**
     * 获取消息状态历史
     *
     * @param messageId 消息ID
     * @return 状态历史列表
     */
    List<MessageStatusHistory> getMessageStatusHistory(String messageId);

    /**
     * 获取消息当前状态
     *
     * @param messageId 消息ID
     * @return 当前状态
     */
    MessageStatus getCurrentStatus(String messageId);

    /**
     * 批量获取消息状态
     *
     * @param messageIds 消息ID列表
     * @return 消息状态映射
     */
    Map<String, MessageStatus> getBatchMessageStatus(List<String> messageIds);

    /**
     * 查询指定状态的消息
     *
     * @param status 消息状态
     * @param channelType 渠道类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 消息列表
     */
    List<NotificationMessage> getMessagesByStatus(MessageStatus status, String channelType,
                                                 LocalDateTime startTime, LocalDateTime endTime,
                                                 int pageNum, int pageSize);

    /**
     * 获取消息发送统计
     *
     * @param channelType 渠道类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    Map<String, Object> getMessageSendingStatistics(String channelType, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取消息状态分布统计
     *
     * @param channelType 渠道类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 状态分布统计
     */
    Map<String, Long> getMessageStatusDistribution(String channelType, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取消息处理时长统计
     *
     * @param channelType 渠道类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 处理时长统计
     */
    Map<String, Object> getMessageProcessingTimeStatistics(String channelType, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取消息重试统计
     *
     * @param channelType 渠道类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 重试统计
     */
    Map<String, Object> getMessageRetryStatistics(String channelType, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取消息发送成功率
     *
     * @param channelType 渠道类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 成功率（百分比）
     */
    double getMessageSuccessRate(String channelType, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取消息发送趋势
     *
     * @param channelType 渠道类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param interval 时间间隔（HOUR, DAY, WEEK, MONTH）
     * @return 发送趋势数据
     */
    List<Map<String, Object>> getMessageSendingTrend(String channelType, LocalDateTime startTime, LocalDateTime endTime, String interval);

    /**
     * 获取实时消息状态监控数据
     *
     * @return 实时监控数据
     */
    Map<String, Object> getRealTimeStatusMonitoring();

    /**
     * 清理历史状态记录
     *
     * @param beforeTime 清理此时间之前的记录
     * @return 清理的记录数量
     */
    long cleanupStatusHistory(LocalDateTime beforeTime);

    /**
     * 导出消息状态报告
     *
     * @param channelType 渠道类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param format 导出格式（JSON, CSV, EXCEL）
     * @return 报告数据
     */
    Object exportStatusReport(String channelType, LocalDateTime startTime, LocalDateTime endTime, String format);

    /**
     * 获取消息状态变更日志
     *
     * @param messageId 消息ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 状态变更日志
     */
    List<Map<String, Object>> getMessageStatusChangeLogs(String messageId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取异常消息列表
     *
     * @param channelType 渠道类型
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 异常消息列表
     */
    List<NotificationMessage> getAbnormalMessages(String channelType, int pageNum, int pageSize);

    /**
     * 获取长时间未处理的消息
     *
     * @param timeoutMinutes 超时分钟数
     * @param channelType 渠道类型
     * @return 超时消息列表
     */
    List<NotificationMessage> getTimeoutMessages(int timeoutMinutes, String channelType);

    /**
     * 更新消息状态（带状态历史记录）
     *
     * @param message 消息对象
     * @param newStatus 新状态
     * @param reason 变更原因
     * @return 更新结果
     */
    boolean updateMessageStatus(NotificationMessage message, MessageStatus newStatus, String reason);

    /**
     * 批量更新消息状态
     *
     * @param messageIds 消息ID列表
     * @param newStatus 新状态
     * @param reason 变更原因
     * @param operator 操作人
     * @return 更新结果统计
     */
    Map<String, Object> batchUpdateMessageStatus(List<String> messageIds, MessageStatus newStatus, String reason, String operator);
}
