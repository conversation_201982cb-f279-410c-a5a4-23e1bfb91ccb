package com.hzwangda.edu.notification.repository;

import com.hzwangda.edu.notification.entity.NotificationMessage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 消息记录Repository接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
public interface NotificationMessageRepository extends JpaRepository<NotificationMessage, Long>, JpaSpecificationExecutor<NotificationMessage> {

    /**
     * 根据消息ID查询消息
     */
    Optional<NotificationMessage> findByMessageIdAndDeletedFalse(String messageId);

    /**
     * 根据外部消息ID查询消息
     */
    Optional<NotificationMessage> findByExternalId(String externalId);

    /**
     * 根据接收人查询消息
     */
    Page<NotificationMessage> findByRecipientAndDeletedFalseOrderByCreateTimeDesc(String recipient, Pageable pageable);

    /**
     * 根据接收人ID查询消息
     */
    Page<NotificationMessage> findByRecipientIdAndDeletedFalseOrderByCreateTimeDesc(Long recipientId, Pageable pageable);

    /**
     * 根据状态查询消息
     */
    Page<NotificationMessage> findByStatusAndDeletedFalse(String status, Pageable pageable);

    /**
     * 根据渠道类型查询消息
     */
    Page<NotificationMessage> findByChannelTypeAndDeletedFalse(String channelType, Pageable pageable);

    /**
     * 根据模板编码查询消息
     */
    Page<NotificationMessage> findByTemplateCodeAndDeletedFalse(String templateCode, Pageable pageable);

    /**
     * 根据业务类型和业务ID查询消息
     */
    List<NotificationMessage> findByBusinessTypeAndBusinessIdAndDeletedFalse(String businessType, String businessId);

    /**
     * 查询待发送的消息
     */
    @Query("SELECT m FROM NotificationMessage m WHERE m.status = 'PENDING' AND m.deleted = false " +
           "AND (m.scheduledTime IS NULL OR m.scheduledTime <= :currentTime) ORDER BY m.priority DESC, m.createTime ASC")
    List<NotificationMessage> findPendingMessages(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查询需要重试的消息
     */
    @Query("SELECT m FROM NotificationMessage m WHERE m.status = 'FAILED' AND m.retryCount < m.maxRetryCount " +
           "AND m.deleted = false ORDER BY m.priority DESC, m.createTime ASC")
    List<NotificationMessage> findRetryableMessages();

    /**
     * 查询过期的消息
     */
    @Query("SELECT m FROM NotificationMessage m WHERE m.expireTime < :currentTime AND m.status IN ('PENDING', 'SENDING') " +
           "AND m.deleted = false")
    List<NotificationMessage> findExpiredMessages(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 查询用户的未读消息
     */
    @Query("SELECT m FROM NotificationMessage m WHERE m.recipientId = :recipientId AND m.isRead = false " +
           "AND m.status IN ('SENT', 'DELIVERED') AND m.deleted = false ORDER BY m.createTime DESC")
    List<NotificationMessage> findUnreadMessages(@Param("recipientId") Long recipientId);

    /**
     * 查询用户的未读消息数量
     */
    @Query("SELECT COUNT(m) FROM NotificationMessage m WHERE m.recipientId = :recipientId AND m.isRead = false " +
           "AND m.status IN ('SENT', 'DELIVERED') AND m.deleted = false")
    Long countUnreadMessages(@Param("recipientId") Long recipientId);

    /**
     * 统计各状态下的消息数量
     */
    @Query("SELECT m.status, COUNT(m) FROM NotificationMessage m WHERE m.deleted = false GROUP BY m.status")
    List<Object[]> countByStatus();

    /**
     * 统计指定状态的消息数量
     */
    Long countByStatus(String status);

    /**
     * 统计各渠道下的消息数量
     */
    @Query("SELECT m.channelType, COUNT(m) FROM NotificationMessage m WHERE m.deleted = false GROUP BY m.channelType")
    List<Object[]> countByChannelType();

    /**
     * 统计指定时间范围内的消息数量
     */
    @Query("SELECT COUNT(m) FROM NotificationMessage m WHERE m.createTime BETWEEN :startTime AND :endTime AND m.deleted = false")
    Long countByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内已发送的消息数量
     */
    @Query("SELECT COUNT(m) FROM NotificationMessage m WHERE m.sendTime BETWEEN :startTime AND :endTime " +
           "AND m.status IN ('SENT', 'DELIVERED', 'READ') AND m.deleted = false")
    Long countSentByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定时间范围内失败的消息数量
     */
    @Query("SELECT COUNT(m) FROM NotificationMessage m WHERE m.createTime BETWEEN :startTime AND :endTime " +
           "AND m.status = 'FAILED' AND m.deleted = false")
    Long countFailedByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询最活跃的接收人TOP10
     */
    @Query("SELECT m.recipientId, m.recipient, m.recipientName, COUNT(m) as messageCount " +
           "FROM NotificationMessage m WHERE m.createTime BETWEEN :startTime AND :endTime AND m.deleted = false " +
           "GROUP BY m.recipientId, m.recipient, m.recipientName ORDER BY messageCount DESC")
    List<Object[]> findTopRecipients(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, Pageable pageable);

    /**
     * 按日期统计消息数量
     */
    @Query("SELECT DATE(m.createTime), COUNT(m) FROM NotificationMessage m WHERE m.createTime BETWEEN :startTime AND :endTime " +
           "AND m.deleted = false GROUP BY DATE(m.createTime) ORDER BY DATE(m.createTime)")
    List<Object[]> countByDate(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 按小时统计消息数量
     */
    @Query("SELECT HOUR(m.createTime), COUNT(m) FROM NotificationMessage m WHERE m.createTime BETWEEN :startTime AND :endTime " +
           "AND m.deleted = false GROUP BY HOUR(m.createTime) ORDER BY HOUR(m.createTime)")
    List<Object[]> countByHour(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定业务类型的消息
     */
    @Query("SELECT m FROM NotificationMessage m WHERE m.businessType = :businessType AND m.deleted = false ORDER BY m.createTime DESC")
    List<NotificationMessage> findByBusinessType(@Param("businessType") String businessType);

    /**
     * 查询高优先级待发送消息
     */
    @Query("SELECT m FROM NotificationMessage m WHERE m.status = 'PENDING' AND m.priority >= :priority " +
           "AND m.deleted = false AND (m.scheduledTime IS NULL OR m.scheduledTime <= :currentTime) " +
           "ORDER BY m.priority DESC, m.createTime ASC")
    List<NotificationMessage> findHighPriorityPendingMessages(@Param("priority") Integer priority,
                                                              @Param("currentTime") LocalDateTime currentTime);

    /**
     * 批量更新消息状态
     */
    @Query("UPDATE NotificationMessage m SET m.status = :status WHERE m.id IN :ids")
    void batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") String status);

    /**
     * 批量标记消息为已读
     */
    @Query("UPDATE NotificationMessage m SET m.isRead = true, m.readTime = :readTime WHERE m.id IN :ids")
    void batchMarkAsRead(@Param("ids") List<Long> ids, @Param("readTime") LocalDateTime readTime);
}
