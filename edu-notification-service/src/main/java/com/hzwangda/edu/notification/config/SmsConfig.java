package com.hzwangda.edu.notification.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 短信配置类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@Component
@ConfigurationProperties(prefix = "hky.notification.sms")
public class SmsConfig {

    /**
     * 是否启用短信功能
     */
    private boolean enabled = false;

    /**
     * 短信服务提供商类型（aliyun, tencent, huawei）
     */
    private String provider = "aliyun";

    /**
     * 阿里云短信配置
     */
    private AliyunSmsConfig aliyun = new AliyunSmsConfig();

    /**
     * 腾讯云短信配置
     */
    private TencentSmsConfig tencent = new TencentSmsConfig();

    /**
     * 华为云短信配置
     */
    private HuaweiSmsConfig huawei = new HuaweiSmsConfig();

    /**
     * 短信发送限制配置
     */
    private SmsLimitConfig limit = new SmsLimitConfig();

    /**
     * 阿里云短信配置
     */
    @Data
    public static class AliyunSmsConfig {
        private String accessKeyId;
        private String accessKeySecret;
        private String signName;
        private String endpoint = "dysmsapi.aliyuncs.com";
        private String regionId = "cn-hangzhou";
    }

    /**
     * 腾讯云短信配置
     */
    @Data
    public static class TencentSmsConfig {
        private String secretId;
        private String secretKey;
        private String sdkAppId;
        private String signName;
        private String endpoint = "sms.tencentcloudapi.com";
        private String region = "ap-beijing";
    }

    /**
     * 华为云短信配置
     */
    @Data
    public static class HuaweiSmsConfig {
        private String accessKeyId;
        private String secretAccessKey;
        private String sender;
        private String endpoint = "https://smsapi.cn-north-4.myhuaweicloud.com:443";
        private String region = "cn-north-4";
    }

    /**
     * 短信发送限制配置
     */
    @Data
    public static class SmsLimitConfig {
        /**
         * 每分钟最大发送数量
         */
        private int maxPerMinute = 10;

        /**
         * 每小时最大发送数量
         */
        private int maxPerHour = 100;

        /**
         * 每天最大发送数量
         */
        private int maxPerDay = 1000;

        /**
         * 同一手机号每分钟最大发送数量
         */
        private int maxPerPhonePerMinute = 1;

        /**
         * 同一手机号每小时最大发送数量
         */
        private int maxPerPhonePerHour = 5;

        /**
         * 同一手机号每天最大发送数量
         */
        private int maxPerPhonePerDay = 20;
    }
}
