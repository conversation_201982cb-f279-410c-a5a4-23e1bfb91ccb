package com.hzwangda.edu.notification.service;

import com.hzwangda.edu.notification.entity.NotificationMessage;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 消息重试服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public interface MessageRetryService {

    /**
     * 添加消息到重试队列
     *
     * @param message 消息对象
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     * @return 是否添加成功
     */
    boolean addToRetryQueue(NotificationMessage message, String errorCode, String errorMessage);

    /**
     * 处理重试队列中的消息
     *
     * @return 处理结果统计
     */
    Map<String, Object> processRetryQueue();

    /**
     * 重试指定消息
     *
     * @param messageId 消息ID
     * @return 重试结果
     */
    boolean retryMessage(String messageId);

    /**
     * 批量重试消息
     *
     * @param messageIds 消息ID列表
     * @return 重试结果统计
     */
    Map<String, Object> batchRetryMessages(List<String> messageIds);

    /**
     * 计算下次重试时间
     *
     * @param channelType 渠道类型
     * @param retryCount 当前重试次数
     * @return 下次重试时间
     */
    LocalDateTime calculateNextRetryTime(String channelType, int retryCount);

    /**
     * 检查是否可以重试
     *
     * @param message 消息对象
     * @param errorCode 错误码
     * @return 是否可以重试
     */
    boolean canRetry(NotificationMessage message, String errorCode);

    /**
     * 移动消息到死信队列
     *
     * @param message 消息对象
     * @param reason 移动原因
     * @return 是否移动成功
     */
    boolean moveToDeadLetterQueue(NotificationMessage message, String reason);

    /**
     * 获取死信队列中的消息
     *
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 死信消息列表
     */
    List<NotificationMessage> getDeadLetterMessages(int pageNum, int pageSize);

    /**
     * 从死信队列恢复消息
     *
     * @param messageId 消息ID
     * @return 恢复结果
     */
    boolean recoverFromDeadLetter(String messageId);

    /**
     * 清理死信队列
     *
     * @param beforeTime 清理此时间之前的消息
     * @return 清理的消息数量
     */
    long cleanupDeadLetterQueue(LocalDateTime beforeTime);

    /**
     * 获取重试统计信息
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    Map<String, Object> getRetryStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取重试队列状态
     *
     * @return 队列状态信息
     */
    Map<String, Object> getRetryQueueStatus();

    /**
     * 暂停重试处理
     */
    void pauseRetryProcessing();

    /**
     * 恢复重试处理
     */
    void resumeRetryProcessing();

    /**
     * 获取重试处理状态
     *
     * @return 是否正在处理
     */
    boolean isRetryProcessingActive();

    /**
     * 更新重试策略
     *
     * @param channelType 渠道类型
     * @param strategyConfig 策略配置
     * @return 更新结果
     */
    boolean updateRetryStrategy(String channelType, Map<String, Object> strategyConfig);

    /**
     * 获取重试策略配置
     *
     * @param channelType 渠道类型
     * @return 策略配置
     */
    Map<String, Object> getRetryStrategy(String channelType);

    /**
     * 获取所有重试策略配置
     *
     * @return 所有策略配置
     */
    Map<String, Map<String, Object>> getAllRetryStrategies();

    /**
     * 重置重试策略为默认值
     *
     * @param channelType 渠道类型
     * @return 重置结果
     */
    boolean resetRetryStrategy(String channelType);

    /**
     * 获取重试监控数据
     *
     * @return 监控数据
     */
    Map<String, Object> getRetryMonitoringData();

    /**
     * 导出重试日志
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param channelType 渠道类型
     * @return 日志数据
     */
    List<Map<String, Object>> exportRetryLogs(LocalDateTime startTime, LocalDateTime endTime, String channelType);
}
