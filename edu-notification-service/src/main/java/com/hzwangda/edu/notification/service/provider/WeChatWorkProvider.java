package com.hzwangda.edu.notification.service.provider;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hzwangda.edu.common.exception.BusinessException;
import com.hzwangda.edu.common.result.ResultCode;
import com.hzwangda.edu.notification.config.WeChatConfig;
import com.hzwangda.edu.notification.entity.WeChatRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 企业微信API提供商
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class WeChatWorkProvider {

    @Autowired
    private WeChatConfig weChatConfig;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    // Access Token缓存
    private final Map<String, String> accessTokenCache = new ConcurrentHashMap<>();
    private final Map<String, LocalDateTime> tokenExpireTime = new ConcurrentHashMap<>();

    // 企业微信API地址
    private static final String WORK_API_BASE = "https://qyapi.weixin.qq.com/cgi-bin";
    private static final String GET_TOKEN_URL = WORK_API_BASE + "/gettoken";
    private static final String SEND_MESSAGE_URL = WORK_API_BASE + "/message/send";
    private static final String GET_USER_URL = WORK_API_BASE + "/user/get";

    @PostConstruct
    public void init() {
        log.info("企业微信API提供商初始化完成");
    }

    /**
     * 获取Access Token
     */
    public String getAccessToken() {
        try {
            WeChatConfig.WeChatWorkConfig workConfig = weChatConfig.getWork();
            String cacheKey = workConfig.getCorpId() + "_" + workConfig.getCorpSecret();

            // 检查缓存
            String cachedToken = accessTokenCache.get(cacheKey);
            LocalDateTime expireTime = tokenExpireTime.get(cacheKey);

            if (StringUtils.hasText(cachedToken) && expireTime != null &&
                LocalDateTime.now().isBefore(expireTime.minusMinutes(5))) {
                return cachedToken;
            }

            // 获取新Token
            String url = GET_TOKEN_URL + "?corpid=" + workConfig.getCorpId() +
                        "&corpsecret=" + workConfig.getCorpSecret();

            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                Map<String, Object> result = objectMapper.readValue(response.getBody(), Map.class);

                if ((Integer) result.get("errcode") == 0) {
                    String accessToken = (String) result.get("access_token");
                    Integer expiresIn = (Integer) result.get("expires_in");

                    // 缓存Token
                    accessTokenCache.put(cacheKey, accessToken);
                    tokenExpireTime.put(cacheKey, LocalDateTime.now().plusSeconds(expiresIn));

                    log.info("企业微信Access Token获取成功");
                    return accessToken;
                } else {
                    log.error("企业微信Access Token获取失败: {}", result.get("errmsg"));
                    throw new BusinessException(ResultCode.EXTERNAL_SERVICE_ERROR,
                            "获取企业微信Access Token失败: " + result.get("errmsg"));
                }
            }

            throw new BusinessException(ResultCode.EXTERNAL_SERVICE_ERROR, "企业微信API调用失败");

        } catch (Exception e) {
            log.error("获取企业微信Access Token异常: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.EXTERNAL_SERVICE_ERROR,
                    "获取企业微信Access Token异常: " + e.getMessage());
        }
    }

    /**
     * 发送文本消息
     */
    public boolean sendTextMessage(WeChatRecord record, String content) {
        try {
            String accessToken = getAccessToken();
            WeChatConfig.WeChatWorkConfig workConfig = weChatConfig.getWork();

            Map<String, Object> message = new HashMap<>();
            message.put("touser", record.getToUser());
            message.put("msgtype", "text");
            message.put("agentid", workConfig.getAgentId());

            Map<String, Object> textContent = new HashMap<>();
            textContent.put("content", content);
            message.put("text", textContent);

            return sendMessage(accessToken, message, record);

        } catch (Exception e) {
            log.error("发送企业微信文本消息失败: {}", e.getMessage(), e);
            record.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 发送模板消息
     */
    public boolean sendTemplateMessage(WeChatRecord record, String templateId, Map<String, Object> templateData) {
        try {
            String accessToken = getAccessToken();
            WeChatConfig.WeChatWorkConfig workConfig = weChatConfig.getWork();

            Map<String, Object> message = new HashMap<>();
            message.put("touser", record.getToUser());
            message.put("msgtype", "template_card");
            message.put("agentid", workConfig.getAgentId());

            Map<String, Object> templateCard = new HashMap<>();
            templateCard.put("card_type", "text_notice");
            templateCard.put("source", templateData.get("source"));
            templateCard.put("main_title", templateData.get("title"));
            templateCard.put("emphasis_content", templateData.get("emphasis"));
            templateCard.put("sub_title_text", templateData.get("subtitle"));

            message.put("template_card", templateCard);

            return sendMessage(accessToken, message, record);

        } catch (Exception e) {
            log.error("发送企业微信模板消息失败: {}", e.getMessage(), e);
            record.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 发送卡片消息
     */
    public boolean sendTextCardMessage(WeChatRecord record, String title, String description, String url, String btnText) {
        try {
            String accessToken = getAccessToken();
            WeChatConfig.WeChatWorkConfig workConfig = weChatConfig.getWork();

            Map<String, Object> message = new HashMap<>();
            message.put("touser", record.getToUser());
            message.put("msgtype", "textcard");
            message.put("agentid", workConfig.getAgentId());

            Map<String, Object> textCard = new HashMap<>();
            textCard.put("title", title);
            textCard.put("description", description);
            textCard.put("url", url);
            textCard.put("btntxt", btnText);

            message.put("textcard", textCard);

            return sendMessage(accessToken, message, record);

        } catch (Exception e) {
            log.error("发送企业微信卡片消息失败: {}", e.getMessage(), e);
            record.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 发送Markdown消息
     */
    public boolean sendMarkdownMessage(WeChatRecord record, String content) {
        try {
            String accessToken = getAccessToken();
            WeChatConfig.WeChatWorkConfig workConfig = weChatConfig.getWork();

            Map<String, Object> message = new HashMap<>();
            message.put("touser", record.getToUser());
            message.put("msgtype", "markdown");
            message.put("agentid", workConfig.getAgentId());

            Map<String, Object> markdown = new HashMap<>();
            markdown.put("content", content);
            message.put("markdown", markdown);

            return sendMessage(accessToken, message, record);

        } catch (Exception e) {
            log.error("发送企业微信Markdown消息失败: {}", e.getMessage(), e);
            record.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 发送图文消息
     */
    public boolean sendNewsMessage(WeChatRecord record, List<Map<String, Object>> articles) {
        try {
            String accessToken = getAccessToken();
            WeChatConfig.WeChatWorkConfig workConfig = weChatConfig.getWork();

            Map<String, Object> message = new HashMap<>();
            message.put("touser", record.getToUser());
            message.put("msgtype", "news");
            message.put("agentid", workConfig.getAgentId());

            Map<String, Object> news = new HashMap<>();
            news.put("articles", articles);
            message.put("news", news);

            return sendMessage(accessToken, message, record);

        } catch (Exception e) {
            log.error("发送企业微信图文消息失败: {}", e.getMessage(), e);
            record.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 获取用户信息
     */
    public Map<String, Object> getUserInfo(String userId) {
        try {
            String accessToken = getAccessToken();
            String url = GET_USER_URL + "?access_token=" + accessToken + "&userid=" + userId;

            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                Map<String, Object> result = objectMapper.readValue(response.getBody(), Map.class);

                if ((Integer) result.get("errcode") == 0) {
                    return result;
                } else {
                    log.error("获取企业微信用户信息失败: {}", result.get("errmsg"));
                }
            }

            return null;

        } catch (Exception e) {
            log.error("获取企业微信用户信息异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查服务可用性
     */
    public boolean isAvailable() {
        try {
            WeChatConfig.WeChatWorkConfig workConfig = weChatConfig.getWork();
            return StringUtils.hasText(workConfig.getCorpId()) &&
                   StringUtils.hasText(workConfig.getCorpSecret()) &&
                   workConfig.getAgentId() != null;
        } catch (Exception e) {
            log.error("检查企业微信服务可用性失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送消息的通用方法
     */
    private boolean sendMessage(String accessToken, Map<String, Object> message, WeChatRecord record) {
        try {
            String url = SEND_MESSAGE_URL + "?access_token=" + accessToken;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            String jsonBody = objectMapper.writeValueAsString(message);
            HttpEntity<String> entity = new HttpEntity<>(jsonBody, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                Map<String, Object> result = objectMapper.readValue(response.getBody(), Map.class);

                if ((Integer) result.get("errcode") == 0) {
                    record.setMessageId((String) result.get("msgid"));
                    log.info("企业微信消息发送成功: toUser={}, msgid={}", record.getToUser(), result.get("msgid"));
                    return true;
                } else {
                    log.error("企业微信消息发送失败: toUser={}, errcode={}, errmsg={}",
                             record.getToUser(), result.get("errcode"), result.get("errmsg"));
                    record.setErrorCode(String.valueOf(result.get("errcode")));
                    record.setErrorMessage((String) result.get("errmsg"));
                    return false;
                }
            }

            return false;

        } catch (Exception e) {
            log.error("企业微信消息发送异常: toUser={}, error={}", record.getToUser(), e.getMessage(), e);
            record.setErrorMessage(e.getMessage());
            return false;
        }
    }
}
