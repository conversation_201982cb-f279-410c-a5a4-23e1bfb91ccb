package com.hzwangda.edu.notification.entity;

import com.hzwangda.edu.common.entity.BaseEntity;
import com.hzwangda.edu.notification.enums.MessageStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 消息状态历史实体
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "notification_message_status_history")
public class MessageStatusHistory extends BaseEntity {

    /**
     * 消息ID
     */
    @Column(name = "message_id", nullable = false, length = 64)
    private String messageId;

    /**
     * 原状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "from_status", length = 50)
    private MessageStatus fromStatus;

    /**
     * 新状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "to_status", nullable = false, length = 50)
    private MessageStatus toStatus;

    /**
     * 状态变更时间
     */
    @Column(name = "status_time", nullable = false)
    private LocalDateTime statusTime;

    /**
     * 变更原因
     */
    @Column(name = "reason", length = 500)
    private String reason;

    /**
     * 错误码
     */
    @Column(name = "error_code", length = 50)
    private String errorCode;

    /**
     * 错误信息
     */
    @Column(name = "error_message", length = 1000)
    private String errorMessage;

    /**
     * 重试次数（状态变更时的重试次数）
     */
    @Column(name = "retry_count")
    private Integer retryCount;

    /**
     * 处理耗时（毫秒）
     */
    @Column(name = "processing_time_ms")
    private Long processingTimeMs;

    /**
     * 渠道类型
     */
    @Column(name = "channel_type", length = 50)
    private String channelType;

    /**
     * 操作人（系统操作为空）
     */
    @Column(name = "operator", length = 100)
    private String operator;

    /**
     * 操作类型（AUTO, MANUAL, SYSTEM）
     */
    @Column(name = "operation_type", length = 20)
    private String operationType = "AUTO";

    /**
     * 客户端IP
     */
    @Column(name = "client_ip", length = 50)
    private String clientIp;

    /**
     * 用户代理
     */
    @Column(name = "user_agent", length = 500)
    private String userAgent;

    /**
     * 扩展信息（JSON格式）
     */
    @Column(name = "extra_info", columnDefinition = "TEXT")
    private String extraInfo;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 创建状态历史记录的静态方法
     */
    public static MessageStatusHistory create(String messageId, MessageStatus fromStatus, MessageStatus toStatus,
                                            String reason, String channelType) {
        return MessageStatusHistory.builder()
                .messageId(messageId)
                .fromStatus(fromStatus)
                .toStatus(toStatus)
                .statusTime(LocalDateTime.now())
                .reason(reason)
                .channelType(channelType)
                .operationType("AUTO")
                .build();
    }

    /**
     * 创建带错误信息的状态历史记录
     */
    public static MessageStatusHistory createWithError(String messageId, MessageStatus fromStatus, MessageStatus toStatus,
                                                      String reason, String errorCode, String errorMessage,
                                                      String channelType, Integer retryCount) {
        return MessageStatusHistory.builder()
                .messageId(messageId)
                .fromStatus(fromStatus)
                .toStatus(toStatus)
                .statusTime(LocalDateTime.now())
                .reason(reason)
                .errorCode(errorCode)
                .errorMessage(errorMessage)
                .channelType(channelType)
                .retryCount(retryCount)
                .operationType("AUTO")
                .build();
    }

    /**
     * 创建手动操作的状态历史记录
     */
    public static MessageStatusHistory createManual(String messageId, MessageStatus fromStatus, MessageStatus toStatus,
                                                   String reason, String operator, String channelType) {
        return MessageStatusHistory.builder()
                .messageId(messageId)
                .fromStatus(fromStatus)
                .toStatus(toStatus)
                .statusTime(LocalDateTime.now())
                .reason(reason)
                .operator(operator)
                .channelType(channelType)
                .operationType("MANUAL")
                .build();
    }

    /**
     * 设置处理耗时
     */
    public void setProcessingTime(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime != null && endTime != null) {
            this.processingTimeMs = java.time.Duration.between(startTime, endTime).toMillis();
        }
    }

    /**
     * 获取状态变更描述
     */
    public String getStatusChangeDescription() {
        StringBuilder sb = new StringBuilder();

        if (fromStatus != null) {
            sb.append(fromStatus.getDescription());
            sb.append(" -> ");
        }

        sb.append(toStatus.getDescription());

        if (reason != null) {
            sb.append(" (").append(reason).append(")");
        }

        return sb.toString();
    }

    /**
     * 判断是否为错误状态变更
     */
    public boolean isErrorStatusChange() {
        return toStatus.isFailureStatus() && (errorCode != null || errorMessage != null);
    }

    /**
     * 判断是否为成功状态变更
     */
    public boolean isSuccessStatusChange() {
        return toStatus.isSuccessStatus();
    }

    /**
     * 判断是否为重试状态变更
     */
    public boolean isRetryStatusChange() {
        return toStatus == MessageStatus.RETRYING ||
               (fromStatus != null && fromStatus == MessageStatus.RETRYING);
    }
}
