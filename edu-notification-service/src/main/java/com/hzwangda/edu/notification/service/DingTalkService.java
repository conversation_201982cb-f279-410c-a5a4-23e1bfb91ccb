package com.hzwangda.edu.notification.service;

import com.hzwangda.edu.notification.entity.DingTalkRecord;
import com.hzwangda.edu.notification.entity.DingTalkUser;

import java.util.List;
import java.util.Map;

/**
 * 钉钉服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public interface DingTalkService {

    // ==================== 消息发送 ====================

    /**
     * 发送文本消息
     *
     * @param toUser 接收者
     * @param content 消息内容
     * @param dingTalkType 钉钉类型（robot/work）
     * @return 发送记录
     */
    DingTalkRecord sendTextMessage(String toUser, String content, String dingTalkType);

    /**
     * 发送Markdown消息
     *
     * @param toUser 接收者
     * @param title 标题
     * @param content Markdown内容
     * @param dingTalkType 钉钉类型
     * @return 发送记录
     */
    DingTalkRecord sendMarkdownMessage(String toUser, String title, String content, String dingTalkType);

    /**
     * 发送ActionCard消息
     *
     * @param toUser 接收者
     * @param title 标题
     * @param content 内容
     * @param actionUrl 跳转链接
     * @param actionTitle 按钮标题
     * @param dingTalkType 钉钉类型
     * @return 发送记录
     */
    DingTalkRecord sendActionCardMessage(String toUser, String title, String content,
                                       String actionUrl, String actionTitle, String dingTalkType);

    /**
     * 发送链接消息
     *
     * @param toUser 接收者
     * @param title 标题
     * @param content 内容
     * @param messageUrl 跳转链接
     * @param picUrl 图片链接
     * @param dingTalkType 钉钉类型
     * @return 发送记录
     */
    DingTalkRecord sendLinkMessage(String toUser, String title, String content,
                                 String messageUrl, String picUrl, String dingTalkType);

    /**
     * 发送群消息
     *
     * @param chatId 群聊ID
     * @param content 消息内容
     * @param msgType 消息类型
     * @param dingTalkType 钉钉类型
     * @return 发送记录
     */
    DingTalkRecord sendGroupMessage(String chatId, String content, String msgType, String dingTalkType);

    /**
     * 批量发送消息
     *
     * @param toUsers 接收者列表
     * @param content 消息内容
     * @param msgType 消息类型
     * @param dingTalkType 钉钉类型
     * @return 发送记录列表
     */
    List<DingTalkRecord> sendBatchMessage(List<String> toUsers, String content, String msgType, String dingTalkType);

    // ==================== 用户管理 ====================

    /**
     * 获取钉钉用户信息
     *
     * @param userId 用户ID
     * @param dingTalkType 钉钉类型
     * @return 用户信息
     */
    DingTalkUser getDingTalkUserInfo(String userId, String dingTalkType);

    /**
     * 绑定钉钉用户
     *
     * @param systemUserId 系统用户ID
     * @param dingTalkUser 钉钉用户信息
     * @return 绑定结果
     */
    boolean bindDingTalkUser(String systemUserId, DingTalkUser dingTalkUser);

    /**
     * 解绑钉钉用户
     *
     * @param systemUserId 系统用户ID
     * @param dingTalkType 钉钉类型
     * @return 解绑结果
     */
    boolean unbindDingTalkUser(String systemUserId, String dingTalkType);

    /**
     * 根据系统用户ID获取钉钉用户
     *
     * @param systemUserId 系统用户ID
     * @param dingTalkType 钉钉类型
     * @return 钉钉用户信息
     */
    DingTalkUser getDingTalkUserBySystemUserId(String systemUserId, String dingTalkType);

    // ==================== 记录管理 ====================

    /**
     * 获取发送记录
     *
     * @param recordId 记录ID
     * @return 发送记录
     */
    DingTalkRecord getDingTalkRecord(String recordId);

    /**
     * 获取发送记录列表
     *
     * @param toUser 接收者
     * @param dingTalkType 钉钉类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发送记录列表
     */
    List<DingTalkRecord> getDingTalkRecords(String toUser, String dingTalkType, String startTime, String endTime);

    /**
     * 重试发送失败的消息
     *
     * @param recordId 记录ID
     * @return 重试结果
     */
    boolean retryDingTalkMessage(String recordId);

    // ==================== 统计和管理 ====================

    /**
     * 获取钉钉发送统计
     *
     * @param dingTalkType 钉钉类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    Map<String, Object> getDingTalkStatistics(String dingTalkType, String startTime, String endTime);

    /**
     * 检查发送限制
     *
     * @param toUser 接收者
     * @param dingTalkType 钉钉类型
     * @return 是否允许发送
     */
    boolean checkSendLimit(String toUser, String dingTalkType);

    /**
     * 获取Access Token
     *
     * @param dingTalkType 钉钉类型
     * @return Access Token
     */
    String getAccessToken(String dingTalkType);

    /**
     * 刷新Access Token
     *
     * @param dingTalkType 钉钉类型
     * @return 新的Access Token
     */
    String refreshAccessToken(String dingTalkType);

    /**
     * 处理钉钉回调
     *
     * @param callbackData 回调数据
     * @param dingTalkType 钉钉类型
     * @return 处理结果
     */
    String handleDingTalkCallback(Map<String, Object> callbackData, String dingTalkType);

    /**
     * 验证钉钉签名
     *
     * @param signature 签名
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param token 验证Token
     * @return 验证结果
     */
    boolean verifySignature(String signature, String timestamp, String nonce, String token);

    // ==================== 高级功能 ====================

    /**
     * 创建群聊
     *
     * @param name 群聊名称
     * @param owner 群主
     * @param userList 成员列表
     * @return 群聊ID
     */
    String createChatGroup(String name, String owner, List<String> userList);

    /**
     * 更新群聊
     *
     * @param chatId 群聊ID
     * @param name 群聊名称
     * @param owner 群主
     * @param addUserList 新增成员
     * @param delUserList 删除成员
     * @return 更新结果
     */
    boolean updateChatGroup(String chatId, String name, String owner,
                          List<String> addUserList, List<String> delUserList);

    /**
     * 获取部门用户列表
     *
     * @param deptId 部门ID
     * @param fetchChild 是否获取子部门
     * @return 用户列表
     */
    List<DingTalkUser> getDepartmentUsers(String deptId, boolean fetchChild);

    /**
     * 发送工作通知
     *
     * @param agentId 应用ID
     * @param userList 用户列表
     * @param deptList 部门列表
     * @param content 消息内容
     * @param msgType 消息类型
     * @return 发送记录
     */
    DingTalkRecord sendWorkNotification(String agentId, List<String> userList,
                                      List<String> deptList, String content, String msgType);

    /**
     * 查询工作通知发送结果
     *
     * @param taskId 任务ID
     * @return 发送结果
     */
    Map<String, Object> getWorkNotificationResult(String taskId);
}
