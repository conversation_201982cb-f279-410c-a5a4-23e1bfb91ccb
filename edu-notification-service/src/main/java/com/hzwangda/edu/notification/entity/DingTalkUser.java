package com.hzwangda.edu.notification.entity;

import com.hzwangda.edu.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;

/**
 * 钉钉用户实体
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "notification_dingtalk_user")
public class DingTalkUser extends BaseEntity {

    /**
     * 系统用户ID
     */
    @Column(name = "user_id", nullable = false, length = 64)
    private String userId;

    /**
     * 钉钉用户ID
     */
    @Column(name = "dingtalk_user_id", nullable = false, length = 100)
    private String dingtalkUserId;

    /**
     * 钉钉UnionID
     */
    @Column(name = "union_id", length = 100)
    private String unionId;

    /**
     * 用户名
     */
    @Column(name = "name", length = 100)
    private String name;

    /**
     * 头像URL
     */
    @Column(name = "avatar", length = 500)
    private String avatar;

    /**
     * 手机号码
     */
    @Column(name = "mobile", length = 20)
    private String mobile;

    /**
     * 邮箱
     */
    @Column(name = "email", length = 100)
    private String email;

    /**
     * 工号
     */
    @Column(name = "job_number", length = 50)
    private String jobNumber;

    /**
     * 职位
     */
    @Column(name = "title", length = 100)
    private String title;

    /**
     * 工作地点
     */
    @Column(name = "work_place", length = 200)
    private String workPlace;

    /**
     * 部门ID列表（JSON格式）
     */
    @Column(name = "dept_id_list", columnDefinition = "TEXT")
    private String deptIdList;

    /**
     * 部门名称列表（JSON格式）
     */
    @Column(name = "dept_name_list", columnDefinition = "TEXT")
    private String deptNameList;

    /**
     * 主部门ID
     */
    @Column(name = "main_dept_id")
    private Long mainDeptId;

    /**
     * 是否为管理员
     */
    @Column(name = "is_admin")
    private Boolean isAdmin = false;

    /**
     * 是否为老板
     */
    @Column(name = "is_boss")
    private Boolean isBoss = false;

    /**
     * 是否为部门主管
     */
    @Column(name = "is_leader")
    private Boolean isLeader = false;

    /**
     * 是否隐藏手机号
     */
    @Column(name = "hide_mobile")
    private Boolean hideMobile = false;

    /**
     * 是否激活
     */
    @Column(name = "active")
    private Boolean active = true;

    /**
     * 员工类型（1-正式员工，2-兼职员工，3-实习生，4-劳务派遣，5-退休返聘）
     */
    @Column(name = "emp_type")
    private Integer empType;

    /**
     * 入职时间
     */
    @Column(name = "hired_date")
    private Long hiredDate;

    /**
     * 实名认证状态
     */
    @Column(name = "real_authed")
    private Boolean realAuthed = false;

    /**
     * 高管模式
     */
    @Column(name = "senior_mode")
    private Boolean seniorMode = false;

    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;

    /**
     * 扩展信息（JSON格式）
     */
    @Column(name = "extra_info", columnDefinition = "TEXT")
    private String extraInfo;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
}
