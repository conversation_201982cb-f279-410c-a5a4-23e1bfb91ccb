package com.hzwangda.edu.notification.util;

import freemarker.template.Configuration;
import freemarker.template.Template;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.StringReader;
import java.io.StringWriter;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 通知模板引擎工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("notificationTemplateEngine")
public class TemplateEngine {

    private static final Logger logger = LoggerFactory.getLogger(TemplateEngine.class);

    private final Configuration freemarkerConfig;

    // 简单变量替换的正则表达式
    private static final Pattern SIMPLE_VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");

    public TemplateEngine() {
        this.freemarkerConfig = new Configuration(Configuration.VERSION_2_3_32);
        this.freemarkerConfig.setDefaultEncoding("UTF-8");
        this.freemarkerConfig.setNumberFormat("0.######");
    }

    /**
     * 渲染模板
     *
     * @param templateContent 模板内容
     * @param variables 变量
     * @return 渲染结果
     */
    public String render(String templateContent, Map<String, Object> variables) {
        if (templateContent == null) {
            return null;
        }

        if (variables == null || variables.isEmpty()) {
            return templateContent;
        }

        try {
            // 首先尝试简单变量替换
            String result = renderSimpleVariables(templateContent, variables);

            // 如果包含FreeMarker语法，则使用FreeMarker渲染
            if (containsFreemarkerSyntax(result)) {
                result = renderWithFreemarker(result, variables);
            }

            return result;

        } catch (Exception e) {
            logger.error("模板渲染失败: {}", e.getMessage(), e);
            return templateContent; // 渲染失败时返回原始内容
        }
    }

    /**
     * 简单变量替换
     */
    private String renderSimpleVariables(String templateContent, Map<String, Object> variables) {
        String result = templateContent;

        Matcher matcher = SIMPLE_VARIABLE_PATTERN.matcher(templateContent);
        while (matcher.find()) {
            String variableName = matcher.group(1);
            Object value = getVariableValue(variables, variableName);
            if (value != null) {
                result = result.replace("${" + variableName + "}", value.toString());
            }
        }

        return result;
    }

    /**
     * 使用FreeMarker渲染
     */
    private String renderWithFreemarker(String templateContent, Map<String, Object> variables) throws Exception {
        Template template = new Template("template", new StringReader(templateContent), freemarkerConfig);

        StringWriter writer = new StringWriter();
        template.process(variables, writer);

        return writer.toString();
    }

    /**
     * 检查是否包含FreeMarker语法
     */
    private boolean containsFreemarkerSyntax(String content) {
        return content.contains("<#") || content.contains("[#") ||
               content.contains("<@") || content.contains("[@");
    }

    /**
     * 获取变量值（支持嵌套属性）
     */
    private Object getVariableValue(Map<String, Object> variables, String variableName) {
        if (variableName.contains(".")) {
            // 处理嵌套属性，如 user.name
            String[] parts = variableName.split("\\.");
            Object current = variables.get(parts[0]);

            for (int i = 1; i < parts.length && current != null; i++) {
                if (current instanceof Map) {
                    current = ((Map<?, ?>) current).get(parts[i]);
                } else {
                    // 可以扩展支持对象属性访问
                    current = null;
                }
            }

            return current;
        } else {
            return variables.get(variableName);
        }
    }

    /**
     * 验证模板语法
     */
    public boolean validateTemplate(String templateContent) {
        try {
            if (containsFreemarkerSyntax(templateContent)) {
                new Template("validation", new StringReader(templateContent), freemarkerConfig);
            }
            return true;
        } catch (Exception e) {
            logger.warn("模板语法验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 提取模板中的变量名
     */
    public java.util.Set<String> extractVariables(String templateContent) {
        java.util.Set<String> variables = new java.util.HashSet<>();

        if (templateContent == null) {
            return variables;
        }

        // 提取简单变量
        Matcher matcher = SIMPLE_VARIABLE_PATTERN.matcher(templateContent);
        while (matcher.find()) {
            variables.add(matcher.group(1));
        }

        // 可以扩展提取FreeMarker变量的逻辑

        return variables;
    }
}
