package com.hzwangda.edu.notification.service.channel.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hzwangda.edu.notification.config.SmsConfig;
import com.hzwangda.edu.notification.entity.NotificationMessage;
import com.hzwangda.edu.notification.entity.SmsRecord;
import com.hzwangda.edu.notification.enums.ChannelType;
import com.hzwangda.edu.notification.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * 短信通知渠道处理器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@Component
public class SmsChannelHandler implements ChannelHandler {

    @Autowired
    private SmsConfig smsConfig;

    @Autowired
    private SmsService smsService;

    @Autowired
    private ObjectMapper objectMapper;

    // 手机号正则表达式
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    // 发送频率控制缓存
    private final Map<String, LocalDateTime> rateLimitCache = new ConcurrentHashMap<>();

    // 黑名单缓存
    private final Map<String, LocalDateTime> blacklistCache = new ConcurrentHashMap<>();

    @Override
    public boolean send(NotificationMessage message) {
        log.info("开始发送短信通知: messageId={}, recipient={}", message.getMessageId(), message.getRecipient());

        try {
            // 检查短信功能是否启用
            if (!smsConfig.isEnabled()) {
                log.warn("短信功能未启用，跳过发送: messageId={}", message.getMessageId());
                return false;
            }

            // 验证手机号码
            String phoneNumber = extractPhoneNumber(message.getRecipient());
            if (!isValidPhoneNumber(phoneNumber)) {
                log.error("无效的手机号码: {}", phoneNumber);
                return false;
            }

            // 检查黑名单
            if (isInBlacklist(phoneNumber)) {
                log.warn("手机号在黑名单中，跳过发送: phoneNumber={}", phoneNumber);
                return false;
            }

            // 检查发送频率限制
            if (!checkRateLimit(phoneNumber)) {
                log.warn("短信发送频率超限: phoneNumber={}", phoneNumber);
                return false;
            }

            // 检查服务商发送限制
            if (!smsService.checkSendLimit(phoneNumber)) {
                log.warn("短信发送超出服务商限制: phoneNumber={}", phoneNumber);
                return false;
            }

            SmsRecord smsRecord;

            // 检查是否使用模板
            if (StringUtils.hasText(message.getTemplateCode())) {
                // 使用模板发送
                Map<String, Object> templateParams = parseTemplateParams(message);
                smsRecord = smsService.sendSmsWithTemplate(phoneNumber, message.getTemplateCode(), templateParams);
            } else {
                // 直接发送内容
                String content = buildSmsContent(message);
                smsRecord = smsService.sendSms(phoneNumber, content);
            }

            if (smsRecord != null && "SUCCESS".equals(smsRecord.getSendStatus())) {
                // 更新发送频率记录
                updateRateLimit(phoneNumber);

                // 保存外部ID
                if (StringUtils.hasText(smsRecord.getProviderMessageId())) {
                    message.setMessageId(smsRecord.getProviderMessageId());
                }

                log.info("短信发送成功: messageId={}, smsRecordId={}, externalId={}",
                        message.getMessageId(), smsRecord.getId(), smsRecord.getProviderMessageId());
                return true;
            } else {
                log.error("短信发送失败: messageId={}, error={}", message.getMessageId(),
                         smsRecord != null ? smsRecord.getErrorMessage() : "未知错误");
                return false;
            }

        } catch (Exception e) {
            log.error("短信发送异常: messageId={}, error={}", message.getMessageId(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Async("notificationTaskExecutor")
    public void sendAsync(NotificationMessage message) {
        log.info("异步发送短信通知: messageId={}", message.getId());

        try {
            boolean success = send(message);

            // 更新消息状态（这里应该调用NotificationService更新状态）
            if (success) {
                log.info("异步短信发送成功: messageId={}", message.getId());
                // TODO: 更新消息状态为SUCCESS
            } else {
                log.error("异步短信发送失败: messageId={}", message.getId());
                // TODO: 更新消息状态为FAILED，可能需要重试
            }

        } catch (Exception e) {
            log.error("异步短信发送异常: messageId={}, error={}", message.getId(), e.getMessage(), e);
            // TODO: 更新消息状态为FAILED
        }
    }

    @Override
    public boolean isAvailable() {
        try {
            // 检查短信配置是否完整
            if (!smsConfig.isEnabled()) {
                return false;
            }

            String provider = smsConfig.getProvider();
            switch (provider) {
                case "aliyun":
                    SmsConfig.AliyunSmsConfig aliyun = smsConfig.getAliyun();
                    return StringUtils.hasText(aliyun.getAccessKeyId()) &&
                           StringUtils.hasText(aliyun.getAccessKeySecret()) &&
                           StringUtils.hasText(aliyun.getSignName());

                case "tencent":
                    SmsConfig.TencentSmsConfig tencent = smsConfig.getTencent();
                    return StringUtils.hasText(tencent.getSecretId()) &&
                           StringUtils.hasText(tencent.getSecretKey()) &&
                           StringUtils.hasText(tencent.getSdkAppId()) &&
                           StringUtils.hasText(tencent.getSignName());

                case "huawei":
                    SmsConfig.HuaweiSmsConfig huawei = smsConfig.getHuawei();
                    return StringUtils.hasText(huawei.getAccessKeyId()) &&
                           StringUtils.hasText(huawei.getSecretAccessKey()) &&
                           StringUtils.hasText(huawei.getSender());

                default:
                    return false;
            }

        } catch (Exception e) {
            log.error("检查短信渠道可用性失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getStatus() {
        if (!smsConfig.isEnabled()) {
            return "短信功能已禁用";
        }

        if (!isAvailable()) {
            return "短信配置不完整";
        }

        return "短信渠道正常";
    }

    @Override
    public String getSupportedChannelType() {
        return ChannelType.SMS.getCode();
    }

    /**
     * 从收件人信息中提取手机号码
     */
    private String extractPhoneNumber(String recipient) {
        if (!StringUtils.hasText(recipient)) {
            return null;
        }

        // 如果是JSON格式，解析手机号码
        if (recipient.startsWith("{") && recipient.endsWith("}")) {
            try {
                // TODO: 解析JSON格式的收件人信息
                // 这里简化处理，实际应该解析JSON
                return recipient;
            } catch (Exception e) {
                log.warn("解析收件人JSON失败: {}", recipient);
                return null;
            }
        }

        // 直接返回手机号码
        return recipient;
    }

    /**
     * 验证手机号码格式
     */
    private boolean isValidPhoneNumber(String phoneNumber) {
        if (!StringUtils.hasText(phoneNumber)) {
            return false;
        }

        // 简单的手机号码格式验证（中国大陆）
        String regex = "^1[3-9]\\d{9}$";
        return phoneNumber.matches(regex);
    }

    /**
     * 解析模板参数
     */
    private Map<String, Object> parseTemplateParams(NotificationMessage message) {
        Map<String, Object> params = new HashMap<>();

        try {
            // 从消息变量中获取参数
            // TODO: 参数对应不上
//            if (message.getMessageVariables() != null) {
//                params.putAll(message.getMessageVariables());
//            }

            // 添加基本参数
            if (StringUtils.hasText(message.getMessageTitle())) {
                params.put("title", message.getMessageTitle());
            }

            if (StringUtils.hasText(message.getMessageContent())) {
                params.put("content", message.getMessageContent());
            }

            // 添加系统参数
            params.put("system", "业务管理系统");
            params.put("time", LocalDateTime.now().toString());

            return params;

        } catch (Exception e) {
            log.warn("解析模板参数失败: messageId={}, error={}", message.getMessageId(), e.getMessage());
            return params;
        }
    }

    /**
     * 构建短信内容
     */
    private String buildSmsContent(NotificationMessage message) {
        StringBuilder content = new StringBuilder();

        // 添加标题
        if (StringUtils.hasText(message.getMessageTitle())) {
            content.append("【").append(message.getMessageTitle()).append("】");
        }

        // 添加内容
        if (StringUtils.hasText(message.getMessageContent())) {
            content.append(message.getMessageContent());
        }

        // 添加签名
        if (!content.toString().contains("杭州望达")) {
            content.append(" - 业务管理系统");
        }

        return content.toString();
    }

    /**
     * 检查发送频率限制
     */
    private boolean checkRateLimit(String phoneNumber) {
        LocalDateTime lastSendTime = rateLimitCache.get(phoneNumber);
        if (lastSendTime == null) {
            return true;
        }

        // 检查是否在1分钟内发送过（防止频繁发送）
        LocalDateTime oneMinuteAgo = LocalDateTime.now().minusMinutes(1);
        return lastSendTime.isBefore(oneMinuteAgo);
    }

    /**
     * 更新发送频率记录
     */
    private void updateRateLimit(String phoneNumber) {
        rateLimitCache.put(phoneNumber, LocalDateTime.now());

        // 清理过期记录（保留最近1小时的记录）
        cleanupExpiredCache(rateLimitCache, 1);
    }

    /**
     * 检查是否在黑名单中
     */
    private boolean isInBlacklist(String phoneNumber) {
        return blacklistCache.containsKey(phoneNumber);
    }

    /**
     * 添加到黑名单
     */
    public void addToBlacklist(String phoneNumber, String reason) {
        blacklistCache.put(phoneNumber, LocalDateTime.now());
        log.info("手机号已添加到黑名单: phoneNumber={}, reason={}", phoneNumber, reason);
    }

    /**
     * 从黑名单移除
     */
    public void removeFromBlacklist(String phoneNumber) {
        blacklistCache.remove(phoneNumber);
        log.info("手机号已从黑名单移除: phoneNumber={}", phoneNumber);
    }

    /**
     * 清理过期缓存
     */
    private void cleanupExpiredCache(Map<String, LocalDateTime> cache, int hours) {
        LocalDateTime expireTime = LocalDateTime.now().minusHours(hours);
        cache.entrySet().removeIf(entry -> entry.getValue().isBefore(expireTime));
    }

    /**
     * 获取发送统计信息
     */
    public Map<String, Object> getSendStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("rateLimitCacheSize", rateLimitCache.size());
        stats.put("blacklistCacheSize", blacklistCache.size());
        stats.put("provider", smsConfig.getProvider());
        stats.put("enabled", smsConfig.isEnabled());
        return stats;
    }
}
