package com.hzwangda.edu.notification.task;

import com.hzwangda.edu.notification.service.NotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 消息通知定时任务
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class NotificationScheduledTask {

    private static final Logger logger = LoggerFactory.getLogger(NotificationScheduledTask.class);

    @Autowired
    private NotificationService notificationService;

    /**
     * 处理待发送消息
     * 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000)
    public void processPendingMessages() {
        logger.debug("开始处理待发送消息");

        try {
            notificationService.processPendingMessages();
        } catch (Exception e) {
            logger.error("处理待发送消息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理重试消息
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000)
    public void processRetryMessages() {
        logger.debug("开始处理重试消息");

        try {
            notificationService.processRetryMessages();
        } catch (Exception e) {
            logger.error("处理重试消息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 清理过期消息
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000)
    public void cleanupExpiredMessages() {
        logger.debug("开始清理过期消息");

        try {
            notificationService.cleanupExpiredMessages();
        } catch (Exception e) {
            logger.error("清理过期消息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 系统健康检查
     * 每10分钟执行一次
     */
    @Scheduled(fixedRate = 600000)
    public void healthCheck() {
        logger.debug("执行系统健康检查");

        try {
            // 可以添加系统健康检查逻辑
            // 例如检查各个通知渠道的状态
            logger.debug("系统健康检查完成");
        } catch (Exception e) {
            logger.error("系统健康检查失败: {}", e.getMessage(), e);
        }
    }
}
