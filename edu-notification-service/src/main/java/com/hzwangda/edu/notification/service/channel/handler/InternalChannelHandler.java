package com.hzwangda.edu.notification.service.channel.handler;

import com.hzwangda.edu.notification.entity.NotificationMessage;
import com.hzwangda.edu.notification.enums.ChannelType;
import com.hzwangda.edu.notification.repository.NotificationMessageRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 站内信渠道处理器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("internalChannelHandler")
public class InternalChannelHandler implements ChannelHandler {

    private static final Logger logger = LoggerFactory.getLogger(InternalChannelHandler.class);

    @Autowired
    private NotificationMessageRepository messageRepository;

    @Autowired(required = false)
    private SimpMessagingTemplate messagingTemplate;

    @Override
    public boolean send(NotificationMessage message) {
        logger.debug("发送站内信: 消息ID={}, 接收人={}", message.getMessageId(), message.getRecipient());

        try {
            // 站内信直接保存到数据库即可，已经在调用前保存了
            // 这里主要是通过WebSocket推送实时通知
            if (messagingTemplate != null && message.getRecipientId() != null) {
                sendWebSocketNotification(message);
            }

            logger.info("站内信发送成功: 消息ID={}, 接收人={}", message.getMessageId(), message.getRecipient());
            return true;

        } catch (Exception e) {
            logger.error("站内信发送失败: 消息ID={}, 接收人={}, 错误={}",
                        message.getMessageId(), message.getRecipient(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Async("notificationExecutor")
    public void sendAsync(NotificationMessage message) {
        logger.debug("异步发送站内信: 消息ID={}, 接收人={}", message.getMessageId(), message.getRecipient());

        boolean success = send(message);

        if (success) {
            logger.debug("异步站内信发送成功: 消息ID={}", message.getMessageId());
        } else {
            logger.error("异步站内信发送失败: 消息ID={}", message.getMessageId());
        }
    }

    @Override
    public boolean isAvailable() {
        // 站内信渠道总是可用的
        return true;
    }

    @Override
    public String getStatus() {
        return "AVAILABLE";
    }

    @Override
    public String getSupportedChannelType() {
        return ChannelType.INTERNAL.getCode();
    }

    /**
     * 发送WebSocket实时通知
     */
    private void sendWebSocketNotification(NotificationMessage message) {
        try {
            // 构建WebSocket消息
            WebSocketMessage wsMessage = new WebSocketMessage();
            wsMessage.setMessageId(message.getMessageId());
            wsMessage.setTitle(message.getMessageTitle());
            wsMessage.setContent(message.getMessageContent());
            wsMessage.setType("NOTIFICATION");
            wsMessage.setTimestamp(LocalDateTime.now());

            // 发送到指定用户
            String destination = "/user/" + message.getRecipientId() + "/notifications";
            messagingTemplate.convertAndSend(destination, wsMessage);

            logger.debug("WebSocket通知发送成功: 用户ID={}, 消息ID={}",
                        message.getRecipientId(), message.getMessageId());

        } catch (Exception e) {
            logger.warn("WebSocket通知发送失败: {}", e.getMessage());
        }
    }

    /**
     * WebSocket消息类
     */
    public static class WebSocketMessage {
        private String messageId;
        private String title;
        private String content;
        private String type;
        private LocalDateTime timestamp;

        // Getter and Setter methods
        public String getMessageId() {
            return messageId;
        }

        public void setMessageId(String messageId) {
            this.messageId = messageId;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public LocalDateTime getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(LocalDateTime timestamp) {
            this.timestamp = timestamp;
        }
    }
}
