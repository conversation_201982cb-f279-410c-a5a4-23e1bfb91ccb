package com.hzwangda.edu.notification.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 钉钉发送记录实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DingTalkRecord {

    /**
     * 记录ID
     */
    private String id;

    /**
     * 接收者
     */
    private String toUser;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息类型（text/markdown/actionCard/link等）
     */
    private String msgType;

    /**
     * 钉钉类型（robot/work）
     */
    private String dingTalkType;

    /**
     * 发送状态（PENDING/SUCCESS/FAILED）
     */
    private String sendStatus;

    /**
     * 发送时间
     */
    private LocalDateTime sendTime;

    /**
     * 接收时间
     */
    private LocalDateTime receiveTime;

    /**
     * 钉钉返回的消息ID
     */
    private String providerMessageId;

    /**
     * 钉钉返回的任务ID（工作通知）
     */
    private String taskId;

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 下次重试时间
     */
    private LocalDateTime nextRetryTime;

    /**
     * 扩展属性（JSON格式）
     */
    private String extendedProperties;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;
}
