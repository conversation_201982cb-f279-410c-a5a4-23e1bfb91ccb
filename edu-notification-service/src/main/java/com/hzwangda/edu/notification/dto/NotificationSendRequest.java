package com.hzwangda.edu.notification.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 消息发送请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@Schema(description = "消息发送请求")
public class NotificationSendRequest {

    @Size(max = 64, message = "模板编码长度不能超过64个字符")
    @Schema(description = "模板编码", example = "LEAVE_APPROVAL_NOTIFY")
    private String templateCode;

    @NotBlank(message = "通知渠道不能为空")
    @Size(max = 32, message = "通知渠道长度不能超过32个字符")
    @Schema(description = "通知渠道", example = "EMAIL")
    private String channelType;

    private String recipient;

    @NotEmpty(message = "接收人列表不能为空")
    @Schema(description = "接收人列表")
    private List<String> recipients;

    @Schema(description = "接收人ID列表")
    private List<Long> recipientIds;

    @Schema(description = "接收人姓名列表")
    private List<String> recipientNames;

    @Size(max = 255, message = "消息标题长度不能超过255个字符")
    @Schema(description = "消息标题", example = "【业务系统】请假审批通知")
    private String messageTitle;

    @Schema(description = "消息内容")
    private String messageContent;

    @Schema(description = "消息变量")
    private Map<String, Object> variables;

    @Schema(description = "优先级", example = "50")
    private Integer priority = 50;

    @Schema(description = "计划发送时间")
    private LocalDateTime scheduledTime;

    @Schema(description = "最大重试次数", example = "3")
    private Integer maxRetryCount = 3;

    @Size(max = 64, message = "业务类型长度不能超过64个字符")
    @Schema(description = "业务类型", example = "WORKFLOW")
    private String businessType;

    @Size(max = 64, message = "业务ID长度不能超过64个字符")
    @Schema(description = "业务ID", example = "LEAVE_20241219_001")
    private String businessId;

    @Size(max = 64, message = "发送人长度不能超过64个字符")
    @Schema(description = "发送人", example = "system")
    private String sender;

    @Schema(description = "发送人ID", example = "0")
    private Long senderId;

    @Schema(description = "附件列表")
    private List<AttachmentInfo> attachments;

    @Schema(description = "过期时间")
    private LocalDateTime expireTime;

    @Schema(description = "是否异步发送", example = "true")
    private Boolean async = true;

    @Schema(description = "是否批量发送", example = "false")
    private Boolean batch = false;

    // 内部类：附件信息
    @Schema(description = "附件信息")
    public static class AttachmentInfo {
        @Schema(description = "附件名称")
        private String fileName;

        @Schema(description = "附件URL")
        private String fileUrl;

        @Schema(description = "附件大小")
        private Long fileSize;

        @Schema(description = "附件类型")
        private String fileType;

        // 构造函数
        public AttachmentInfo() {
        }

        public AttachmentInfo(String fileName, String fileUrl) {
            this.fileName = fileName;
            this.fileUrl = fileUrl;
        }

        // Getter and Setter methods
        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public String getFileUrl() {
            return fileUrl;
        }

        public void setFileUrl(String fileUrl) {
            this.fileUrl = fileUrl;
        }

        public Long getFileSize() {
            return fileSize;
        }

        public void setFileSize(Long fileSize) {
            this.fileSize = fileSize;
        }

        public String getFileType() {
            return fileType;
        }

        public void setFileType(String fileType) {
            this.fileType = fileType;
        }
    }

    // 构造函数
    public NotificationSendRequest() {
    }

    // 便捷方法 - 为了兼容测试类
    public void setTitle(String title) {
        this.messageTitle = title;
    }

    public String getTitle() {
        return this.messageTitle;
    }

    public void setContent(String content) {
        this.messageContent = content;
    }

    public String getContent() {
        return this.messageContent;
    }

    public void setTemplateVariables(Map<String, Object> templateVariables) {
        this.variables = templateVariables;
    }

    public Map<String, Object> getTemplateVariables() {
        return this.variables;
    }

    public void setTemplateId(String templateId) {
        this.templateCode = templateId;
    }

    public String getTemplateId() {
        return this.templateCode;
    }

    public String getRecipient() {
        return this.recipient;
    }

    public void setRecipient(String recipient) {
        this.recipient = recipient;
    }

}
