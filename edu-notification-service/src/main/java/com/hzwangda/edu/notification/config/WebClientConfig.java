package com.hzwangda.edu.notification.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

/**
 * Web客户端配置类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class WebClientConfig {

    /**
     * 配置RestTemplate Bean
     *
     * @return RestTemplate实例
     */
    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(clientHttpRequestFactory());
        return restTemplate;
    }

    /**
     * 配置HTTP请求工厂
     *
     * @return ClientHttpRequestFactory实例
     */
    @Bean
    public ClientHttpRequestFactory clientHttpRequestFactory() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();

        // 设置连接超时时间（毫秒）
        factory.setConnectTimeout(Duration.ofSeconds(10));

        // 设置读取超时时间（毫秒）
        factory.setReadTimeout(Duration.ofSeconds(30));

        return factory;
    }
}
