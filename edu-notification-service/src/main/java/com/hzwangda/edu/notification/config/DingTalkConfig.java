package com.hzwangda.edu.notification.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 钉钉配置类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@Component
@ConfigurationProperties(prefix = "hky.notification.dingtalk")
public class DingTalkConfig {

    /**
     * 是否启用钉钉功能
     */
    private boolean enabled = false;

    /**
     * 钉钉类型（robot, work）
     */
    private String type = "work";

    /**
     * 钉钉机器人配置
     */
    private DingTalkRobotConfig robot = new DingTalkRobotConfig();

    /**
     * 钉钉工作通知配置
     */
    private DingTalkWorkConfig work = new DingTalkWorkConfig();

    /**
     * 钉钉机器人配置
     */
    @Data
    public static class DingTalkRobotConfig {
        /**
         * 机器人Webhook地址
         */
        private String webhookUrl;

        /**
         * 机器人密钥
         */
        private String secret;

        /**
         * 机器人访问Token
         */
        private String accessToken;

        /**
         * 是否启用签名验证
         */
        private boolean enableSign = true;

        /**
         * 关键词列表（安全设置）
         */
        private String[] keywords;

        /**
         * 白名单IP列表
         */
        private String[] whitelistIps;

        /**
         * @所有人时是否提醒
         */
        private boolean atAll = false;

        /**
         * @指定人员的手机号列表
         */
        private String[] atMobiles;

        /**
         * @指定人员的用户ID列表
         */
        private String[] atUserIds;
    }

    /**
     * 钉钉工作通知配置
     */
    @Data
    public static class DingTalkWorkConfig {
        /**
         * 企业ID
         */
        private String corpId;

        /**
         * 应用Key
         */
        private String appKey;

        /**
         * 应用Secret
         */
        private String appSecret;

        /**
         * 应用ID
         */
        private String agentId;

        /**
         * API地址
         */
        private String apiUrl = "https://oapi.dingtalk.com";

        /**
         * 获取Token的URL
         */
        private String tokenUrl = "/gettoken";

        /**
         * 发送工作通知的URL
         */
        private String sendWorkNoticeUrl = "/topapi/message/corpconversation/asyncsend_v2";

        /**
         * 获取用户信息的URL
         */
        private String getUserInfoUrl = "/topapi/v2/user/get";

        /**
         * 获取部门用户的URL
         */
        private String getDeptUsersUrl = "/topapi/user/simplelist";

        /**
         * 发送群消息的URL
         */
        private String sendGroupMessageUrl = "/robot/send";
    }

    /**
     * 消息发送限制配置
     */
    @Data
    public static class DingTalkLimitConfig {
        /**
         * 每分钟最大发送数量
         */
        private int maxPerMinute = 20;

        /**
         * 每小时最大发送数量
         */
        private int maxPerHour = 200;

        /**
         * 每天最大发送数量
         */
        private int maxPerDay = 2000;

        /**
         * 机器人每分钟最大发送数量
         */
        private int robotMaxPerMinute = 20;
    }

    /**
     * 消息发送限制
     */
    private DingTalkLimitConfig limit = new DingTalkLimitConfig();
}
