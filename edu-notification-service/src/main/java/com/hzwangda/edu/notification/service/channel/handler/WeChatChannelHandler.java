package com.hzwangda.edu.notification.service.channel.handler;

import com.hzwangda.edu.notification.config.WeChatConfig;
import com.hzwangda.edu.notification.entity.NotificationMessage;
import com.hzwangda.edu.notification.entity.WeChatRecord;
import com.hzwangda.edu.notification.enums.ChannelType;
import com.hzwangda.edu.notification.service.WeChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信通知渠道处理器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@Component
public class WeChatChannelHandler implements ChannelHandler {

    @Autowired
    private WeChatConfig weChatConfig;

    @Autowired
    private WeChatService weChatService;

    @Override
    public boolean send(NotificationMessage message) {
        log.info("开始发送微信通知: messageId={}, recipient={}", message.getId(), message.getRecipient());

        try {
            // 检查微信功能是否启用
            if (!weChatConfig.isEnabled()) {
                log.warn("微信功能未启用，跳过发送: messageId={}", message.getId());
                return false;
            }

            // 解析接收者信息
            String recipient = extractRecipient(message.getRecipient());
            if (!StringUtils.hasText(recipient)) {
                log.error("无效的接收者信息: {}", message.getRecipient());
                return false;
            }

            // 获取微信类型
            String wechatType = determineWeChatType(message);

            // 检查发送限制
            if (!weChatService.checkSendLimit(recipient, wechatType)) {
                log.warn("微信发送超出限制: recipient={}, wechatType={}", recipient, wechatType);
                return false;
            }

            // 根据消息类型发送
            WeChatRecord record = sendWeChatMessage(message, recipient, wechatType);

            if (record != null && "SUCCESS".equals(record.getSendStatus())) {
                log.info("微信发送成功: messageId={}, recordId={}", message.getId(), record.getId());
                return true;
            } else {
                log.error("微信发送失败: messageId={}, error={}", message.getId(),
                         record != null ? record.getErrorMessage() : "未知错误");
                return false;
            }

        } catch (Exception e) {
            log.error("微信发送异常: messageId={}, error={}", message.getId(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Async("notificationTaskExecutor")
    public void sendAsync(NotificationMessage message) {
        log.info("异步发送微信通知: messageId={}", message.getId());

        try {
            boolean success = send(message);

            if (success) {
                log.info("异步微信发送成功: messageId={}", message.getId());
                // TODO: 更新消息状态为SUCCESS
            } else {
                log.error("异步微信发送失败: messageId={}", message.getId());
                // TODO: 更新消息状态为FAILED，可能需要重试
            }

        } catch (Exception e) {
            log.error("异步微信发送异常: messageId={}, error={}", message.getId(), e.getMessage(), e);
            // TODO: 更新消息状态为FAILED
        }
    }

    @Override
    public boolean isAvailable() {
        try {
            if (!weChatConfig.isEnabled()) {
                return false;
            }

            String type = weChatConfig.getType();
            switch (type) {
                case "work":
                    WeChatConfig.WeChatWorkConfig work = weChatConfig.getWork();
                    return StringUtils.hasText(work.getCorpId()) &&
                           StringUtils.hasText(work.getCorpSecret()) &&
                           StringUtils.hasText(work.getAgentId());

                case "service":
                    WeChatConfig.WeChatServiceConfig service = weChatConfig.getService();
                    return StringUtils.hasText(service.getAppId()) &&
                           StringUtils.hasText(service.getAppSecret());

                case "mini":
                    WeChatConfig.WeChatMiniConfig mini = weChatConfig.getMini();
                    return StringUtils.hasText(mini.getAppId()) &&
                           StringUtils.hasText(mini.getAppSecret());

                default:
                    return false;
            }

        } catch (Exception e) {
            log.error("检查微信渠道可用性失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getStatus() {
        if (!weChatConfig.isEnabled()) {
            return "微信功能已禁用";
        }

        if (!isAvailable()) {
            return "微信配置不完整";
        }

        // 检查Access Token是否有效
        try {
            String accessToken = weChatService.getAccessToken(weChatConfig.getType());
            if (StringUtils.hasText(accessToken)) {
                return "微信渠道正常";
            } else {
                return "微信Token获取失败";
            }
        } catch (Exception e) {
            return "微信Token验证失败: " + e.getMessage();
        }
    }

    @Override
    public String getSupportedChannelType() {
        return ChannelType.WECHAT.getCode();
    }

    /**
     * 发送微信消息
     */
    private WeChatRecord sendWeChatMessage(NotificationMessage message, String recipient, String wechatType) {
        try {
            // 检查是否使用模板
            if (StringUtils.hasText(message.getTemplateCode())) {
                // 使用模板发送
                Map<String, Object> templateData = parseTemplateParams(message.getMessageVariables());
                return weChatService.sendTemplateMessage(recipient, message.getTemplateCode(), templateData, wechatType);
            } else {
                // 根据内容类型发送
                String contentType = determineContentType(message.getMessageContent());

                switch (contentType) {
                    case "markdown":
                        if ("work".equals(wechatType)) {
                            return weChatService.sendMarkdownMessage(recipient, message.getMessageContent());
                        } else {
                            // 其他类型不支持Markdown，转为文本
                            return weChatService.sendTextMessage(recipient, message.getMessageContent(), wechatType);
                        }

                    case "textcard":
                        if ("work".equals(wechatType)) {
                            Map<String, Object> cardData = parseCardData(message.getMessageContent());
                            return weChatService.sendTextCardMessage(
                                recipient,
                                (String) cardData.get("title"),
                                (String) cardData.get("description"),
                                (String) cardData.get("url"),
                                (String) cardData.get("btnText")
                            );
                        } else {
                            return weChatService.sendTextMessage(recipient, message.getMessageContent(), wechatType);
                        }

                    default:
                        // 默认发送文本消息
                        return weChatService.sendTextMessage(recipient, message.getMessageContent(), wechatType);
                }
            }

        } catch (Exception e) {
            log.error("发送微信消息失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从收件人信息中提取接收者
     */
    private String extractRecipient(String recipient) {
        if (!StringUtils.hasText(recipient)) {
            return null;
        }

        // 如果是JSON格式，解析接收者信息
        if (recipient.startsWith("{") && recipient.endsWith("}")) {
            try {
                // TODO: 解析JSON格式的收件人信息
                // 这里简化处理，实际应该解析JSON
                return recipient;
            } catch (Exception e) {
                log.warn("解析收件人JSON失败: {}", recipient);
                return null;
            }
        }

        // 直接返回接收者
        return recipient;
    }

    /**
     * 确定微信类型
     */
    private String determineWeChatType(NotificationMessage message) {
        // 可以从消息的扩展属性中获取微信类型
        // 这里使用配置的默认类型
        return weChatConfig.getType();
    }

    /**
     * 确定内容类型
     */
    private String determineContentType(String content) {
        if (!StringUtils.hasText(content)) {
            return "text";
        }

        // 简单判断内容类型
        if (content.contains("##") || content.contains("**") || content.contains("[") && content.contains("](")) {
            return "markdown";
        }

        if (content.startsWith("{") && content.contains("\"title\"") && content.contains("\"description\"")) {
            return "textcard";
        }

        return "text";
    }

    /**
     * 解析模板参数
     */
    private Map<String, Object> parseTemplateParams(String templateParams) {
        Map<String, Object> params = new HashMap<>();

        if (!StringUtils.hasText(templateParams)) {
            return params;
        }

        try {
            // TODO: 解析JSON格式的模板参数
            // 这里简化处理，实际应该使用JSON解析器
            return params;
        } catch (Exception e) {
            log.warn("解析模板参数失败: {}", templateParams);
            return params;
        }
    }

    /**
     * 解析卡片数据
     */
    private Map<String, Object> parseCardData(String content) {
        Map<String, Object> cardData = new HashMap<>();

        try {
            // TODO: 解析卡片数据
            // 这里简化处理，实际应该解析JSON或其他格式
            cardData.put("title", "通知");
            cardData.put("description", content);
            cardData.put("url", "");
            cardData.put("btnText", "查看详情");

            return cardData;
        } catch (Exception e) {
            log.warn("解析卡片数据失败: {}", content);
            return cardData;
        }
    }
}
