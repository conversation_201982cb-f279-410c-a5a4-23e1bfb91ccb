package com.hzwangda.edu.notification.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * 定时发送短信请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "定时发送短信请求")
public class ScheduleSmsRequest {

    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "手机号", example = "13800138000")
    private String phoneNumber;

    @NotBlank(message = "短信内容不能为空")
    @Schema(description = "短信内容", example = "您的验证码是123456，5分钟内有效。")
    private String content;

    @NotNull(message = "发送时间不能为空")
    @Schema(description = "发送时间", example = "2024-01-15T10:30:00")
    private LocalDateTime sendTime;

    @Schema(description = "优先级", example = "50")
    private Integer priority = 50;

    @Schema(description = "备注", example = "系统通知")
    private String remark;
}
