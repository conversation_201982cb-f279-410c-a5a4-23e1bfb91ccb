package com.hzwangda.edu.notification.service;

import com.hzwangda.edu.notification.entity.WeChatRecord;
import com.hzwangda.edu.notification.entity.WeChatUser;

import java.util.List;
import java.util.Map;

/**
 * 微信服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public interface WeChatService {

    /**
     * 发送文本消息
     *
     * @param toUser 接收者
     * @param content 消息内容
     * @param wechatType 微信类型
     * @return 发送记录
     */
    WeChatRecord sendTextMessage(String toUser, String content, String wechatType);

    /**
     * 发送模板消息
     *
     * @param toUser 接收者
     * @param templateId 模板ID
     * @param templateData 模板数据
     * @param wechatType 微信类型
     * @return 发送记录
     */
    WeChatRecord sendTemplateMessage(String toUser, String templateId, Map<String, Object> templateData, String wechatType);

    /**
     * 发送卡片消息（企业微信）
     *
     * @param toUser 接收者
     * @param title 标题
     * @param description 描述
     * @param url 跳转链接
     * @param btnText 按钮文字
     * @return 发送记录
     */
    WeChatRecord sendTextCardMessage(String toUser, String title, String description, String url, String btnText);

    /**
     * 发送图文消息
     *
     * @param toUser 接收者
     * @param articles 图文列表
     * @param wechatType 微信类型
     * @return 发送记录
     */
    WeChatRecord sendNewsMessage(String toUser, List<Map<String, Object>> articles, String wechatType);

    /**
     * 发送Markdown消息（企业微信）
     *
     * @param toUser 接收者
     * @param content Markdown内容
     * @return 发送记录
     */
    WeChatRecord sendMarkdownMessage(String toUser, String content);

    /**
     * 发送群消息
     *
     * @param chatId 群聊ID
     * @param content 消息内容
     * @param msgType 消息类型
     * @param wechatType 微信类型
     * @return 发送记录
     */
    WeChatRecord sendGroupMessage(String chatId, String content, String msgType, String wechatType);

    /**
     * 批量发送消息
     *
     * @param toUsers 接收者列表
     * @param content 消息内容
     * @param msgType 消息类型
     * @param wechatType 微信类型
     * @return 发送记录列表
     */
    List<WeChatRecord> sendBatchMessage(List<String> toUsers, String content, String msgType, String wechatType);

    /**
     * 获取Access Token
     *
     * @param wechatType 微信类型
     * @return Access Token
     */
    String getAccessToken(String wechatType);

    /**
     * 刷新Access Token
     *
     * @param wechatType 微信类型
     * @return 新的Access Token
     */
    String refreshAccessToken(String wechatType);

    /**
     * 获取微信用户信息
     *
     * @param openId OpenID
     * @param wechatType 微信类型
     * @return 用户信息
     */
    WeChatUser getWeChatUserInfo(String openId, String wechatType);

    /**
     * 绑定微信用户
     *
     * @param userId 系统用户ID
     * @param wechatUser 微信用户信息
     * @return 绑定结果
     */
    boolean bindWeChatUser(String userId, WeChatUser wechatUser);

    /**
     * 解绑微信用户
     *
     * @param userId 系统用户ID
     * @param wechatType 微信类型
     * @return 解绑结果
     */
    boolean unbindWeChatUser(String userId, String wechatType);

    /**
     * 根据系统用户ID获取微信用户
     *
     * @param userId 系统用户ID
     * @param wechatType 微信类型
     * @return 微信用户信息
     */
    WeChatUser getWeChatUserByUserId(String userId, String wechatType);

    /**
     * 根据OpenID获取微信用户
     *
     * @param openId OpenID
     * @param wechatType 微信类型
     * @return 微信用户信息
     */
    WeChatUser getWeChatUserByOpenId(String openId, String wechatType);

    /**
     * 获取发送记录
     *
     * @param recordId 记录ID
     * @return 发送记录
     */
    WeChatRecord getWeChatRecord(String recordId);

    /**
     * 获取发送记录列表
     *
     * @param toUser 接收者
     * @param wechatType 微信类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 发送记录列表
     */
    List<WeChatRecord> getWeChatRecords(String toUser, String wechatType, String startTime, String endTime);

    /**
     * 重试发送失败的消息
     *
     * @param recordId 记录ID
     * @return 重试结果
     */
    boolean retryWeChatMessage(String recordId);

    /**
     * 获取微信发送统计
     *
     * @param wechatType 微信类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    Map<String, Object> getWeChatStatistics(String wechatType, String startTime, String endTime);

    /**
     * 检查发送限制
     *
     * @param toUser 接收者
     * @param wechatType 微信类型
     * @return 是否允许发送
     */
    boolean checkSendLimit(String toUser, String wechatType);

    /**
     * 验证微信签名
     *
     * @param signature 签名
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param token 验证Token
     * @return 验证结果
     */
    boolean verifySignature(String signature, String timestamp, String nonce, String token);

    /**
     * 处理微信回调
     *
     * @param callbackData 回调数据
     * @param wechatType 微信类型
     * @return 处理结果
     */
    String handleWeChatCallback(Map<String, Object> callbackData, String wechatType);
}
