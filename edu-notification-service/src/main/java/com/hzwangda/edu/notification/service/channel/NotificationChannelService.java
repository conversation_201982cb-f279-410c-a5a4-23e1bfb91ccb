package com.hzwangda.edu.notification.service.channel;

import com.hzwangda.edu.notification.entity.NotificationMessage;

/**
 * 通知渠道服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface NotificationChannelService {

    /**
     * 同步发送消息
     *
     * @param message 消息实体
     * @return 是否发送成功
     */
    boolean send(NotificationMessage message);

    /**
     * 异步发送消息
     *
     * @param message 消息实体
     */
    void sendAsync(NotificationMessage message);

    /**
     * 检查渠道是否可用
     *
     * @param channelType 渠道类型
     * @return 是否可用
     */
    boolean isChannelAvailable(String channelType);

    /**
     * 获取渠道状态
     *
     * @param channelType 渠道类型
     * @return 状态信息
     */
    String getChannelStatus(String channelType);
}
