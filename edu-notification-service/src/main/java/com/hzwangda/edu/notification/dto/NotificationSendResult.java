package com.hzwangda.edu.notification.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 通知发送结果DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "通知发送结果")
public class NotificationSendResult {

    @Schema(description = "是否发送成功", example = "true")
    private boolean success;

    @Schema(description = "消息ID", example = "msg_123456789")
    private String messageId;

    @Schema(description = "通道类型", example = "SMS")
    private String channelType;

    @Schema(description = "接收者", example = "13800138000")
    private String recipient;

    @Schema(description = "发送时间")
    private LocalDateTime sendTime;

    @Schema(description = "错误码", example = "0")
    private String errorCode;

    @Schema(description = "错误信息", example = "发送成功")
    private String errorMessage;

    @Schema(description = "扩展信息")
    private Map<String, Object> extendInfo;

    @Schema(description = "重试次数", example = "0")
    private int retryCount;

    @Schema(description = "费用（分）", example = "5")
    private Long cost;

    // 构造函数
    public NotificationSendResult() {
        this.sendTime = LocalDateTime.now();
        this.retryCount = 0;
    }

    public NotificationSendResult(boolean success, String messageId, String channelType, String recipient) {
        this();
        this.success = success;
        this.messageId = messageId;
        this.channelType = channelType;
        this.recipient = recipient;
    }

    // 静态工厂方法
    public static NotificationSendResult success(String messageId, String channelType, String recipient) {
        NotificationSendResult result = new NotificationSendResult(true, messageId, channelType, recipient);
        result.setErrorCode("0");
        result.setErrorMessage("发送成功");
        return result;
    }

    public static NotificationSendResult failure(String channelType, String recipient, String errorCode, String errorMessage) {
        NotificationSendResult result = new NotificationSendResult(false, null, channelType, recipient);
        result.setErrorCode(errorCode);
        result.setErrorMessage(errorMessage);
        return result;
    }

    // Getter和Setter方法
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public String getRecipient() {
        return recipient;
    }

    public void setRecipient(String recipient) {
        this.recipient = recipient;
    }

    public LocalDateTime getSendTime() {
        return sendTime;
    }

    public void setSendTime(LocalDateTime sendTime) {
        this.sendTime = sendTime;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Map<String, Object> getExtendInfo() {
        return extendInfo;
    }

    public void setExtendInfo(Map<String, Object> extendInfo) {
        this.extendInfo = extendInfo;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }

    public Long getCost() {
        return cost;
    }

    public void setCost(Long cost) {
        this.cost = cost;
    }

    @Override
    public String toString() {
        return "NotificationSendResult{" +
                "success=" + success +
                ", messageId='" + messageId + '\'' +
                ", channelType='" + channelType + '\'' +
                ", recipient='" + recipient + '\'' +
                ", sendTime=" + sendTime +
                ", errorCode='" + errorCode + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", retryCount=" + retryCount +
                ", cost=" + cost +
                '}';
    }
}
