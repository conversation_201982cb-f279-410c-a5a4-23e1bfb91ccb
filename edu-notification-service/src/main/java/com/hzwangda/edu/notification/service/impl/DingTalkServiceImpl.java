package com.hzwangda.edu.notification.service.impl;

import com.hzwangda.edu.common.exception.BusinessException;
import com.hzwangda.edu.common.result.ResultCode;
import com.hzwangda.edu.notification.config.DingTalkConfig;
import com.hzwangda.edu.notification.entity.DingTalkRecord;
import com.hzwangda.edu.notification.entity.DingTalkUser;
import com.hzwangda.edu.notification.service.DingTalkService;
import com.hzwangda.edu.notification.service.channel.handler.ChannelHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 钉钉服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class DingTalkServiceImpl implements DingTalkService {

    @Autowired
    private DingTalkConfig dingTalkConfig;

    @Autowired
    @Qualifier("dingTalkChannelHandler")
    private ChannelHandler dingTalkChannelHandler;

    // 内存存储（实际项目中应该使用数据库）
    private final Map<String, DingTalkRecord> dingTalkRecords = new ConcurrentHashMap<>();
    private final Map<String, DingTalkUser> dingTalkUsers = new ConcurrentHashMap<>();
    private final Map<String, List<LocalDateTime>> sendLimitTracker = new ConcurrentHashMap<>();

    @Override
    public DingTalkRecord sendTextMessage(String toUser, String content, String dingTalkType) {
        log.info("发送钉钉文本消息: toUser={}, dingTalkType={}", toUser, dingTalkType);

        try {
            // 检查发送限制
            if (!checkSendLimit(toUser, dingTalkType)) {
                throw new BusinessException(ResultCode.TOO_MANY_REQUESTS, "钉钉消息发送频率超限");
            }

            // 创建发送记录
            DingTalkRecord record = DingTalkRecord.builder()
                    .id(UUID.randomUUID().toString())
                    .toUser(toUser)
                    .content(content)
                    .msgType("text")
                    .dingTalkType(dingTalkType)
                    .sendStatus("PENDING")
                    .sendTime(LocalDateTime.now())
                    .retryCount(0)
                    .maxRetryCount(3)
                    .createTime(LocalDateTime.now())
                    .build();

            // 调用钉钉渠道处理器发送
            boolean success = sendDingTalkMessage(record);

            if (success) {
                record.setSendStatus("SUCCESS");
                log.info("钉钉文本消息发送成功: recordId={}", record.getId());
            } else {
                record.setSendStatus("FAILED");
                log.error("钉钉文本消息发送失败: recordId={}", record.getId());
            }

            // 保存记录
            dingTalkRecords.put(record.getId(), record);

            // 记录发送限制
            recordSendLimit(toUser, dingTalkType);

            return record;

        } catch (Exception e) {
            log.error("钉钉文本消息发送异常: toUser={}, error={}", toUser, e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "钉钉文本消息发送失败: " + e.getMessage());
        }
    }

    @Override
    public DingTalkRecord sendMarkdownMessage(String toUser, String title, String content, String dingTalkType) {
        log.info("发送钉钉Markdown消息: toUser={}, title={}, dingTalkType={}", toUser, title, dingTalkType);

        try {
            // 检查发送限制
            if (!checkSendLimit(toUser, dingTalkType)) {
                throw new BusinessException(ResultCode.TOO_MANY_REQUESTS, "钉钉消息发送频率超限");
            }

            // 创建发送记录
            DingTalkRecord record = DingTalkRecord.builder()
                    .id(UUID.randomUUID().toString())
                    .toUser(toUser)
                    .title(title)
                    .content(content)
                    .msgType("markdown")
                    .dingTalkType(dingTalkType)
                    .sendStatus("PENDING")
                    .sendTime(LocalDateTime.now())
                    .retryCount(0)
                    .maxRetryCount(3)
                    .createTime(LocalDateTime.now())
                    .build();

            // 调用钉钉渠道处理器发送
            boolean success = sendDingTalkMessage(record);

            if (success) {
                record.setSendStatus("SUCCESS");
                log.info("钉钉Markdown消息发送成功: recordId={}, title={}", record.getId(), title);
            } else {
                record.setSendStatus("FAILED");
                log.error("钉钉Markdown消息发送失败: recordId={}, title={}", record.getId(), title);
            }

            // 保存记录
            dingTalkRecords.put(record.getId(), record);

            // 记录发送限制
            recordSendLimit(toUser, dingTalkType);

            return record;

        } catch (Exception e) {
            log.error("钉钉Markdown消息发送异常: toUser={}, title={}, error={}",
                     toUser, title, e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "钉钉Markdown消息发送失败: " + e.getMessage());
        }
    }

    @Override
    public DingTalkRecord sendActionCardMessage(String toUser, String title, String content,
                                              String actionUrl, String actionTitle, String dingTalkType) {
        log.info("发送钉钉ActionCard消息: toUser={}, title={}, dingTalkType={}", toUser, title, dingTalkType);

        try {
            // 检查发送限制
            if (!checkSendLimit(toUser, dingTalkType)) {
                throw new BusinessException(ResultCode.TOO_MANY_REQUESTS, "钉钉消息发送频率超限");
            }

            // 构建ActionCard内容
            String actionCardContent = buildActionCardContent(title, content, actionUrl, actionTitle);

            // 创建发送记录
            DingTalkRecord record = DingTalkRecord.builder()
                    .id(UUID.randomUUID().toString())
                    .toUser(toUser)
                    .title(title)
                    .content(actionCardContent)
                    .msgType("actionCard")
                    .dingTalkType(dingTalkType)
                    .sendStatus("PENDING")
                    .sendTime(LocalDateTime.now())
                    .retryCount(0)
                    .maxRetryCount(3)
                    .createTime(LocalDateTime.now())
                    .build();

            // 调用钉钉渠道处理器发送
            boolean success = sendDingTalkMessage(record);

            if (success) {
                record.setSendStatus("SUCCESS");
                log.info("钉钉ActionCard消息发送成功: recordId={}, title={}", record.getId(), title);
            } else {
                record.setSendStatus("FAILED");
                log.error("钉钉ActionCard消息发送失败: recordId={}, title={}", record.getId(), title);
            }

            // 保存记录
            dingTalkRecords.put(record.getId(), record);

            // 记录发送限制
            recordSendLimit(toUser, dingTalkType);

            return record;

        } catch (Exception e) {
            log.error("钉钉ActionCard消息发送异常: toUser={}, title={}, error={}",
                     toUser, title, e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "钉钉ActionCard消息发送失败: " + e.getMessage());
        }
    }

    @Override
    public DingTalkRecord sendLinkMessage(String toUser, String title, String content,
                                        String messageUrl, String picUrl, String dingTalkType) {
        log.info("发送钉钉链接消息: toUser={}, title={}, dingTalkType={}", toUser, title, dingTalkType);

        try {
            // 检查发送限制
            if (!checkSendLimit(toUser, dingTalkType)) {
                throw new BusinessException(ResultCode.TOO_MANY_REQUESTS, "钉钉消息发送频率超限");
            }

            // 构建链接消息内容
            String linkContent = buildLinkContent(title, content, messageUrl, picUrl);

            // 创建发送记录
            DingTalkRecord record = DingTalkRecord.builder()
                    .id(UUID.randomUUID().toString())
                    .toUser(toUser)
                    .title(title)
                    .content(linkContent)
                    .msgType("link")
                    .dingTalkType(dingTalkType)
                    .sendStatus("PENDING")
                    .sendTime(LocalDateTime.now())
                    .retryCount(0)
                    .maxRetryCount(3)
                    .createTime(LocalDateTime.now())
                    .build();

            // 调用钉钉渠道处理器发送
            boolean success = sendDingTalkMessage(record);

            if (success) {
                record.setSendStatus("SUCCESS");
                log.info("钉钉链接消息发送成功: recordId={}, title={}", record.getId(), title);
            } else {
                record.setSendStatus("FAILED");
                log.error("钉钉链接消息发送失败: recordId={}, title={}", record.getId(), title);
            }

            // 保存记录
            dingTalkRecords.put(record.getId(), record);

            // 记录发送限制
            recordSendLimit(toUser, dingTalkType);

            return record;

        } catch (Exception e) {
            log.error("钉钉链接消息发送异常: toUser={}, title={}, error={}",
                     toUser, title, e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "钉钉链接消息发送失败: " + e.getMessage());
        }
    }

    @Override
    public DingTalkRecord sendGroupMessage(String chatId, String content, String msgType, String dingTalkType) {
        log.info("发送钉钉群消息: chatId={}, msgType={}, dingTalkType={}", chatId, msgType, dingTalkType);

        try {
            // 创建发送记录
            DingTalkRecord record = DingTalkRecord.builder()
                    .id(UUID.randomUUID().toString())
                    .toUser(chatId)
                    .content(content)
                    .msgType(msgType)
                    .dingTalkType(dingTalkType)
                    .sendStatus("PENDING")
                    .sendTime(LocalDateTime.now())
                    .retryCount(0)
                    .maxRetryCount(3)
                    .createTime(LocalDateTime.now())
                    .build();

            // TODO: 实现群消息发送逻辑
            record.setSendStatus("SUCCESS");
            log.info("钉钉群消息发送成功: recordId={}", record.getId());

            // 保存记录
            dingTalkRecords.put(record.getId(), record);

            return record;

        } catch (Exception e) {
            log.error("钉钉群消息发送异常: chatId={}, error={}", chatId, e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "钉钉群消息发送失败: " + e.getMessage());
        }
    }

    @Override
    public List<DingTalkRecord> sendBatchMessage(List<String> toUsers, String content, String msgType, String dingTalkType) {
        log.info("批量发送钉钉消息: toUsers={}, msgType={}, dingTalkType={}", toUsers.size(), msgType, dingTalkType);

        return toUsers.stream()
                .map(toUser -> {
                    try {
                        if ("text".equals(msgType)) {
                            return sendTextMessage(toUser, content, dingTalkType);
                        } else if ("markdown".equals(msgType)) {
                            return sendMarkdownMessage(toUser, "批量通知", content, dingTalkType);
                        } else {
                            return sendTextMessage(toUser, content, dingTalkType);
                        }
                    } catch (Exception e) {
                        log.error("批量发送钉钉消息失败: toUser={}, error={}", toUser, e.getMessage());
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 调用钉钉渠道处理器发送消息
     */
    private boolean sendDingTalkMessage(DingTalkRecord record) {
        try {
            // TODO: 这里需要将DingTalkRecord转换为NotificationMessage
            // 然后调用DingTalkChannelHandler发送
            // 由于结构复杂，这里简化处理
            log.info("调用钉钉渠道处理器发送消息: recordId={}, msgType={}",
                    record.getId(), record.getMsgType());

            // 模拟发送成功
            record.setProviderMessageId("dingtalk_" + UUID.randomUUID().toString());
            return true;

        } catch (Exception e) {
            log.error("调用钉钉渠道处理器发送消息失败: {}", e.getMessage(), e);
            record.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 构建ActionCard内容
     */
    private String buildActionCardContent(String title, String content, String actionUrl, String actionTitle) {
        return String.format("{\"title\":\"%s\",\"text\":\"%s\",\"singleTitle\":\"%s\",\"singleURL\":\"%s\"}",
                title, content, actionTitle, actionUrl);
    }

    /**
     * 构建链接消息内容
     */
    private String buildLinkContent(String title, String content, String messageUrl, String picUrl) {
        return String.format("{\"title\":\"%s\",\"text\":\"%s\",\"messageUrl\":\"%s\",\"picUrl\":\"%s\"}",
                title, content, messageUrl, picUrl);
    }

    @Override
    public DingTalkUser getDingTalkUserInfo(String userId, String dingTalkType) {
        log.info("获取钉钉用户信息: userId={}, dingTalkType={}", userId, dingTalkType);

        try {
            // TODO: 调用钉钉API获取用户信息
            // 这里返回模拟数据
            DingTalkUser user = new DingTalkUser();
            user.setDingtalkUserId(userId);
            user.setName("用户" + userId);
            user.setMobile("138****" + userId.substring(Math.max(0, userId.length() - 4)));
            user.setActive(true);

            return user;

        } catch (Exception e) {
            log.error("获取钉钉用户信息失败: userId={}, dingTalkType={}, error={}",
                     userId, dingTalkType, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean bindDingTalkUser(String systemUserId, DingTalkUser dingTalkUser) {
        log.info("绑定钉钉用户: systemUserId={}, dingtalkUserId={}",
                systemUserId, dingTalkUser.getDingtalkUserId());

        try {
            String key = systemUserId + "_" + dingTalkUser.getDingtalkUserId();
            dingTalkUser.setUserId(systemUserId);
            dingTalkUsers.put(key, dingTalkUser);

            log.info("钉钉用户绑定成功: systemUserId={}, dingtalkUserId={}",
                    systemUserId, dingTalkUser.getDingtalkUserId());
            return true;

        } catch (Exception e) {
            log.error("绑定钉钉用户失败: systemUserId={}, error={}", systemUserId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean unbindDingTalkUser(String systemUserId, String dingTalkType) {
        log.info("解绑钉钉用户: systemUserId={}, dingTalkType={}", systemUserId, dingTalkType);

        try {
            // 查找并删除绑定关系
            String keyToRemove = dingTalkUsers.entrySet().stream()
                    .filter(entry -> systemUserId.equals(entry.getValue().getUserId()))
                    .map(Map.Entry::getKey)
                    .findFirst()
                    .orElse(null);

            if (keyToRemove != null) {
                DingTalkUser removed = dingTalkUsers.remove(keyToRemove);
                log.info("钉钉用户解绑成功: systemUserId={}, dingtalkUserId={}",
                        systemUserId, removed.getDingtalkUserId());
                return true;
            } else {
                log.warn("未找到要解绑的钉钉用户: systemUserId={}, dingTalkType={}", systemUserId, dingTalkType);
                return false;
            }

        } catch (Exception e) {
            log.error("解绑钉钉用户失败: systemUserId={}, error={}", systemUserId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public DingTalkUser getDingTalkUserBySystemUserId(String systemUserId, String dingTalkType) {
        return dingTalkUsers.values().stream()
                .filter(user -> systemUserId.equals(user.getUserId()))
                .findFirst()
                .orElse(null);
    }

    @Override
    public DingTalkRecord getDingTalkRecord(String recordId) {
        return dingTalkRecords.get(recordId);
    }

    @Override
    public List<DingTalkRecord> getDingTalkRecords(String toUser, String dingTalkType, String startTime, String endTime) {
        return dingTalkRecords.values().stream()
                .filter(record -> toUser == null || toUser.equals(record.getToUser()))
                .filter(record -> dingTalkType == null || dingTalkType.equals(record.getDingTalkType()))
                .collect(Collectors.toList());
    }

    @Override
    public boolean retryDingTalkMessage(String recordId) {
        log.info("重试钉钉消息: recordId={}", recordId);

        DingTalkRecord record = dingTalkRecords.get(recordId);
        if (record == null) {
            log.warn("钉钉消息记录不存在: recordId={}", recordId);
            return false;
        }

        if (record.getRetryCount() >= record.getMaxRetryCount()) {
            log.warn("钉钉消息重试次数已达上限: recordId={}, retryCount={}", recordId, record.getRetryCount());
            return false;
        }

        try {
            record.setRetryCount(record.getRetryCount() + 1);
            record.setSendStatus("PENDING");
            record.setSendTime(LocalDateTime.now());

            boolean success = sendDingTalkMessage(record);

            if (success) {
                record.setSendStatus("SUCCESS");
                log.info("钉钉消息重试发送成功: recordId={}", recordId);
            } else {
                record.setSendStatus("FAILED");
                record.setNextRetryTime(LocalDateTime.now().plusMinutes(5)); // 5分钟后重试
                log.error("钉钉消息重试发送失败: recordId={}", recordId);
            }

            dingTalkRecords.put(recordId, record);
            return success;

        } catch (Exception e) {
            log.error("钉钉消息重试发送异常: recordId={}, error={}", recordId, e.getMessage(), e);
            record.setSendStatus("FAILED");
            record.setErrorMessage(e.getMessage());
            dingTalkRecords.put(recordId, record);
            return false;
        }
    }

    @Override
    public Map<String, Object> getDingTalkStatistics(String dingTalkType, String startTime, String endTime) {
        Map<String, Object> statistics = new HashMap<>();

        List<DingTalkRecord> records = dingTalkRecords.values().stream()
                .filter(record -> dingTalkType == null || dingTalkType.equals(record.getDingTalkType()))
                .collect(Collectors.toList());

        long totalCount = records.size();
        long successCount = records.stream()
                .filter(r -> "SUCCESS".equals(r.getSendStatus()))
                .count();
        long failedCount = records.stream()
                .filter(r -> "FAILED".equals(r.getSendStatus()))
                .count();

        statistics.put("totalCount", totalCount);
        statistics.put("successCount", successCount);
        statistics.put("failedCount", failedCount);
        statistics.put("successRate", totalCount > 0 ? (double) successCount / totalCount * 100 : 0);

        return statistics;
    }

    @Override
    public boolean checkSendLimit(String toUser, String dingTalkType) {
        if (!StringUtils.hasText(toUser)) {
            return false;
        }

        String key = toUser + "_" + dingTalkType;
        List<LocalDateTime> sendTimes = sendLimitTracker.getOrDefault(key, new ArrayList<>());
        LocalDateTime now = LocalDateTime.now();

        // 清理过期记录
        sendTimes.removeIf(time -> time.isBefore(now.minusHours(1)));

        // 检查每分钟限制（最多10条）
        long countPerMinute = sendTimes.stream()
                .filter(time -> time.isAfter(now.minusMinutes(1)))
                .count();
        if (countPerMinute >= 10) {
            return false;
        }

        // 检查每小时限制（最多200条）
        long countPerHour = sendTimes.stream()
                .filter(time -> time.isAfter(now.minusHours(1)))
                .count();
        if (countPerHour >= 200) {
            return false;
        }

        return true;
    }

    @Override
    public String getAccessToken(String dingTalkType) {
        log.info("获取钉钉Access Token: dingTalkType={}", dingTalkType);

        try {
            if ("work".equals(dingTalkType)) {
                // 调用DingTalkChannelHandler获取Token
                return "mock_access_token_" + System.currentTimeMillis();
            }

            log.warn("不支持的钉钉类型: {}", dingTalkType);
            return null;

        } catch (Exception e) {
            log.error("获取钉钉Access Token失败: dingTalkType={}, error={}", dingTalkType, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String refreshAccessToken(String dingTalkType) {
        log.info("刷新钉钉Access Token: dingTalkType={}", dingTalkType);

        try {
            // 清除缓存，强制重新获取
            return getAccessToken(dingTalkType);

        } catch (Exception e) {
            log.error("刷新钉钉Access Token失败: dingTalkType={}, error={}", dingTalkType, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public String handleDingTalkCallback(Map<String, Object> callbackData, String dingTalkType) {
        log.info("处理钉钉回调: dingTalkType={}, data={}", dingTalkType, callbackData);

        try {
            // TODO: 根据不同的钉钉类型处理回调
            if ("work".equals(dingTalkType)) {
                // 处理工作通知回调
                return handleWorkCallback(callbackData);
            } else if ("robot".equals(dingTalkType)) {
                // 处理机器人回调
                return handleRobotCallback(callbackData);
            }

            return "success";

        } catch (Exception e) {
            log.error("处理钉钉回调失败: {}", e.getMessage(), e);
            return "error";
        }
    }

    @Override
    public boolean verifySignature(String signature, String timestamp, String nonce, String token) {
        try {
            String[] arr = {token, timestamp, nonce};
            Arrays.sort(arr);

            StringBuilder content = new StringBuilder();
            for (String s : arr) {
                content.append(s);
            }

            MessageDigest md = MessageDigest.getInstance("SHA-1");
            byte[] digest = md.digest(content.toString().getBytes());

            StringBuilder hexStr = new StringBuilder();
            for (byte b : digest) {
                String shaHex = Integer.toHexString(b & 0xFF);
                if (shaHex.length() < 2) {
                    hexStr.append(0);
                }
                hexStr.append(shaHex);
            }

            return hexStr.toString().equals(signature);

        } catch (Exception e) {
            log.error("验证钉钉签名失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String createChatGroup(String name, String owner, List<String> userList) {
        log.info("创建钉钉群聊: name={}, owner={}, userCount={}", name, owner, userList.size());

        try {
            // TODO: 调用钉钉API创建群聊
            String chatId = "chat_" + UUID.randomUUID().toString();
            log.info("钉钉群聊创建成功: name={}, chatId={}", name, chatId);
            return chatId;

        } catch (Exception e) {
            log.error("创建钉钉群聊失败: name={}, error={}", name, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean updateChatGroup(String chatId, String name, String owner,
                                 List<String> addUserList, List<String> delUserList) {
        log.info("更新钉钉群聊: chatId={}, name={}, addUsers={}, delUsers={}",
                chatId, name, addUserList != null ? addUserList.size() : 0,
                delUserList != null ? delUserList.size() : 0);

        try {
            // TODO: 调用钉钉API更新群聊
            log.info("钉钉群聊更新成功: chatId={}", chatId);
            return true;

        } catch (Exception e) {
            log.error("更新钉钉群聊失败: chatId={}, error={}", chatId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<DingTalkUser> getDepartmentUsers(String deptId, boolean fetchChild) {
        log.info("获取部门用户列表: deptId={}, fetchChild={}", deptId, fetchChild);

        try {
            // TODO: 调用钉钉API获取部门用户
            List<DingTalkUser> users = new ArrayList<>();

            // 返回模拟数据
            for (int i = 1; i <= 5; i++) {
                DingTalkUser user = new DingTalkUser();
                user.setDingtalkUserId("user" + i);
                user.setName("用户" + i);
                user.setMobile("138000000" + i);
                user.setActive(true);
                users.add(user);
            }

            log.info("获取部门用户列表成功: deptId={}, userCount={}", deptId, users.size());
            return users;

        } catch (Exception e) {
            log.error("获取部门用户列表失败: deptId={}, error={}", deptId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public DingTalkRecord sendWorkNotification(String agentId, List<String> userList,
                                             List<String> deptList, String content, String msgType) {
        log.info("发送钉钉工作通知: agentId={}, userCount={}, deptCount={}, msgType={}",
                agentId, userList != null ? userList.size() : 0,
                deptList != null ? deptList.size() : 0, msgType);

        try {
            // 创建发送记录
            DingTalkRecord record = DingTalkRecord.builder()
                    .id(UUID.randomUUID().toString())
                    .toUser(userList != null ? String.join(",", userList) : "")
                    .content(content)
                    .msgType(msgType)
                    .dingTalkType("work")
                    .sendStatus("PENDING")
                    .sendTime(LocalDateTime.now())
                    .retryCount(0)
                    .maxRetryCount(3)
                    .createTime(LocalDateTime.now())
                    .build();

            // TODO: 调用钉钉API发送工作通知
            record.setSendStatus("SUCCESS");
            record.setTaskId("task_" + UUID.randomUUID().toString());

            // 保存记录
            dingTalkRecords.put(record.getId(), record);

            log.info("钉钉工作通知发送成功: recordId={}, taskId={}", record.getId(), record.getTaskId());
            return record;

        } catch (Exception e) {
            log.error("发送钉钉工作通知失败: agentId={}, error={}", agentId, e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "发送钉钉工作通知失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getWorkNotificationResult(String taskId) {
        log.info("查询钉钉工作通知发送结果: taskId={}", taskId);

        try {
            // TODO: 调用钉钉API查询发送结果
            Map<String, Object> result = new HashMap<>();
            result.put("taskId", taskId);
            result.put("status", "SUCCESS");
            result.put("sendCount", 10);
            result.put("successCount", 9);
            result.put("failedCount", 1);

            return result;

        } catch (Exception e) {
            log.error("查询钉钉工作通知发送结果失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 处理工作通知回调
     */
    private String handleWorkCallback(Map<String, Object> callbackData) {
        log.info("处理钉钉工作通知回调: {}", callbackData);
        // TODO: 实现工作通知回调处理逻辑
        return "success";
    }

    /**
     * 处理机器人回调
     */
    private String handleRobotCallback(Map<String, Object> callbackData) {
        log.info("处理钉钉机器人回调: {}", callbackData);
        // TODO: 实现机器人回调处理逻辑
        return "success";
    }

    /**
     * 记录发送限制
     */
    private void recordSendLimit(String toUser, String dingTalkType) {
        String key = toUser + "_" + dingTalkType;
        List<LocalDateTime> sendTimes = sendLimitTracker.computeIfAbsent(key, k -> new ArrayList<>());
        sendTimes.add(LocalDateTime.now());

        // 清理过期记录（保留最近1小时的记录）
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        sendTimes.removeIf(time -> time.isBefore(oneHourAgo));
    }
}
