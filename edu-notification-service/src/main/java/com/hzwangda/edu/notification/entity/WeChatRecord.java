package com.hzwangda.edu.notification.entity;

import com.hzwangda.edu.common.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 微信消息发送记录实体
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "notification_wechat_record")
public class WeChatRecord extends BaseEntity {

    /**
     * 关联的通知消息ID
     */
    @Column(name = "message_id", length = 64)
    private String messageId;

    /**
     * 微信类型（work, service, mini）
     */
    @Column(name = "wechat_type", nullable = false, length = 20)
    private String wechatType;

    /**
     * 接收者OpenID
     */
    @Column(name = "to_user", nullable = false, length = 100)
    private String toUser;

    /**
     * 消息类型（text, image, voice, video, file, textcard, news, markdown）
     */
    @Column(name = "msg_type", nullable = false, length = 50)
    private String msgType;

    /**
     * 消息内容（JSON格式）
     */
    @Column(name = "content", nullable = false, columnDefinition = "TEXT")
    private String content;

    /**
     * 模板ID（模板消息使用）
     */
    @Column(name = "template_id", length = 100)
    private String templateId;

    /**
     * 模板数据（JSON格式）
     */
    @Column(name = "template_data", columnDefinition = "TEXT")
    private String templateData;

    /**
     * 跳转URL
     */
    @Column(name = "url", length = 500)
    private String url;

    /**
     * 小程序页面路径
     */
    @Column(name = "mini_program_page", length = 500)
    private String miniProgramPage;

    /**
     * 应用ID（企业微信使用）
     */
    @Column(name = "agent_id", length = 50)
    private String agentId;

    /**
     * 发送状态（PENDING, SENDING, SUCCESS, FAILED）
     */
    @Column(name = "send_status", nullable = false, length = 50)
    private String sendStatus;

    /**
     * 发送时间
     */
    @Column(name = "send_time")
    private LocalDateTime sendTime;

    /**
     * 微信返回的消息ID
     */
    @Column(name = "wechat_msg_id", length = 100)
    private String wechatMsgId;

    /**
     * 错误码
     */
    @Column(name = "error_code", length = 50)
    private String errorCode;

    /**
     * 错误信息
     */
    @Column(name = "error_message", length = 500)
    private String errorMessage;

    /**
     * 重试次数
     */
    @Column(name = "retry_count")
    private Integer retryCount = 0;

    /**
     * 最大重试次数
     */
    @Column(name = "max_retry_count")
    private Integer maxRetryCount = 3;

    /**
     * 下次重试时间
     */
    @Column(name = "next_retry_time")
    private LocalDateTime nextRetryTime;

    /**
     * 是否安全模式
     */
    @Column(name = "safe_mode")
    private Boolean safeMode = false;

    /**
     * 是否开启ID转译
     */
    @Column(name = "enable_id_trans")
    private Boolean enableIdTrans = false;

    /**
     * 是否开启重复消息检查
     */
    @Column(name = "enable_duplicate_check")
    private Boolean enableDuplicateCheck = false;

    /**
     * 重复消息检查间隔（秒）
     */
    @Column(name = "duplicate_check_interval")
    private Integer duplicateCheckInterval = 1800;

    /**
     * 扩展信息（JSON格式）
     */
    @Column(name = "extra_info", columnDefinition = "TEXT")
    private String extraInfo;

    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
}
