package com.hzwangda.edu.notification.service.channel.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hzwangda.edu.notification.config.DingTalkConfig;
import com.hzwangda.edu.notification.entity.NotificationMessage;
import com.hzwangda.edu.notification.enums.ChannelType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 钉钉通知渠道处理器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@Component
public class DingTalkChannelHandler implements ChannelHandler {

    @Autowired
    private DingTalkConfig dingTalkConfig;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    // 访问令牌缓存
    private final Map<String, String> accessTokenCache = new ConcurrentHashMap<>();
    private final Map<String, LocalDateTime> tokenExpireCache = new ConcurrentHashMap<>();

    // 钉钉API地址
    private static final String DINGTALK_ROBOT_API = "https://oapi.dingtalk.com/robot/send";
    private static final String DINGTALK_TOKEN_API = "https://oapi.dingtalk.com/gettoken";
    private static final String DINGTALK_WORK_MESSAGE_API = "https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2";

    @Override
    public boolean send(NotificationMessage message) {
        log.info("开始发送钉钉通知: messageId={}, recipient={}", message.getId(), message.getRecipient());

        try {
            // 检查钉钉功能是否启用
            if (!dingTalkConfig.isEnabled()) {
                log.warn("钉钉功能未启用，跳过发送: messageId={}", message.getId());
                return false;
            }

            // 解析接收者信息
            String recipient = extractRecipient(message.getRecipient());
            if (!StringUtils.hasText(recipient)) {
                log.error("无效的接收者信息: {}", message.getRecipient());
                return false;
            }

            // 根据钉钉类型发送消息
            String dingTalkType = dingTalkConfig.getType();
            boolean success = false;

            switch (dingTalkType) {
                case "robot":
                    success = sendRobotMessage(message, recipient);
                    break;
                case "work":
                    success = sendWorkNotification(message, recipient);
                    break;
                default:
                    log.error("不支持的钉钉类型: {}", dingTalkType);
                    return false;
            }

            if (success) {
                log.info("钉钉发送成功: messageId={}", message.getId());
            } else {
                log.error("钉钉发送失败: messageId={}", message.getId());
            }

            return success;

        } catch (Exception e) {
            log.error("钉钉发送异常: messageId={}, error={}", message.getId(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Async("notificationTaskExecutor")
    public void sendAsync(NotificationMessage message) {
        log.info("异步发送钉钉通知: messageId={}", message.getId());

        try {
            boolean success = send(message);

            if (success) {
                log.info("异步钉钉发送成功: messageId={}", message.getId());
                // TODO: 更新消息状态为SUCCESS
            } else {
                log.error("异步钉钉发送失败: messageId={}", message.getId());
                // TODO: 更新消息状态为FAILED，可能需要重试
            }

        } catch (Exception e) {
            log.error("异步钉钉发送异常: messageId={}, error={}", message.getId(), e.getMessage(), e);
            // TODO: 更新消息状态为FAILED
        }
    }

    @Override
    public boolean isAvailable() {
        try {
            if (!dingTalkConfig.isEnabled()) {
                return false;
            }

            String type = dingTalkConfig.getType();
            switch (type) {
                case "robot":
                    DingTalkConfig.DingTalkRobotConfig robot = dingTalkConfig.getRobot();
                    return StringUtils.hasText(robot.getWebhookUrl()) &&
                           StringUtils.hasText(robot.getAccessToken());

                case "work":
                    DingTalkConfig.DingTalkWorkConfig work = dingTalkConfig.getWork();
                    return StringUtils.hasText(work.getCorpId()) &&
                           StringUtils.hasText(work.getAppKey()) &&
                           StringUtils.hasText(work.getAppSecret()) &&
                           StringUtils.hasText(work.getAgentId());

                default:
                    return false;
            }

        } catch (Exception e) {
            log.error("检查钉钉渠道可用性失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getStatus() {
        if (!dingTalkConfig.isEnabled()) {
            return "钉钉功能已禁用";
        }

        if (!isAvailable()) {
            return "钉钉配置不完整";
        }

        return "钉钉渠道正常";
    }

    @Override
    public String getSupportedChannelType() {
        return ChannelType.DINGTALK.getCode();
    }

    /**
     * 发送机器人消息
     */
    private boolean sendRobotMessage(NotificationMessage message, String recipient) {
        log.info("发送钉钉机器人消息: messageId={}", message.getMessageId());

        try {
            DingTalkConfig.DingTalkRobotConfig robotConfig = dingTalkConfig.getRobot();

            // 构建消息内容
            Map<String, Object> messageData = buildRobotMessage(message);

            // 构建请求URL（包含签名）
            String webhookUrl = buildRobotWebhookUrl(robotConfig);

            // 发送HTTP请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(messageData, headers);

            ResponseEntity<Map> response = restTemplate.postForEntity(webhookUrl, requestEntity, Map.class);

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                Integer errcode = (Integer) responseBody.get("errcode");
                String errmsg = (String) responseBody.get("errmsg");

                if (errcode != null && errcode == 0) {
                    log.info("钉钉机器人消息发送成功: messageId={}, webhook={}",
                            message.getMessageId(), robotConfig.getWebhookUrl());
                    return true;
                } else {
                    log.error("钉钉机器人消息发送失败: messageId={}, errcode={}, errmsg={}",
                            message.getMessageId(), errcode, errmsg);
                    return false;
                }
            } else {
                log.error("钉钉机器人API调用失败: messageId={}, status={}",
                        message.getMessageId(), response.getStatusCode());
                return false;
            }

        } catch (Exception e) {
            log.error("发送钉钉机器人消息异常: messageId={}, error={}",
                    message.getMessageId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送工作通知
     */
    private boolean sendWorkNotification(NotificationMessage message, String recipient) {
        log.info("发送钉钉工作通知: messageId={}, recipient={}", message.getMessageId(), recipient);

        try {
            DingTalkConfig.DingTalkWorkConfig workConfig = dingTalkConfig.getWork();

            // 获取访问令牌
            String accessToken = getAccessToken(workConfig);
            if (!StringUtils.hasText(accessToken)) {
                log.error("获取钉钉访问令牌失败: messageId={}", message.getMessageId());
                return false;
            }

            // 构建工作通知内容
            Map<String, Object> notificationData = buildWorkNotification(message, recipient);

            // 构建请求URL
            String apiUrl = DINGTALK_WORK_MESSAGE_API + "?access_token=" + accessToken;

            // 发送HTTP请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(notificationData, headers);

            ResponseEntity<Map> response = restTemplate.postForEntity(apiUrl, requestEntity, Map.class);

            // 处理响应
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                Integer errcode = (Integer) responseBody.get("errcode");
                String errmsg = (String) responseBody.get("errmsg");

                if (errcode != null && errcode == 0) {
                    String taskId = (String) responseBody.get("task_id");
                    log.info("钉钉工作通知发送成功: messageId={}, taskId={}, recipient={}",
                            message.getMessageId(), taskId, recipient);

                    // 保存任务ID
                    if (StringUtils.hasText(taskId)) {
                        message.setMessageId(taskId);
                    }

                    return true;
                } else {
                    log.error("钉钉工作通知发送失败: messageId={}, errcode={}, errmsg={}",
                            message.getMessageId(), errcode, errmsg);
                    return false;
                }
            } else {
                log.error("钉钉工作通知API调用失败: messageId={}, status={}",
                        message.getMessageId(), response.getStatusCode());
                return false;
            }

        } catch (Exception e) {
            log.error("发送钉钉工作通知异常: messageId={}, error={}",
                    message.getMessageId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 构建机器人消息
     */
    private Map<String, Object> buildRobotMessage(NotificationMessage message) {
        Map<String, Object> messageData = new HashMap<>();

        // 确定消息类型
        String msgType = determineRobotMessageType(message.getMessageContent());
        messageData.put("msgtype", msgType);

        switch (msgType) {
            case "text":
                Map<String, Object> textContent = new HashMap<>();
                textContent.put("content", message.getMessageContent());
                messageData.put("text", textContent);
                break;

            case "markdown":
                Map<String, Object> markdownContent = new HashMap<>();
                markdownContent.put("title", message.getMessageTitle() != null ? message.getMessageTitle() : "通知");
                markdownContent.put("text", message.getMessageContent());
                messageData.put("markdown", markdownContent);
                break;

            case "actionCard":
                Map<String, Object> actionCardContent = buildActionCard(message);
                messageData.put("actionCard", actionCardContent);
                break;

            default:
                // 默认文本消息
                Map<String, Object> defaultContent = new HashMap<>();
                defaultContent.put("content", message.getMessageContent());
                messageData.put("text", defaultContent);
                messageData.put("msgtype", "text");
        }

        // 添加@信息
        Map<String, Object> atInfo = buildAtInfo();
        messageData.put("at", atInfo);

        return messageData;
    }

    /**
     * 构建工作通知
     */
    private Map<String, Object> buildWorkNotification(NotificationMessage message, String recipient) {
        Map<String, Object> notificationData = new HashMap<>();

        notificationData.put("agent_id", dingTalkConfig.getWork().getAgentId());
        notificationData.put("userid_list", recipient);

        // 构建消息内容
        Map<String, Object> msg = new HashMap<>();

        // 确定消息类型
        String msgType = determineWorkMessageType(message.getMessageContent());
        msg.put("msgtype", msgType);

        switch (msgType) {
            case "text":
                Map<String, Object> textContent = new HashMap<>();
                textContent.put("content", message.getMessageContent());
                msg.put("text", textContent);
                break;

            case "markdown":
                Map<String, Object> markdownContent = new HashMap<>();
                markdownContent.put("title", message.getMessageTitle() != null ? message.getMessageTitle() : "通知");
                markdownContent.put("text", message.getMessageContent());
                msg.put("markdown", markdownContent);
                break;

            case "action_card":
                Map<String, Object> actionCardContent = buildWorkActionCard(message);
                msg.put("action_card", actionCardContent);
                break;

            default:
                // 默认文本消息
                Map<String, Object> defaultContent = new HashMap<>();
                defaultContent.put("content", message.getMessageContent());
                msg.put("text", defaultContent);
                msg.put("msgtype", "text");
        }

        notificationData.put("msg", msg);

        return notificationData;
    }

    /**
     * 构建ActionCard
     */
    private Map<String, Object> buildActionCard(NotificationMessage message) {
        Map<String, Object> actionCard = new HashMap<>();

        actionCard.put("title", message.getMessageTitle() != null ? message.getMessageTitle() : "通知");
        actionCard.put("text", message.getMessageContent());
        actionCard.put("singleTitle", "查看详情");
        actionCard.put("singleURL", ""); // 可以从消息扩展属性中获取

        return actionCard;
    }

    /**
     * 构建工作通知ActionCard
     */
    private Map<String, Object> buildWorkActionCard(NotificationMessage message) {
        Map<String, Object> actionCard = new HashMap<>();

        actionCard.put("title", message.getMessageTitle() != null ? message.getMessageTitle() : "通知");
        actionCard.put("markdown", message.getMessageContent());
        actionCard.put("single_title", "查看详情");
        actionCard.put("single_url", ""); // 可以从消息扩展属性中获取

        return actionCard;
    }

    /**
     * 构建@信息
     */
    private Map<String, Object> buildAtInfo() {
        Map<String, Object> atInfo = new HashMap<>();

        DingTalkConfig.DingTalkRobotConfig robotConfig = dingTalkConfig.getRobot();

        atInfo.put("isAtAll", robotConfig.isAtAll());

        if (robotConfig.getAtMobiles() != null && robotConfig.getAtMobiles().length > 0) {
            atInfo.put("atMobiles", robotConfig.getAtMobiles());
        }

        if (robotConfig.getAtUserIds() != null && robotConfig.getAtUserIds().length > 0) {
            atInfo.put("atUserIds", robotConfig.getAtUserIds());
        }

        return atInfo;
    }

    /**
     * 确定机器人消息类型
     */
    private String determineRobotMessageType(String content) {
        if (!StringUtils.hasText(content)) {
            return "text";
        }

        // 简单判断内容类型
        if (content.contains("##") || content.contains("**") || content.contains("[") && content.contains("](")) {
            return "markdown";
        }

        if (content.startsWith("{") && content.contains("\"title\"")) {
            return "actionCard";
        }

        return "text";
    }

    /**
     * 确定工作通知消息类型
     */
    private String determineWorkMessageType(String content) {
        if (!StringUtils.hasText(content)) {
            return "text";
        }

        // 简单判断内容类型
        if (content.contains("##") || content.contains("**") || content.contains("[") && content.contains("](")) {
            return "markdown";
        }

        if (content.startsWith("{") && content.contains("\"title\"")) {
            return "action_card";
        }

        return "text";
    }

    /**
     * 从收件人信息中提取接收者
     */
    private String extractRecipient(String recipient) {
        if (!StringUtils.hasText(recipient)) {
            return null;
        }

        // 如果是JSON格式，解析接收者信息
        if (recipient.startsWith("{") && recipient.endsWith("}")) {
            try {
                // TODO: 解析JSON格式的收件人信息
                // 这里简化处理，实际应该解析JSON
                return recipient;
            } catch (Exception e) {
                log.warn("解析收件人JSON失败: {}", recipient);
                return null;
            }
        }

        // 直接返回接收者
        return recipient;
    }

    /**
     * 构建机器人Webhook URL（包含签名）
     */
    private String buildRobotWebhookUrl(DingTalkConfig.DingTalkRobotConfig robotConfig) throws Exception {
        String webhookUrl = robotConfig.getWebhookUrl();
        String accessToken = robotConfig.getAccessToken();
        String secret = robotConfig.getSecret();

        // 如果没有配置密钥，直接返回带access_token的URL
        if (!StringUtils.hasText(secret)) {
            return webhookUrl + "?access_token=" + accessToken;
        }

        // 计算签名
        long timestamp = System.currentTimeMillis();
        String stringToSign = timestamp + "\n" + secret;

        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
        String sign = URLEncoder.encode(Base64.getEncoder().encodeToString(signData), StandardCharsets.UTF_8);

        return webhookUrl + "?access_token=" + accessToken + "&timestamp=" + timestamp + "&sign=" + sign;
    }

    /**
     * 获取访问令牌
     */
    private String getAccessToken(DingTalkConfig.DingTalkWorkConfig workConfig) {
        String cacheKey = workConfig.getAppKey();

        // 检查缓存
        String cachedToken = accessTokenCache.get(cacheKey);
        LocalDateTime expireTime = tokenExpireCache.get(cacheKey);

        if (StringUtils.hasText(cachedToken) && expireTime != null && expireTime.isAfter(LocalDateTime.now())) {
            return cachedToken;
        }

        try {
            // 请求新的访问令牌
            String tokenUrl = DINGTALK_TOKEN_API + "?appkey=" + workConfig.getAppKey() + "&appsecret=" + workConfig.getAppSecret();

            ResponseEntity<Map> response = restTemplate.getForEntity(tokenUrl, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseBody = response.getBody();
                Integer errcode = (Integer) responseBody.get("errcode");

                if (errcode != null && errcode == 0) {
                    String accessToken = (String) responseBody.get("access_token");
                    Integer expiresIn = (Integer) responseBody.get("expires_in");

                    // 缓存令牌（提前5分钟过期）
                    accessTokenCache.put(cacheKey, accessToken);
                    tokenExpireCache.put(cacheKey, LocalDateTime.now().plusSeconds(expiresIn - 300));

                    log.info("获取钉钉访问令牌成功: appKey={}, expiresIn={}", workConfig.getAppKey(), expiresIn);
                    return accessToken;
                } else {
                    String errmsg = (String) responseBody.get("errmsg");
                    log.error("获取钉钉访问令牌失败: appKey={}, errcode={}, errmsg={}",
                            workConfig.getAppKey(), errcode, errmsg);
                }
            }

        } catch (Exception e) {
            log.error("获取钉钉访问令牌异常: appKey={}, error={}", workConfig.getAppKey(), e.getMessage(), e);
        }

        return null;
    }

    /**
     * 清理过期的访问令牌缓存
     */
    public void cleanupExpiredTokens() {
        LocalDateTime now = LocalDateTime.now();
        tokenExpireCache.entrySet().removeIf(entry -> entry.getValue().isBefore(now));

        // 同时清理对应的访问令牌
        tokenExpireCache.keySet().forEach(key -> {
            if (!tokenExpireCache.containsKey(key)) {
                accessTokenCache.remove(key);
            }
        });
    }

    /**
     * 获取发送统计信息
     */
    public Map<String, Object> getSendStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("tokenCacheSize", accessTokenCache.size());
        stats.put("type", dingTalkConfig.getType());
        stats.put("enabled", dingTalkConfig.isEnabled());

        if ("robot".equals(dingTalkConfig.getType())) {
            DingTalkConfig.DingTalkRobotConfig robot = dingTalkConfig.getRobot();
            stats.put("robotWebhook", robot.getWebhookUrl());
            stats.put("robotAtAll", robot.isAtAll());
        } else if ("work".equals(dingTalkConfig.getType())) {
            DingTalkConfig.DingTalkWorkConfig work = dingTalkConfig.getWork();
            stats.put("workCorpId", work.getCorpId());
            stats.put("workAgentId", work.getAgentId());
        }

        return stats;
    }
}
