# 通知服务配置
hky:
  notification:
    # 消息重试配置
    retry:
      enabled: true
      default-strategy:
        max-retry-count: 3
        initial-delay-seconds: 60
        max-delay-seconds: 1800
        delay-multiplier: 2.0
        strategy-type: EXPONENTIAL
        enable-jitter: true
        jitter-factor: 0.1
        retryable-error-codes:
          - NETWORK_ERROR
          - TIMEOUT
          - RATE_LIMIT
          - TEMPORARY_FAILURE
        non-retryable-error-codes:
          - INVALID_RECIPIENT
          - INVALID_CONTENT
          - PERMISSION_DENIED
      
      # 按渠道配置重试策略
      channel-strategies:
        EMAIL:
          max-retry-count: 5
          initial-delay-seconds: 120
          max-delay-seconds: 3600
        SMS:
          max-retry-count: 3
          initial-delay-seconds: 60
          max-delay-seconds: 900
        WECHAT:
          max-retry-count: 3
          initial-delay-seconds: 30
          max-delay-seconds: 600
        DINGTALK:
          max-retry-count: 3
          initial-delay-seconds: 30
          max-delay-seconds: 600
        INTERNAL:
          max-retry-count: 2
          initial-delay-seconds: 10
          max-delay-seconds: 300
      
      # 死信队列配置
      dead-letter:
        enabled: true
        retention-days: 7
        max-messages: 10000
        auto-cleanup: true
        handle-strategy: STORE
      
      # 调度配置
      schedule:
        scan-interval-seconds: 30
        batch-size: 100
        thread-pool-size: 5
        enable-distributed-lock: true
        lock-timeout-seconds: 300

    # 短信配置
    sms:
      enabled: false
      provider: aliyun
      aliyun:
        access-key-id: ${SMS_ALIYUN_ACCESS_KEY_ID:}
        access-key-secret: ${SMS_ALIYUN_ACCESS_KEY_SECRET:}
        sign-name: ${SMS_ALIYUN_SIGN_NAME:}
        endpoint: dysmsapi.aliyuncs.com
        region-id: cn-hangzhou
      tencent:
        secret-id: ${SMS_TENCENT_SECRET_ID:}
        secret-key: ${SMS_TENCENT_SECRET_KEY:}
        sdk-app-id: ${SMS_TENCENT_SDK_APP_ID:}
        sign-name: ${SMS_TENCENT_SIGN_NAME:}
        endpoint: sms.tencentcloudapi.com
        region: ap-beijing
      huawei:
        access-key-id: ${SMS_HUAWEI_ACCESS_KEY_ID:}
        secret-access-key: ${SMS_HUAWEI_SECRET_ACCESS_KEY:}
        sender: ${SMS_HUAWEI_SENDER:}
        endpoint: https://smsapi.cn-north-4.myhuaweicloud.com:443
        region: cn-north-4
      limit:
        max-per-minute: 10
        max-per-hour: 100
        max-per-day: 1000
        max-per-phone-per-minute: 1
        max-per-phone-per-hour: 5
        max-per-phone-per-day: 20

    # 微信配置
    wechat:
      enabled: false
      type: work
      work:
        corp-id: ${WECHAT_WORK_CORP_ID:}
        corp-secret: ${WECHAT_WORK_CORP_SECRET:}
        agent-id: ${WECHAT_WORK_AGENT_ID:}
        api-url: https://qyapi.weixin.qq.com
        token-url: /cgi-bin/gettoken
        send-message-url: /cgi-bin/message/send
        upload-media-url: /cgi-bin/media/upload
        get-user-info-url: /cgi-bin/user/get
      service:
        app-id: ${WECHAT_SERVICE_APP_ID:}
        app-secret: ${WECHAT_SERVICE_APP_SECRET:}
        api-url: https://api.weixin.qq.com
        token-url: /cgi-bin/token
        send-template-url: /cgi-bin/message/template/send
        get-user-info-url: /cgi-bin/user/info
      mini:
        app-id: ${WECHAT_MINI_APP_ID:}
        app-secret: ${WECHAT_MINI_APP_SECRET:}
        api-url: https://api.weixin.qq.com
        token-url: /cgi-bin/token
        send-subscribe-url: /cgi-bin/message/subscribe/send
      limit:
        max-per-minute: 20
        max-per-hour: 200
        max-per-day: 2000

    # 钉钉配置
    dingtalk:
      enabled: false
      type: work
      robot:
        webhook-url: ${DINGTALK_ROBOT_WEBHOOK_URL:}
        secret: ${DINGTALK_ROBOT_SECRET:}
        access-token: ${DINGTALK_ROBOT_ACCESS_TOKEN:}
        enable-sign: true
        at-all: false
        keywords: []
        whitelist-ips: []
        at-mobiles: []
        at-user-ids: []
      work:
        corp-id: ${DINGTALK_WORK_CORP_ID:}
        app-key: ${DINGTALK_WORK_APP_KEY:}
        app-secret: ${DINGTALK_WORK_APP_SECRET:}
        agent-id: ${DINGTALK_WORK_AGENT_ID:}
        api-url: https://oapi.dingtalk.com
        token-url: /gettoken
        send-work-notice-url: /topapi/message/corpconversation/asyncsend_v2
        get-user-info-url: /topapi/v2/user/get
        get-dept-users-url: /topapi/user/simplelist
        send-group-message-url: /robot/send
      limit:
        max-per-minute: 20
        max-per-hour: 200
        max-per-day: 2000
        robot-max-per-minute: 20

# Spring配置
spring:
  task:
    scheduling:
      pool:
        size: 10
    execution:
      pool:
        core-size: 5
        max-size: 20
        queue-capacity: 100

# 日志配置
logging:
  level:
    com.hky.hr.notification: DEBUG
    org.springframework.scheduling: INFO
