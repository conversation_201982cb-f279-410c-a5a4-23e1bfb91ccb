package com.hzwangda.edu.notification.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.hzwangda.edu.notification.NotificationServiceApplication;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import javax.sql.DataSource;

/**
 * 通知服务集成测试基础类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(
    classes = NotificationServiceApplication.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Testcontainers
@Transactional
public abstract class BaseNotificationIntegrationTest {

    @LocalServerPort
    protected int port;

    @Autowired
    protected MockMvc mockMvc;

    @Autowired
    protected TestRestTemplate restTemplate;

    @Autowired
    protected ObjectMapper objectMapper;

    @Autowired
    protected DataSource dataSource;

    // TestContainers配置
    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15-alpine")
            .withDatabaseName("hky_notification_test")
            .withUsername("test")
            .withPassword("test")
            .withReuse(true);

    @Container
    static GenericContainer<?> redis = new GenericContainer<>("redis:7-alpine")
            .withExposedPorts(6379)
            .withReuse(true);

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        // PostgreSQL配置
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.datasource.driver-class-name", () -> "org.postgresql.Driver");

        // Redis配置
        registry.add("spring.data.redis.host", redis::getHost);
        registry.add("spring.data.redis.port", redis::getFirstMappedPort);

        // JPA配置
        registry.add("spring.jpa.hibernate.ddl-auto", () -> "create-drop");
        registry.add("spring.jpa.show-sql", () -> "true");
        registry.add("spring.jpa.properties.hibernate.format_sql", () -> "true");

        // 测试环境特定配置
        registry.add("logging.level.com.hky.hr", () -> "DEBUG");

        // 通知渠道测试配置
        registry.add("notification.sms.enabled", () -> "true");
        registry.add("notification.sms.accessKeyId", () -> "test_access_key");
        registry.add("notification.sms.accessKeySecret", () -> "test_access_secret");
        registry.add("notification.sms.signName", () -> "测试签名");
        registry.add("notification.sms.templateCode", () -> "SMS_TEST_001");

        registry.add("notification.wechat.enabled", () -> "true");
        registry.add("notification.wechat.corpId", () -> "test_corp_id");
        registry.add("notification.wechat.corpSecret", () -> "test_corp_secret");
        registry.add("notification.wechat.agentId", () -> "test_agent_id");

        registry.add("notification.dingtalk.enabled", () -> "true");
        registry.add("notification.dingtalk.appKey", () -> "test_app_key");
        registry.add("notification.dingtalk.appSecret", () -> "test_app_secret");
        registry.add("notification.dingtalk.agentId", () -> "test_agent_id");
    }

    @BeforeEach
    void setUp() {
        // 设置基础URL
        restTemplate = restTemplate.withBasicAuth("test", "test");
    }

    /**
     * 获取完整的API URL
     */
    protected String getApiUrl(String path) {
        return "http://localhost:" + port + "/api/v1" + path;
    }

    /**
     * 创建JSON请求体
     */
    protected String toJson(Object object) throws Exception {
        return objectMapper.writeValueAsString(object);
    }

    /**
     * 解析JSON响应
     */
    protected <T> T fromJson(String json, Class<T> clazz) throws Exception {
        return objectMapper.readValue(json, clazz);
    }

    /**
     * 等待异步操作完成
     */
    protected void waitForAsyncOperation(long milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 验证数据库连接
     */
    protected boolean isDatabaseConnected() {
        try {
            return dataSource.getConnection().isValid(5);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 清理测试数据
     */
    protected void cleanupTestData() {
        // 子类可以重写此方法来清理特定的测试数据
    }
}
