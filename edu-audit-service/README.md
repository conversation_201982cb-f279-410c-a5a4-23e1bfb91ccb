# 业务管理系统 - 审计日志服务

## 📋 服务概述

审计日志服务是业务管理系统的核心平台服务之一，负责记录、查询、分析和管理系统中所有用户的关键操作，确保系统操作的全程可追溯性和安全审计要求。

## 🎯 核心功能

### 📝 日志记录功能
- **全程记录**：记录所有用户在系统中的关键操作（登录、登出、数据新增、修改、删除、审批、导入、导出、查询、敏感数据访问等）
- **详细信息**：记录操作人、操作时间、操作类型、操作对象、操作结果、IP地址等详细信息
- **异步记录**：支持异步日志记录，不影响业务性能
- **批量记录**：支持批量记录多个审计日志

### 🔍 日志查询功能
- **多维度查询**：支持按用户、时间、操作类型、业务模块、IP地址等多维度查询
- **分页查询**：支持大数据量的分页查询
- **关键词搜索**：支持在操作描述中进行关键词搜索
- **操作历史**：查询用户操作历史和对象操作历史

### 📊 统计分析功能
- **基础统计**：总操作次数、成功/失败操作数、高风险操作数、活跃用户数
- **维度统计**：按操作类型、业务模块、操作结果、风险级别统计
- **时间统计**：按小时、日期统计操作趋势
- **TOP统计**：最活跃用户TOP10、最频繁IP地址TOP10
- **风险分析**：高风险操作识别和分析

### 📤 导出功能
- **Excel导出**：支持将查询结果导出为Excel文件
- **格式化输出**：提供美观的Excel格式，包含完整的审计信息
- **导出限制**：防止大数据量导出影响系统性能

### 🔒 安全功能
- **日志不可篡改**：确保审计日志的完整性和不可篡改性
- **风险评估**：自动评估操作风险级别
- **完整性验证**：提供日志完整性验证功能
- **过期清理**：支持过期日志的安全清理

## 🏗️ 技术架构

### 技术栈
- **框架**：Spring Boot 3.0
- **数据库**：PostgreSQL（独立存储）
- **缓存**：Redis
- **安全**：Spring Security
- **文档**：SpringDoc OpenAPI 3
- **导出**：Apache POI

### 架构特点
- **微服务架构**：独立部署，松耦合
- **异步处理**：支持异步日志记录
- **缓存优化**：Redis缓存提升查询性能
- **分页查询**：支持大数据量分页
- **安全防护**：完善的权限控制和安全机制

## 📊 数据模型

### 审计日志表 (audit_log)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| user_id | BIGINT | 操作用户ID |
| username | VARCHAR(64) | 操作用户名 |
| real_name | VARCHAR(64) | 操作用户真实姓名 |
| operation_time | TIMESTAMP | 操作时间 |
| operation_type | VARCHAR(32) | 操作类型 |
| module | VARCHAR(64) | 业务模块 |
| operation_object | VARCHAR(128) | 操作对象 |
| object_id | VARCHAR(64) | 操作对象ID |
| operation_result | VARCHAR(16) | 操作结果 |
| operation_description | VARCHAR(255) | 操作描述 |
| ip_address | VARCHAR(45) | IP地址 |
| user_agent | VARCHAR(255) | 用户代理 |
| request_uri | VARCHAR(128) | 请求URI |
| request_method | VARCHAR(16) | 请求方法 |
| request_params | TEXT | 请求参数 |
| response_data | TEXT | 响应数据 |
| execution_time | BIGINT | 执行时间(毫秒) |
| error_message | VARCHAR(500) | 错误信息 |
| session_id | VARCHAR(64) | 会话ID |
| risk_level | VARCHAR(32) | 风险级别 |
| create_time | TIMESTAMP | 创建时间 |
| create_by | VARCHAR(64) | 创建人 |
| update_time | TIMESTAMP | 更新时间 |
| update_by | VARCHAR(64) | 更新人 |
| deleted | BOOLEAN | 删除标记 |

### 索引设计
- `idx_audit_user_id`：用户ID索引
- `idx_audit_operation_time`：操作时间索引
- `idx_audit_operation_type`：操作类型索引
- `idx_audit_module`：业务模块索引
- `idx_audit_ip_address`：IP地址索引
- `idx_audit_operation_result`：操作结果索引

## 🚀 API接口

### 核心接口

#### 1. 记录审计日志
```http
POST /api/v1/audit/logs

请求体:
{
  "userId": 1,
  "username": "admin",
  "realName": "管理员",
  "operationType": "CREATE",
  "module": "EMPLOYEE",
  "operationObject": "员工信息",
  "objectId": "E001",
  "operationResult": "SUCCESS",
  "operationDescription": "创建新员工",
  "ipAddress": "*************",
  "requestUri": "/api/v1/employees",
  "requestMethod": "POST"
}
```

#### 2. 分页查询审计日志
```http
GET /api/v1/audit/logs?page=1&size=20&operationType=CREATE&module=EMPLOYEE

响应:
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "data": [...],
    "total": 100,
    "page": 1,
    "size": 20
  }
}
```

#### 3. 获取审计统计信息
```http
GET /api/v1/audit/statistics?startTime=2024-01-01T00:00:00&endTime=2024-01-31T23:59:59

响应:
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalOperations": 10000,
    "successOperations": 9500,
    "failedOperations": 500,
    "highRiskOperations": 100,
    "activeUsers": 50,
    "operationTypeStats": {...},
    "moduleStats": {...}
  }
}
```

#### 4. 导出审计日志
```http
GET /api/v1/audit/logs/export?startTime=2024-01-01T00:00:00&endTime=2024-01-31T23:59:59

响应: Excel文件下载
```

### 完整API文档
启动服务后访问: http://localhost:8004/swagger-ui.html

## 🚀 快速开始

### 1. 环境准备
```bash
# 启动基础设施服务
docker-compose up -d postgresql redis

# 等待服务启动完成
docker-compose ps
```

### 2. 编译运行
```bash
# 编译项目
mvn clean compile

# 运行服务
mvn spring-boot:run

# 或者打包后运行
mvn clean package
java -jar target/hky-audit-service-1.0.0-SNAPSHOT.jar
```

### 3. 验证服务
```bash
# 健康检查
curl http://localhost:8004/api/v1/audit/health

# 查看API文档
open http://localhost:8004/swagger-ui.html
```

## 🔍 监控与运维

### 健康检查
- **应用健康**: GET /api/v1/audit/health
- **系统健康**: GET /actuator/health
- **应用信息**: GET /actuator/info
- **性能指标**: GET /actuator/metrics

### 日志监控
- **应用日志**: logs/audit-service.log
- **日志级别**: 可通过配置文件调整
- **日志格式**: 包含时间戳、线程、级别、类名、消息

### 性能监控
- **Prometheus**: GET /actuator/prometheus
- **JVM指标**: 内存、GC、线程等
- **业务指标**: 审计日志记录数、查询响应时间等

## ⚙️ 配置说明

### 核心配置
```yaml
hky:
  audit:
    retention-days: 365        # 日志保留天数
    batch-size: 1000          # 批量处理大小
    async-queue-size: 10000   # 异步处理队列大小
    export-limit: 10000       # 导出限制
    cache-expire-hours: 1     # 缓存过期时间
```

### 数据库配置
- **连接池**: HikariCP
- **最大连接数**: 20
- **最小空闲连接**: 5
- **连接超时**: 30秒

### Redis配置
- **数据库**: 2
- **连接池**: Lettuce
- **最大连接数**: 20

## 🔒 安全机制

### 权限控制
- `audit:create` - 创建审计日志权限
- `audit:view` - 查看审计日志权限
- `audit:export` - 导出审计日志权限
- `audit:delete` - 删除审计日志权限

### 数据安全
- **日志不可篡改**: 使用软删除，保证数据完整性
- **敏感数据保护**: 对敏感信息进行适当脱敏
- **访问控制**: 严格的权限验证机制

## 📈 性能优化

### 查询优化
- **索引优化**: 针对常用查询字段建立索引
- **分页查询**: 避免大数据量一次性加载
- **缓存机制**: Redis缓存热点数据

### 写入优化
- **异步写入**: 不影响业务性能
- **批量写入**: 提高写入效率
- **连接池**: 优化数据库连接管理

## 🧪 测试

### 单元测试
```bash
mvn test
```

### 集成测试
```bash
mvn integration-test
```

### API测试
使用Swagger UI或Postman进行API测试

## 📝 开发指南

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用统一的代码格式化配置
- 所有API遵循RESTful设计规范
- 强制代码审查和单元测试

### 扩展开发
1. **新增查询维度**: 在Repository中添加查询方法
2. **新增统计指标**: 在Service中添加统计逻辑
3. **新增导出格式**: 扩展ExcelExportUtil
4. **新增风险规则**: 扩展RiskLevel枚举

## 📋 版本历史

- **v1.0.0**: 初始版本，实现审计日志的记录、查询、分析、导出等核心功能

## 📞 联系方式

- 项目负责人: HKY-HR-System
- 技术支持: <EMAIL>
- 文档更新: 2024-12-19
