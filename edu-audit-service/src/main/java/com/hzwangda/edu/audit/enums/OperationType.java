package com.hzwangda.edu.audit.enums;

/**
 * 操作类型枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum OperationType {

    // 认证相关操作
    LOGIN("登录"),
    LOGOUT("登出"),
    LOGIN_FAILED("登录失败"),
    PASSWORD_CHANGE("密码修改"),

    // 数据操作
    CREATE("创建"),
    UPDATE("更新"),
    DELETE("删除"),
    QUERY("查询"),
    VIEW("查看"),

    // 审批操作
    APPROVE("审批通过"),
    REJECT("审批拒绝"),
    SUBMIT("提交审批"),
    WITHDRAW("撤回"),

    // 导入导出操作
    IMPORT("导入"),
    EXPORT("导出"),
    DOWNLOAD("下载"),
    UPLOAD("上传"),

    // 系统操作
    CONFIG_CHANGE("配置修改"),
    PERMISSION_GRANT("权限授予"),
    PERMISSION_REVOKE("权限撤销"),

    // 敏感操作
    SENSITIVE_DATA_ACCESS("敏感数据访问"),
    BATCH_OPERATION("批量操作"),
    DATA_MIGRATION("数据迁移"),

    // 其他操作
    OTHER("其他");

    private final String description;

    OperationType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public String getCode() {
        return this.name();
    }
}
