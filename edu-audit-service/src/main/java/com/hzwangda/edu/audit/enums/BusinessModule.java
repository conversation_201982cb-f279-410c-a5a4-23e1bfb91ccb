package com.hzwangda.edu.audit.enums;

/**
 * 业务模块枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum BusinessModule {

    // 认证授权模块
    AUTH("认证授权"),

    // 组织管理模块
    ORGANIZATION("组织管理"),
    POSITION("岗位管理"),

    // 人员管理模块
    EMPLOYEE("教职工信息管理"),
    PERSONNEL_CHANGE("人事变动管理"),

    // 合同管理模块
    CONTRACT("合同管理"),

    // 考勤管理模块
    ATTENDANCE("考勤管理"),
    LEAVE("请假管理"),

    // 薪酬福利模块
    COMPENSATION("薪酬福利管理"),

    // 招聘管理模块
    RECRUITMENT("招聘管理"),

    // 师资发展模块
    FACULTY_DEVELOPMENT("师资发展"),
    TRAINING("培训管理"),

    // 考核管理模块
    APPRAISAL("考核管理"),

    // 职称评聘模块
    TITLE_EVALUATION("职称评聘"),

    // 综合服务模块
    INTEGRATED_SERVICE("综合服务"),

    // 领导驾驶舱模块
    DASHBOARD("领导驾驶舱"),

    // 平台服务模块
    FILE_MANAGEMENT("文件管理"),
    NOTIFICATION("消息通知"),
    AUDIT("审计日志"),
    DICT_CONFIG("字典配置"),
    WORKFLOW("工作流"),

    // 系统管理模块
    SYSTEM("系统管理"),
    USER_MANAGEMENT("用户管理"),
    ROLE_MANAGEMENT("角色管理"),
    PERMISSION_MANAGEMENT("权限管理"),

    // 其他模块
    OTHER("其他");

    private final String description;

    BusinessModule(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public String getCode() {
        return this.name();
    }
}
