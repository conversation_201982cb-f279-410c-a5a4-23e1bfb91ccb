package com.hzwangda.edu.audit.util;

import com.hzwangda.edu.audit.dto.AuditLogResponse;
import com.hzwangda.edu.audit.entity.AuditLog;
import org.springframework.stereotype.Component;

/**
 * 审计日志映射工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class AuditLogMapper {

    /**
     * 实体转响应DTO
     */
    public AuditLogResponse toResponse(AuditLog auditLog) {
        if (auditLog == null) {
            return null;
        }

        AuditLogResponse response = new AuditLogResponse();

        // 基本信息
        response.setId(auditLog.getId());
        response.setUserId(auditLog.getUserId());
        response.setUsername(auditLog.getUsername());
        response.setRealName(auditLog.getRealName());
        response.setOperationTime(auditLog.getOperationTime());

        // 操作信息
        response.setOperationType(auditLog.getOperationType());
        response.setModule(auditLog.getModule());
        response.setOperationObject(auditLog.getOperationObject());
        response.setObjectId(auditLog.getObjectId());
        response.setOperationResult(auditLog.getOperationResult());
        response.setOperationDescription(auditLog.getOperationDescription());

        // 网络信息
        response.setIpAddress(auditLog.getIpAddress());
        response.setUserAgent(auditLog.getUserAgent());
        response.setRequestUri(auditLog.getRequestUri());
        response.setRequestMethod(auditLog.getRequestMethod());
        response.setRequestParams(auditLog.getRequestParams());
        response.setResponseData(auditLog.getResponseData());

        // 性能信息
        response.setExecutionTime(auditLog.getExecutionTime());
        response.setErrorMessage(auditLog.getErrorMessage());
        response.setSessionId(auditLog.getSessionId());
        response.setRiskLevel(auditLog.getRiskLevel());

        // 审计信息
        response.setCreateTime(auditLog.getCreateTime());

        return response;
    }
}
