package com.hzwangda.edu.audit.controller;

import com.hzwangda.edu.audit.dto.AuditLogCreateRequest;
import com.hzwangda.edu.audit.dto.AuditLogQueryRequest;
import com.hzwangda.edu.audit.dto.AuditLogResponse;
import com.hzwangda.edu.audit.dto.AuditStatisticsResponse;
import com.hzwangda.edu.audit.service.AuditLogService;
import com.hzwangda.edu.common.dto.PageResult;
import com.hzwangda.edu.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 审计日志控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/v1/audit")
@Tag(name = "审计日志管理", description = "审计日志记录、查询、分析、导出等功能")
public class AuditLogController {

    private static final Logger logger = LoggerFactory.getLogger(AuditLogController.class);

    @Autowired
    private AuditLogService auditLogService;

    @PostMapping("/logs")
    @Operation(summary = "记录审计日志", description = "记录用户操作的审计日志")
    @PreAuthorize("hasAuthority('audit:create')")
    public Result<AuditLogResponse> recordAuditLog(@Valid @RequestBody AuditLogCreateRequest request) {
        logger.info("记录审计日志: 用户={}, 操作={}", request.getUsername(), request.getOperationType());
        AuditLogResponse response = auditLogService.recordAuditLog(request);
        return Result.success(response);
    }

    @PostMapping("/logs/batch")
    @Operation(summary = "批量记录审计日志", description = "批量记录多个审计日志")
    @PreAuthorize("hasAuthority('audit:create')")
    public Result<Integer> batchRecordAuditLogs(@Valid @RequestBody List<AuditLogCreateRequest> requests) {
        logger.info("批量记录审计日志: 数量={}", requests.size());
        Integer count = auditLogService.batchRecordAuditLogs(requests);
        return Result.success(count);
    }

    @GetMapping("/logs")
    @Operation(summary = "分页查询审计日志", description = "根据条件分页查询审计日志")
    @PreAuthorize("hasAuthority('audit:view')")
    public Result<PageResult<AuditLogResponse>> queryAuditLogs(@Valid AuditLogQueryRequest request) {
        logger.debug("查询审计日志: 页码={}, 大小={}", request.getPage(), request.getSize());
        PageResult<AuditLogResponse> result = auditLogService.queryAuditLogs(request);
        return Result.success(result);
    }

    @GetMapping("/logs/{id}")
    @Operation(summary = "查询审计日志详情", description = "根据ID查询审计日志详情")
    @PreAuthorize("hasAuthority('audit:view')")
    public Result<AuditLogResponse> getAuditLogById(
            @Parameter(description = "日志ID") @PathVariable Long id) {
        logger.debug("查询审计日志详情: ID={}", id);
        AuditLogResponse response = auditLogService.getAuditLogById(id);
        return Result.success(response);
    }

    @GetMapping("/logs/user/{userId}/history")
    @Operation(summary = "查询用户操作历史", description = "查询指定用户的操作历史")
    @PreAuthorize("hasAuthority('audit:view')")
    public Result<List<AuditLogResponse>> getUserOperationHistory(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        logger.debug("查询用户操作历史: 用户ID={}", userId);
        List<AuditLogResponse> history = auditLogService.getUserOperationHistory(userId, startTime, endTime);
        return Result.success(history);
    }

    @GetMapping("/logs/object/{objectId}/history")
    @Operation(summary = "查询对象操作历史", description = "查询指定对象的操作历史")
    @PreAuthorize("hasAuthority('audit:view')")
    public Result<List<AuditLogResponse>> getObjectOperationHistory(
            @Parameter(description = "对象ID") @PathVariable String objectId) {
        logger.debug("查询对象操作历史: 对象ID={}", objectId);
        List<AuditLogResponse> history = auditLogService.getObjectOperationHistory(objectId);
        return Result.success(history);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取审计统计信息", description = "获取指定时间范围内的审计统计信息")
    @PreAuthorize("hasAuthority('audit:view')")
    public Result<AuditStatisticsResponse> getAuditStatistics(
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        logger.debug("获取审计统计信息: 开始时间={}, 结束时间={}", startTime, endTime);
        AuditStatisticsResponse statistics = auditLogService.getAuditStatistics(startTime, endTime);
        return Result.success(statistics);
    }

    @GetMapping("/logs/high-risk")
    @Operation(summary = "查询高风险操作", description = "查询指定时间范围内的高风险操作")
    @PreAuthorize("hasAuthority('audit:view')")
    public Result<List<AuditLogResponse>> getHighRiskOperations(
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        logger.debug("查询高风险操作: 开始时间={}, 结束时间={}", startTime, endTime);
        List<AuditLogResponse> operations = auditLogService.getHighRiskOperations(startTime, endTime);
        return Result.success(operations);
    }

    @GetMapping("/logs/export")
    @Operation(summary = "导出审计日志", description = "根据查询条件导出审计日志到Excel文件")
    @PreAuthorize("hasAuthority('audit:export')")
    public ResponseEntity<byte[]> exportAuditLogs(@Valid AuditLogQueryRequest request) {
        logger.info("导出审计日志: 查询条件={}", request);

        byte[] excelData = auditLogService.exportAuditLogs(request);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "audit_logs_" + System.currentTimeMillis() + ".xlsx");

        return ResponseEntity.ok()
                .headers(headers)
                .body(excelData);
    }

    @DeleteMapping("/logs/cleanup")
    @Operation(summary = "清理过期日志", description = "清理指定日期之前的过期日志")
    @PreAuthorize("hasAuthority('audit:delete')")
    public Result<Long> cleanupExpiredLogs(
            @Parameter(description = "清理此日期之前的日志") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime beforeDate) {
        logger.info("清理过期日志: 清理{}之前的日志", beforeDate);
        Long count = auditLogService.cleanupExpiredLogs(beforeDate);
        return Result.success(count);
    }

    @GetMapping("/logs/verify")
    @Operation(summary = "验证日志完整性", description = "验证指定时间范围内日志的完整性")
    @PreAuthorize("hasAuthority('audit:view')")
    public Result<Boolean> verifyLogIntegrity(
            @Parameter(description = "开始时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime) {
        logger.debug("验证日志完整性: 开始时间={}, 结束时间={}", startTime, endTime);
        Boolean isValid = auditLogService.verifyLogIntegrity(startTime, endTime);
        return Result.success(isValid);
    }

    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "审计日志服务健康检查")
    public Result<String> health() {
        return Result.success("审计日志服务运行正常");
    }
}
