package com.hzwangda.edu.audit.enums;

/**
 * 操作结果枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum OperationResult {

    SUCCESS("成功"),
    FAILED("失败"),
    PARTIAL_SUCCESS("部分成功"),
    UNAUTHORIZED("未授权"),
    FORBIDDEN("禁止访问"),
    NOT_FOUND("未找到"),
    TIMEOUT("超时"),
    ERROR("错误");

    private final String description;

    OperationResult(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public String getCode() {
        return this.name();
    }
}
