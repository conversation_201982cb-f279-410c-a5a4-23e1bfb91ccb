package com.hzwangda.edu.audit.service;

import com.hzwangda.edu.audit.dto.AuditLogCreateRequest;
import com.hzwangda.edu.audit.dto.AuditLogQueryRequest;
import com.hzwangda.edu.audit.dto.AuditLogResponse;
import com.hzwangda.edu.audit.dto.AuditStatisticsResponse;
import com.hzwangda.edu.common.dto.PageResult;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 审计日志服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface AuditLogService {

    /**
     * 记录审计日志
     *
     * @param request 审计日志创建请求
     * @return 审计日志响应
     */
    AuditLogResponse recordAuditLog(AuditLogCreateRequest request);

    /**
     * 异步记录审计日志
     *
     * @param request 审计日志创建请求
     */
    void recordAuditLogAsync(AuditLogCreateRequest request);

    /**
     * 分页查询审计日志
     *
     * @param request 查询请求
     * @return 分页结果
     */
    PageResult<AuditLogResponse> queryAuditLogs(AuditLogQueryRequest request);

    /**
     * 根据ID查询审计日志详情
     *
     * @param id 日志ID
     * @return 审计日志响应
     */
    AuditLogResponse getAuditLogById(Long id);

    /**
     * 查询用户操作历史
     *
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作历史列表
     */
    List<AuditLogResponse> getUserOperationHistory(Long userId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询对象操作历史
     *
     * @param objectId 对象ID
     * @return 操作历史列表
     */
    List<AuditLogResponse> getObjectOperationHistory(String objectId);

    /**
     * 获取审计统计信息
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    AuditStatisticsResponse getAuditStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询高风险操作
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 高风险操作列表
     */
    List<AuditLogResponse> getHighRiskOperations(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 导出审计日志
     *
     * @param request 查询请求
     * @return Excel文件字节数组
     */
    byte[] exportAuditLogs(AuditLogQueryRequest request);

    /**
     * 清理过期日志
     *
     * @param beforeDate 清理此日期之前的日志
     * @return 清理的日志数量
     */
    Long cleanupExpiredLogs(LocalDateTime beforeDate);

    /**
     * 验证日志完整性
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 验证结果
     */
    boolean verifyLogIntegrity(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 批量记录审计日志
     *
     * @param requests 审计日志创建请求列表
     * @return 成功记录的数量
     */
    Integer batchRecordAuditLogs(List<AuditLogCreateRequest> requests);
}
