package com.hzwangda.edu.audit.util;

import com.hzwangda.edu.audit.dto.AuditLogResponse;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Excel导出工具类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class ExcelExportUtil {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 导出审计日志到Excel
     */
    public byte[] exportAuditLogs(List<AuditLogResponse> auditLogs) throws IOException {
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

            Sheet sheet = workbook.createSheet("审计日志");

            // 创建标题行
            createHeaderRow(sheet, workbook);

            // 创建数据行
            createDataRows(sheet, auditLogs, workbook);

            // 自动调整列宽
            autoSizeColumns(sheet);

            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }

    /**
     * 创建标题行
     */
    private void createHeaderRow(Sheet sheet, Workbook workbook) {
        Row headerRow = sheet.createRow(0);

        // 创建标题样式
        CellStyle headerStyle = createHeaderStyle(workbook);

        String[] headers = {
            "日志ID", "用户ID", "用户名", "真实姓名", "操作时间",
            "操作类型", "业务模块", "操作对象", "对象ID", "操作结果",
            "操作描述", "IP地址", "用户代理", "请求URI", "请求方法",
            "执行时间(ms)", "错误信息", "会话ID", "风险级别", "创建时间"
        };

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }

    /**
     * 创建数据行
     */
    private void createDataRows(Sheet sheet, List<AuditLogResponse> auditLogs, Workbook workbook) {
        CellStyle dataStyle = createDataStyle(workbook);
        CellStyle dateStyle = createDateStyle(workbook);

        int rowNum = 1;
        for (AuditLogResponse log : auditLogs) {
            Row row = sheet.createRow(rowNum++);

            int colNum = 0;

            // 日志ID
            createCell(row, colNum++, log.getId(), dataStyle);

            // 用户ID
            createCell(row, colNum++, log.getUserId(), dataStyle);

            // 用户名
            createCell(row, colNum++, log.getUsername(), dataStyle);

            // 真实姓名
            createCell(row, colNum++, log.getRealName(), dataStyle);

            // 操作时间
            createCell(row, colNum++,
                log.getOperationTime() != null ? log.getOperationTime().format(DATE_TIME_FORMATTER) : "",
                dateStyle);

            // 操作类型
            createCell(row, colNum++, log.getOperationType(), dataStyle);

            // 业务模块
            createCell(row, colNum++, log.getModule(), dataStyle);

            // 操作对象
            createCell(row, colNum++, log.getOperationObject(), dataStyle);

            // 对象ID
            createCell(row, colNum++, log.getObjectId(), dataStyle);

            // 操作结果
            createCell(row, colNum++, log.getOperationResult(), dataStyle);

            // 操作描述
            createCell(row, colNum++, log.getOperationDescription(), dataStyle);

            // IP地址
            createCell(row, colNum++, log.getIpAddress(), dataStyle);

            // 用户代理
            createCell(row, colNum++, log.getUserAgent(), dataStyle);

            // 请求URI
            createCell(row, colNum++, log.getRequestUri(), dataStyle);

            // 请求方法
            createCell(row, colNum++, log.getRequestMethod(), dataStyle);

            // 执行时间
            createCell(row, colNum++, log.getExecutionTime(), dataStyle);

            // 错误信息
            createCell(row, colNum++, log.getErrorMessage(), dataStyle);

            // 会话ID
            createCell(row, colNum++, log.getSessionId(), dataStyle);

            // 风险级别
            createCell(row, colNum++, log.getRiskLevel(), dataStyle);

            // 创建时间
            createCell(row, colNum++,
                log.getCreateTime() != null ? log.getCreateTime().format(DATE_TIME_FORMATTER) : "",
                dateStyle);
        }
    }

    /**
     * 创建单元格
     */
    private void createCell(Row row, int colNum, Object value, CellStyle style) {
        Cell cell = row.createCell(colNum);

        if (value != null) {
            if (value instanceof String) {
                cell.setCellValue((String) value);
            } else if (value instanceof Number) {
                cell.setCellValue(((Number) value).doubleValue());
            } else {
                cell.setCellValue(value.toString());
            }
        }

        cell.setCellStyle(style);
    }

    /**
     * 创建标题样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置背景色
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置字体
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);

        // 设置对齐
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        return style;
    }

    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置对齐
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置自动换行
        style.setWrapText(true);

        return style;
    }

    /**
     * 创建日期样式
     */
    private CellStyle createDateStyle(Workbook workbook) {
        CellStyle style = createDataStyle(workbook);
        style.setAlignment(HorizontalAlignment.CENTER);
        return style;
    }

    /**
     * 自动调整列宽
     */
    private void autoSizeColumns(Sheet sheet) {
        Row headerRow = sheet.getRow(0);
        if (headerRow != null) {
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                sheet.autoSizeColumn(i);

                // 设置最大列宽，防止过宽
                int columnWidth = sheet.getColumnWidth(i);
                if (columnWidth > 15000) {
                    sheet.setColumnWidth(i, 15000);
                }

                // 设置最小列宽
                if (columnWidth < 2000) {
                    sheet.setColumnWidth(i, 2000);
                }
            }
        }
    }
}
