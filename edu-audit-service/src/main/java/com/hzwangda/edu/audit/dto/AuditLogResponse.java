package com.hzwangda.edu.audit.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 审计日志响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "审计日志响应")
public class AuditLogResponse {

    @Schema(description = "日志ID", example = "1")
    private Long id;

    @Schema(description = "操作用户ID", example = "1")
    private Long userId;

    @Schema(description = "操作用户名", example = "admin")
    private String username;

    @Schema(description = "操作用户真实姓名", example = "张三")
    private String realName;

    @Schema(description = "操作时间")
    private LocalDateTime operationTime;

    @Schema(description = "操作类型", example = "CREATE")
    private String operationType;

    @Schema(description = "业务模块", example = "EMPLOYEE")
    private String module;

    @Schema(description = "操作对象", example = "员工信息")
    private String operationObject;

    @Schema(description = "操作对象ID", example = "E001")
    private String objectId;

    @Schema(description = "操作结果", example = "SUCCESS")
    private String operationResult;

    @Schema(description = "操作描述", example = "创建新员工")
    private String operationDescription;

    @Schema(description = "操作IP地址", example = "*************")
    private String ipAddress;

    @Schema(description = "用户代理", example = "Mozilla/5.0...")
    private String userAgent;

    @Schema(description = "请求URI", example = "/api/v1/employees")
    private String requestUri;

    @Schema(description = "请求方法", example = "POST")
    private String requestMethod;

    @Schema(description = "请求参数")
    private String requestParams;

    @Schema(description = "响应数据")
    private String responseData;

    @Schema(description = "执行时间(毫秒)", example = "150")
    private Long executionTime;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "会话ID")
    private String sessionId;

    @Schema(description = "风险级别", example = "LOW")
    private String riskLevel;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    // 构造函数
    public AuditLogResponse() {
    }

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public LocalDateTime getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(LocalDateTime operationTime) {
        this.operationTime = operationTime;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public String getOperationObject() {
        return operationObject;
    }

    public void setOperationObject(String operationObject) {
        this.operationObject = operationObject;
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public String getOperationResult() {
        return operationResult;
    }

    public void setOperationResult(String operationResult) {
        this.operationResult = operationResult;
    }

    public String getOperationDescription() {
        return operationDescription;
    }

    public void setOperationDescription(String operationDescription) {
        this.operationDescription = operationDescription;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getRequestUri() {
        return requestUri;
    }

    public void setRequestUri(String requestUri) {
        this.requestUri = requestUri;
    }

    public String getRequestMethod() {
        return requestMethod;
    }

    public void setRequestMethod(String requestMethod) {
        this.requestMethod = requestMethod;
    }

    public String getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(String requestParams) {
        this.requestParams = requestParams;
    }

    public String getResponseData() {
        return responseData;
    }

    public void setResponseData(String responseData) {
        this.responseData = responseData;
    }

    public Long getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(Long executionTime) {
        this.executionTime = executionTime;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
