package com.hzwangda.edu.audit.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

/**
 * 审计日志查询请求DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "审计日志查询请求")
public class AuditLogQueryRequest {

    @Schema(description = "操作用户ID", example = "1")
    private Long userId;

    @Size(max = 64, message = "用户名长度不能超过64个字符")
    @Schema(description = "操作用户名", example = "admin")
    private String username;

    @Size(max = 64, message = "真实姓名长度不能超过64个字符")
    @Schema(description = "操作用户真实姓名", example = "张三")
    private String realName;

    @Schema(description = "操作开始时间")
    private LocalDateTime startTime;

    @Schema(description = "操作结束时间")
    private LocalDateTime endTime;

    @Size(max = 32, message = "操作类型长度不能超过32个字符")
    @Schema(description = "操作类型", example = "CREATE")
    private String operationType;

    @Size(max = 64, message = "业务模块长度不能超过64个字符")
    @Schema(description = "业务模块", example = "EMPLOYEE")
    private String module;

    @Size(max = 128, message = "操作对象长度不能超过128个字符")
    @Schema(description = "操作对象", example = "员工信息")
    private String operationObject;

    @Size(max = 64, message = "操作对象ID长度不能超过64个字符")
    @Schema(description = "操作对象ID", example = "E001")
    private String objectId;

    @Size(max = 16, message = "操作结果长度不能超过16个字符")
    @Schema(description = "操作结果", example = "SUCCESS")
    private String operationResult;

    @Size(max = 45, message = "IP地址长度不能超过45个字符")
    @Schema(description = "操作IP地址", example = "*************")
    private String ipAddress;

    @Size(max = 128, message = "请求URI长度不能超过128个字符")
    @Schema(description = "请求URI", example = "/api/v1/employees")
    private String requestUri;

    @Size(max = 16, message = "请求方法长度不能超过16个字符")
    @Schema(description = "请求方法", example = "POST")
    private String requestMethod;

    @Size(max = 32, message = "风险级别长度不能超过32个字符")
    @Schema(description = "风险级别", example = "LOW")
    private String riskLevel;

    @Schema(description = "关键词搜索（在操作描述中搜索）")
    private String keyword;

    @Min(value = 1, message = "页码必须大于0")
    @Schema(description = "页码", example = "1")
    private Integer page = 1;

    @Min(value = 1, message = "每页大小必须大于0")
    @Schema(description = "每页大小", example = "20")
    private Integer size = 20;

    @Schema(description = "排序字段", example = "operationTime")
    private String sortBy = "operationTime";

    @Schema(description = "排序方向", example = "DESC")
    private String sortDirection = "DESC";

    // 构造函数
    public AuditLogQueryRequest() {
    }

    // Getter and Setter methods
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public String getOperationObject() {
        return operationObject;
    }

    public void setOperationObject(String operationObject) {
        this.operationObject = operationObject;
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public String getOperationResult() {
        return operationResult;
    }

    public void setOperationResult(String operationResult) {
        this.operationResult = operationResult;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getRequestUri() {
        return requestUri;
    }

    public void setRequestUri(String requestUri) {
        this.requestUri = requestUri;
    }

    public String getRequestMethod() {
        return requestMethod;
    }

    public void setRequestMethod(String requestMethod) {
        this.requestMethod = requestMethod;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    public String getSortDirection() {
        return sortDirection;
    }

    public void setSortDirection(String sortDirection) {
        this.sortDirection = sortDirection;
    }
}
