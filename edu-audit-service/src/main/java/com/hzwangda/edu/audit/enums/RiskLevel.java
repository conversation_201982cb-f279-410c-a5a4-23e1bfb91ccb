package com.hzwangda.edu.audit.enums;

/**
 * 风险级别枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum RiskLevel {

    LOW("低风险"),
    MEDIUM("中风险"),
    HIGH("高风险"),
    CRITICAL("严重风险");

    private final String description;

    RiskLevel(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public String getCode() {
        return this.name();
    }

    /**
     * 根据操作类型判断风险级别
     */
    public static RiskLevel evaluateRisk(OperationType operationType, BusinessModule module) {
        // 高风险操作
        if (operationType == OperationType.DELETE ||
            operationType == OperationType.BATCH_OPERATION ||
            operationType == OperationType.DATA_MIGRATION ||
            operationType == OperationType.PERMISSION_GRANT ||
            operationType == OperationType.PERMISSION_REVOKE) {
            return HIGH;
        }

        // 中风险操作
        if (operationType == OperationType.SENSITIVE_DATA_ACCESS ||
            operationType == OperationType.EXPORT ||
            operationType == OperationType.CONFIG_CHANGE ||
            module == BusinessModule.AUTH ||
            module == BusinessModule.USER_MANAGEMENT ||
            module == BusinessModule.ROLE_MANAGEMENT) {
            return MEDIUM;
        }

        // 登录失败等严重安全事件
        if (operationType == OperationType.LOGIN_FAILED) {
            return CRITICAL;
        }

        // 默认低风险
        return LOW;
    }
}
