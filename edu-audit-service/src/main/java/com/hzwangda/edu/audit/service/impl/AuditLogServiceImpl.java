package com.hzwangda.edu.audit.service.impl;


import com.hzwangda.edu.audit.dto.AuditLogCreateRequest;
import com.hzwangda.edu.audit.dto.AuditLogQueryRequest;
import com.hzwangda.edu.audit.dto.AuditLogResponse;
import com.hzwangda.edu.audit.dto.AuditStatisticsResponse;
import com.hzwangda.edu.audit.entity.AuditLog;
import com.hzwangda.edu.audit.enums.BusinessModule;
import com.hzwangda.edu.audit.enums.OperationType;
import com.hzwangda.edu.audit.enums.RiskLevel;
import com.hzwangda.edu.audit.repository.AuditLogRepository;
import com.hzwangda.edu.audit.service.AuditLogService;
import com.hzwangda.edu.audit.util.AuditLogMapper;
import com.hzwangda.edu.audit.util.ExcelExportUtil;
import com.hzwangda.edu.common.dto.PageResult;
import com.hzwangda.edu.common.exception.BusinessException;
import com.hzwangda.edu.common.result.ResultCode;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 审计日志服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Transactional
public class AuditLogServiceImpl implements AuditLogService {

    private static final Logger logger = LoggerFactory.getLogger(AuditLogServiceImpl.class);

    @Autowired
    private AuditLogRepository auditLogRepository;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private AuditLogMapper auditLogMapper;

    @Autowired
    private ExcelExportUtil excelExportUtil;

    private static final String CACHE_KEY_PREFIX = "audit:log:";
    private static final String CACHE_STATS_PREFIX = "audit:stats:";
    private static final int CACHE_EXPIRE_HOURS = 1;

    @Override
    public AuditLogResponse recordAuditLog(AuditLogCreateRequest request) {
        logger.info("记录审计日志: 用户={}, 操作类型={}, 模块={}",
                   request.getUsername(), request.getOperationType(), request.getModule());

        try {
            // 创建审计日志实体
            AuditLog auditLog = createAuditLogEntity(request);

            // 保存到数据库
            auditLog = auditLogRepository.save(auditLog);

            // 清除相关缓存
            clearRelatedCache();

            logger.info("审计日志记录成功: ID={}", auditLog.getId());
            return auditLogMapper.toResponse(auditLog);

        } catch (Exception e) {
            logger.error("记录审计日志失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "记录审计日志失败: " + e.getMessage());
        }
    }

    @Override
    @Async
    public void recordAuditLogAsync(AuditLogCreateRequest request) {
        try {
            recordAuditLog(request);
        } catch (Exception e) {
            logger.error("异步记录审计日志失败: {}", e.getMessage(), e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public PageResult<AuditLogResponse> queryAuditLogs(AuditLogQueryRequest request) {
        logger.debug("查询审计日志: 页码={}, 大小={}", request.getPage(), request.getSize());

        try {
            // 构建查询条件
            Specification<AuditLog> spec = buildQuerySpecification(request);

            // 构建分页和排序
            Pageable pageable = buildPageable(request);

            // 执行查询
            Page<AuditLog> page = auditLogRepository.findAll(spec, pageable);

            // 转换结果
            List<AuditLogResponse> responses = page.getContent().stream()
                    .map(auditLogMapper::toResponse)
                    .collect(Collectors.toList());

            return new PageResult<>(responses, page.getTotalElements(),
                                  request.getPage(), request.getSize());

        } catch (Exception e) {
            logger.error("查询审计日志失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "查询审计日志失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public AuditLogResponse getAuditLogById(Long id) {
        logger.debug("根据ID查询审计日志: {}", id);

        // 先从缓存查询
        String cacheKey = CACHE_KEY_PREFIX + id;
        AuditLogResponse cached = (AuditLogResponse) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }

        // 从数据库查询
        AuditLog auditLog = auditLogRepository.findById(id)
                .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "审计日志不存在"));

        if (auditLog.getDeleted()) {
            throw new BusinessException(ResultCode.NOT_FOUND, "审计日志不存在");
        }

        AuditLogResponse response = auditLogMapper.toResponse(auditLog);

        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, response, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);

        return response;
    }

    @Override
    @Transactional(readOnly = true)
    public List<AuditLogResponse> getUserOperationHistory(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        logger.debug("查询用户操作历史: 用户ID={}, 开始时间={}, 结束时间={}", userId, startTime, endTime);

        List<AuditLog> logs = auditLogRepository.findUserOperationHistory(userId, startTime, endTime);
        return logs.stream()
                .map(auditLogMapper::toResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<AuditLogResponse> getObjectOperationHistory(String objectId) {
        logger.debug("查询对象操作历史: 对象ID={}", objectId);

        List<AuditLog> logs = auditLogRepository.findObjectOperationHistory(objectId);
        return logs.stream()
                .map(auditLogMapper::toResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<AuditLogResponse> getHighRiskOperations(LocalDateTime startTime, LocalDateTime endTime) {
        logger.debug("查询高风险操作: 开始时间={}, 结束时间={}", startTime, endTime);

        List<AuditLog> logs = auditLogRepository.findHighRiskOperations(startTime, endTime);
        return logs.stream()
                .map(auditLogMapper::toResponse)
                .collect(Collectors.toList());
    }

    @Override
    public Integer batchRecordAuditLogs(List<AuditLogCreateRequest> requests) {
        logger.info("批量记录审计日志: 数量={}", requests.size());

        try {
            List<AuditLog> auditLogs = requests.stream()
                    .map(this::createAuditLogEntity)
                    .collect(Collectors.toList());

            List<AuditLog> savedLogs = auditLogRepository.saveAll(auditLogs);

            // 清除相关缓存
            clearRelatedCache();

            logger.info("批量记录审计日志成功: 数量={}", savedLogs.size());
            return savedLogs.size();

        } catch (Exception e) {
            logger.error("批量记录审计日志失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "批量记录审计日志失败: " + e.getMessage());
        }
    }

    /**
     * 创建审计日志实体
     */
    private AuditLog createAuditLogEntity(AuditLogCreateRequest request) {
        AuditLog auditLog = new AuditLog();

        // 基本信息
        auditLog.setUserId(request.getUserId());
        auditLog.setUsername(request.getUsername());
        auditLog.setRealName(request.getRealName());
        auditLog.setOperationTime(request.getOperationTime() != null ?
                                 request.getOperationTime() : LocalDateTime.now());

        // 操作信息
        auditLog.setOperationType(request.getOperationType());
        auditLog.setModule(request.getModule());
        auditLog.setOperationObject(request.getOperationObject());
        auditLog.setObjectId(request.getObjectId());
        auditLog.setOperationResult(request.getOperationResult());
        auditLog.setOperationDescription(request.getOperationDescription());

        // 网络信息
        auditLog.setIpAddress(request.getIpAddress());
        auditLog.setUserAgent(request.getUserAgent());
        auditLog.setRequestUri(request.getRequestUri());
        auditLog.setRequestMethod(request.getRequestMethod());
        auditLog.setRequestParams(request.getRequestParams());
        auditLog.setResponseData(request.getResponseData());

        // 性能信息
        auditLog.setExecutionTime(request.getExecutionTime());
        auditLog.setErrorMessage(request.getErrorMessage());
        auditLog.setSessionId(request.getSessionId());

        // 风险评估
        if (StringUtils.isBlank(request.getRiskLevel())) {
            try {
                OperationType operationType = OperationType.valueOf(request.getOperationType());
                BusinessModule module = BusinessModule.valueOf(request.getModule());
                RiskLevel riskLevel = RiskLevel.evaluateRisk(operationType, module);
                auditLog.setRiskLevel(riskLevel.getCode());
            } catch (Exception e) {
                auditLog.setRiskLevel(RiskLevel.LOW.getCode());
            }
        } else {
            auditLog.setRiskLevel(request.getRiskLevel());
        }

        return auditLog;
    }

    /**
     * 构建查询条件
     */
    private Specification<AuditLog> buildQuerySpecification(AuditLogQueryRequest request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 基础条件：未删除
            predicates.add(criteriaBuilder.equal(root.get("deleted"), false));

            // 用户ID
            if (request.getUserId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("userId"), request.getUserId()));
            }

            // 用户名
            if (StringUtils.isNotBlank(request.getUsername())) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("username")),
                    "%" + request.getUsername().toLowerCase() + "%"));
            }

            // 真实姓名
            if (StringUtils.isNotBlank(request.getRealName())) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("realName")),
                    "%" + request.getRealName().toLowerCase() + "%"));
            }

            // 时间范围
            if (request.getStartTime() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(
                    root.get("operationTime"), request.getStartTime()));
            }
            if (request.getEndTime() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(
                    root.get("operationTime"), request.getEndTime()));
            }

            // 操作类型
            if (StringUtils.isNotBlank(request.getOperationType())) {
                predicates.add(criteriaBuilder.equal(root.get("operationType"), request.getOperationType()));
            }

            // 业务模块
            if (StringUtils.isNotBlank(request.getModule())) {
                predicates.add(criteriaBuilder.equal(root.get("module"), request.getModule()));
            }

            // 操作对象
            if (StringUtils.isNotBlank(request.getOperationObject())) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("operationObject")),
                    "%" + request.getOperationObject().toLowerCase() + "%"));
            }

            // 对象ID
            if (StringUtils.isNotBlank(request.getObjectId())) {
                predicates.add(criteriaBuilder.equal(root.get("objectId"), request.getObjectId()));
            }

            // 操作结果
            if (StringUtils.isNotBlank(request.getOperationResult())) {
                predicates.add(criteriaBuilder.equal(root.get("operationResult"), request.getOperationResult()));
            }

            // IP地址
            if (StringUtils.isNotBlank(request.getIpAddress())) {
                predicates.add(criteriaBuilder.equal(root.get("ipAddress"), request.getIpAddress()));
            }

            // 请求URI
            if (StringUtils.isNotBlank(request.getRequestUri())) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("requestUri")),
                    "%" + request.getRequestUri().toLowerCase() + "%"));
            }

            // 请求方法
            if (StringUtils.isNotBlank(request.getRequestMethod())) {
                predicates.add(criteriaBuilder.equal(root.get("requestMethod"), request.getRequestMethod()));
            }

            // 风险级别
            if (StringUtils.isNotBlank(request.getRiskLevel())) {
                predicates.add(criteriaBuilder.equal(root.get("riskLevel"), request.getRiskLevel()));
            }

            // 关键词搜索
            if (StringUtils.isNotBlank(request.getKeyword())) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("operationDescription")),
                    "%" + request.getKeyword().toLowerCase() + "%"));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 构建分页和排序
     */
    private Pageable buildPageable(AuditLogQueryRequest request) {
        Sort sort = Sort.by(
            "DESC".equalsIgnoreCase(request.getSortDirection()) ?
            Sort.Direction.DESC : Sort.Direction.ASC,
            request.getSortBy()
        );

        return PageRequest.of(request.getPage() - 1, request.getSize(), sort);
    }

    @Override
    @Transactional(readOnly = true)
    public AuditStatisticsResponse getAuditStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        logger.debug("获取审计统计信息: 开始时间={}, 结束时间={}", startTime, endTime);

        // 检查缓存
        String cacheKey = CACHE_STATS_PREFIX + startTime + ":" + endTime;
        AuditStatisticsResponse cached = (AuditStatisticsResponse) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }

        try {
            AuditStatisticsResponse response = new AuditStatisticsResponse();

            // 基础统计
            response.setTotalOperations(auditLogRepository.countOperationsByTimeRange(startTime, endTime));
            response.setSuccessOperations(auditLogRepository.countSuccessOperationsByTimeRange(startTime, endTime));
            response.setFailedOperations(auditLogRepository.countFailedOperationsByTimeRange(startTime, endTime));
            response.setHighRiskOperations(auditLogRepository.countHighRiskOperationsByTimeRange(startTime, endTime));
            response.setActiveUsers(auditLogRepository.countActiveUsersByTimeRange(startTime, endTime));

            // 按维度统计
            response.setOperationTypeStats(convertToMap(auditLogRepository.countByOperationType(startTime, endTime)));
            response.setModuleStats(convertToMap(auditLogRepository.countByModule(startTime, endTime)));
            response.setResultStats(convertToMap(auditLogRepository.countByOperationResult(startTime, endTime)));
            response.setRiskLevelStats(convertToMap(auditLogRepository.countByRiskLevel(startTime, endTime)));

            // 时间维度统计
            response.setHourlyStats(convertToMap(auditLogRepository.countByHour(startTime, endTime)));
            response.setDailyStats(convertToMap(auditLogRepository.countByDate(startTime, endTime)));

            // TOP统计
            response.setTopActiveUsers(convertToUserStats(
                auditLogRepository.findTopActiveUsers(startTime, endTime, PageRequest.of(0, 10))));
            response.setTopActiveIps(convertToIpStats(
                auditLogRepository.findTopActiveIps(startTime, endTime, PageRequest.of(0, 10))));

            // 最近高风险操作
            List<AuditLog> highRiskLogs = auditLogRepository.findHighRiskOperations(startTime, endTime);
            response.setRecentHighRiskOperations(highRiskLogs.stream()
                .limit(20)
                .map(auditLogMapper::toResponse)
                .collect(Collectors.toList()));

            // 缓存结果
            redisTemplate.opsForValue().set(cacheKey, response, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);

            return response;

        } catch (Exception e) {
            logger.error("获取审计统计信息失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "获取审计统计信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public byte[] exportAuditLogs(AuditLogQueryRequest request) {
        logger.info("导出审计日志: 查询条件={}", request);

        try {
            // 设置导出限制，防止数据量过大
            request.setSize(Math.min(request.getSize(), 10000));

            // 查询数据
            PageResult<AuditLogResponse> pageResult = queryAuditLogs(request);

            // 导出Excel
            return excelExportUtil.exportAuditLogs(pageResult.getContent());

        } catch (Exception e) {
            logger.error("导出审计日志失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "导出审计日志失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Long cleanupExpiredLogs(LocalDateTime beforeDate) {
        logger.info("清理过期日志: 清理{}之前的日志", beforeDate);

        try {
            // 查询要清理的日志数量
            Specification<AuditLog> spec = (root, query, criteriaBuilder) -> {
                List<Predicate> predicates = new ArrayList<>();
                predicates.add(criteriaBuilder.equal(root.get("deleted"), false));
                predicates.add(criteriaBuilder.lessThan(root.get("createTime"), beforeDate));
                return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
            };

            long count = auditLogRepository.count(spec);

            if (count > 0) {
                // 软删除过期日志
                List<AuditLog> expiredLogs = auditLogRepository.findAll(spec);
                expiredLogs.forEach(log -> log.setDeleted(true));
                auditLogRepository.saveAll(expiredLogs);

                // 清除缓存
                clearRelatedCache();

                logger.info("清理过期日志完成: 清理数量={}", count);
            }

            return count;

        } catch (Exception e) {
            logger.error("清理过期日志失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "清理过期日志失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public boolean verifyLogIntegrity(LocalDateTime startTime, LocalDateTime endTime) {
        logger.debug("验证日志完整性: 开始时间={}, 结束时间={}", startTime, endTime);

        try {
            // 简单的完整性验证：检查是否有数据缺失或异常
            long totalCount = auditLogRepository.countOperationsByTimeRange(startTime, endTime);

            // 检查是否有异常的时间跳跃（这里简化处理）
            if (totalCount == 0) {
                return true; // 没有数据也算完整
            }

            // 可以添加更复杂的完整性验证逻辑
            // 例如：检查哈希值、检查时间序列连续性等

            return true;

        } catch (Exception e) {
            logger.error("验证日志完整性失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 转换统计结果为Map
     */
    private Map<String, Long> convertToMap(List<Object[]> results) {
        return results.stream()
            .collect(Collectors.toMap(
                result -> String.valueOf(result[0]),
                result -> ((Number) result[1]).longValue(),
                (existing, replacement) -> existing,
                LinkedHashMap::new
            ));
    }

    /**
     * 转换用户活动统计
     */
    private List<AuditStatisticsResponse.UserActivityStats> convertToUserStats(List<Object[]> results) {
        return results.stream()
            .map(result -> new AuditStatisticsResponse.UserActivityStats(
                ((Number) result[0]).longValue(),
                String.valueOf(result[1]),
                String.valueOf(result[2]),
                ((Number) result[3]).longValue()
            ))
            .collect(Collectors.toList());
    }

    /**
     * 转换IP活动统计
     */
    private List<AuditStatisticsResponse.IpActivityStats> convertToIpStats(List<Object[]> results) {
        return results.stream()
            .map(result -> new AuditStatisticsResponse.IpActivityStats(
                String.valueOf(result[0]),
                ((Number) result[1]).longValue(),
                ((Number) result[2]).longValue()
            ))
            .collect(Collectors.toList());
    }

    /**
     * 清除相关缓存
     */
    private void clearRelatedCache() {
        try {
            Set<String> keys = redisTemplate.keys(CACHE_STATS_PREFIX + "*");
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
            }
        } catch (Exception e) {
            logger.warn("清除缓存失败: {}", e.getMessage());
        }
    }
}
