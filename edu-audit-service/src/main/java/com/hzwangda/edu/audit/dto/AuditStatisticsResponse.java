package com.hzwangda.edu.audit.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Map;

/**
 * 审计统计响应DTO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "审计统计响应")
public class AuditStatisticsResponse {

    @Schema(description = "总操作次数")
    private Long totalOperations;

    @Schema(description = "成功操作次数")
    private Long successOperations;

    @Schema(description = "失败操作次数")
    private Long failedOperations;

    @Schema(description = "高风险操作次数")
    private Long highRiskOperations;

    @Schema(description = "活跃用户数")
    private Long activeUsers;

    @Schema(description = "按操作类型统计")
    private Map<String, Long> operationTypeStats;

    @Schema(description = "按业务模块统计")
    private Map<String, Long> moduleStats;

    @Schema(description = "按操作结果统计")
    private Map<String, Long> resultStats;

    @Schema(description = "按风险级别统计")
    private Map<String, Long> riskLevelStats;

    @Schema(description = "按小时统计（24小时）")
    private Map<String, Long> hourlyStats;

    @Schema(description = "按日期统计（最近7天）")
    private Map<String, Long> dailyStats;

    @Schema(description = "最活跃用户TOP10")
    private List<UserActivityStats> topActiveUsers;

    @Schema(description = "最频繁IP地址TOP10")
    private List<IpActivityStats> topActiveIps;

    @Schema(description = "最近高风险操作")
    private List<AuditLogResponse> recentHighRiskOperations;

    // 内部类：用户活动统计
    @Schema(description = "用户活动统计")
    public static class UserActivityStats {
        @Schema(description = "用户ID")
        private Long userId;

        @Schema(description = "用户名")
        private String username;

        @Schema(description = "真实姓名")
        private String realName;

        @Schema(description = "操作次数")
        private Long operationCount;

        // 构造函数
        public UserActivityStats() {
        }

        public UserActivityStats(Long userId, String username, String realName, Long operationCount) {
            this.userId = userId;
            this.username = username;
            this.realName = realName;
            this.operationCount = operationCount;
        }

        // Getter and Setter methods
        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getRealName() {
            return realName;
        }

        public void setRealName(String realName) {
            this.realName = realName;
        }

        public Long getOperationCount() {
            return operationCount;
        }

        public void setOperationCount(Long operationCount) {
            this.operationCount = operationCount;
        }
    }

    // 内部类：IP活动统计
    @Schema(description = "IP活动统计")
    public static class IpActivityStats {
        @Schema(description = "IP地址")
        private String ipAddress;

        @Schema(description = "操作次数")
        private Long operationCount;

        @Schema(description = "用户数量")
        private Long userCount;

        // 构造函数
        public IpActivityStats() {
        }

        public IpActivityStats(String ipAddress, Long operationCount, Long userCount) {
            this.ipAddress = ipAddress;
            this.operationCount = operationCount;
            this.userCount = userCount;
        }

        // Getter and Setter methods
        public String getIpAddress() {
            return ipAddress;
        }

        public void setIpAddress(String ipAddress) {
            this.ipAddress = ipAddress;
        }

        public Long getOperationCount() {
            return operationCount;
        }

        public void setOperationCount(Long operationCount) {
            this.operationCount = operationCount;
        }

        public Long getUserCount() {
            return userCount;
        }

        public void setUserCount(Long userCount) {
            this.userCount = userCount;
        }
    }

    // 构造函数
    public AuditStatisticsResponse() {
    }

    // Getter and Setter methods
    public Long getTotalOperations() {
        return totalOperations;
    }

    public void setTotalOperations(Long totalOperations) {
        this.totalOperations = totalOperations;
    }

    public Long getSuccessOperations() {
        return successOperations;
    }

    public void setSuccessOperations(Long successOperations) {
        this.successOperations = successOperations;
    }

    public Long getFailedOperations() {
        return failedOperations;
    }

    public void setFailedOperations(Long failedOperations) {
        this.failedOperations = failedOperations;
    }

    public Long getHighRiskOperations() {
        return highRiskOperations;
    }

    public void setHighRiskOperations(Long highRiskOperations) {
        this.highRiskOperations = highRiskOperations;
    }

    public Long getActiveUsers() {
        return activeUsers;
    }

    public void setActiveUsers(Long activeUsers) {
        this.activeUsers = activeUsers;
    }

    public Map<String, Long> getOperationTypeStats() {
        return operationTypeStats;
    }

    public void setOperationTypeStats(Map<String, Long> operationTypeStats) {
        this.operationTypeStats = operationTypeStats;
    }

    public Map<String, Long> getModuleStats() {
        return moduleStats;
    }

    public void setModuleStats(Map<String, Long> moduleStats) {
        this.moduleStats = moduleStats;
    }

    public Map<String, Long> getResultStats() {
        return resultStats;
    }

    public void setResultStats(Map<String, Long> resultStats) {
        this.resultStats = resultStats;
    }

    public Map<String, Long> getRiskLevelStats() {
        return riskLevelStats;
    }

    public void setRiskLevelStats(Map<String, Long> riskLevelStats) {
        this.riskLevelStats = riskLevelStats;
    }

    public Map<String, Long> getHourlyStats() {
        return hourlyStats;
    }

    public void setHourlyStats(Map<String, Long> hourlyStats) {
        this.hourlyStats = hourlyStats;
    }

    public Map<String, Long> getDailyStats() {
        return dailyStats;
    }

    public void setDailyStats(Map<String, Long> dailyStats) {
        this.dailyStats = dailyStats;
    }

    public List<UserActivityStats> getTopActiveUsers() {
        return topActiveUsers;
    }

    public void setTopActiveUsers(List<UserActivityStats> topActiveUsers) {
        this.topActiveUsers = topActiveUsers;
    }

    public List<IpActivityStats> getTopActiveIps() {
        return topActiveIps;
    }

    public void setTopActiveIps(List<IpActivityStats> topActiveIps) {
        this.topActiveIps = topActiveIps;
    }

    public List<AuditLogResponse> getRecentHighRiskOperations() {
        return recentHighRiskOperations;
    }

    public void setRecentHighRiskOperations(List<AuditLogResponse> recentHighRiskOperations) {
        this.recentHighRiskOperations = recentHighRiskOperations;
    }
}
