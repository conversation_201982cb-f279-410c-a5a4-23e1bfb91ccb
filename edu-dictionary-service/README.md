# 字典配置服务 (hky-dictionary-service)

## 服务概述

字典配置服务是业务管理系统的核心平台服务之一，为整个系统提供统一的字典数据管理功能。

## 主要功能

### 核心功能
- **字典类型管理**：支持系统字典和业务字典的分类管理
- **字典项管理**：完整的增删改查操作，支持排序和状态管理
- **多级字典结构**：支持树形结构的字典数据组织
- **缓存机制**：实现Redis缓存提升查询性能
- **数据导入导出**：支持Excel格式的批量数据操作
- **使用统计监控**：记录字典使用频次和访问统计
- **数据校验**：确保字典数据的完整性和一致性

### 技术特性
- 基于Spring Boot 2.7.x框架
- 使用MyBatis Plus进行数据访问
- 集成Redis缓存机制
- 提供完整的RESTful API
- 支持Swagger API文档
- 完整的异常处理和日志记录

## 服务配置

- **服务端口**：8008
- **数据库**：PostgreSQL (hky_hr_db)
- **缓存**：Redis (database: 5)
- **API文档**：http://localhost:8008/swagger-ui.html

## 快速开始

### 环境要求
- JDK 17+
- PostgreSQL 15+
- Redis 7.0+
- Maven 3.8+

### 启动服务
```bash
cd services/platform-services/hky-dictionary-service
mvn spring-boot:run
```

### 访问服务
- API文档：http://localhost:8008/swagger-ui.html
- 健康检查：http://localhost:8008/actuator/health

## API接口

### 字典类型管理
- `GET /api/dict-types` - 查询字典类型列表
- `POST /api/dict-types` - 创建字典类型
- `PUT /api/dict-types/{id}` - 更新字典类型
- `DELETE /api/dict-types/{id}` - 删除字典类型

### 字典项管理
- `GET /api/dict-items` - 查询字典项列表
- `POST /api/dict-items` - 创建字典项
- `PUT /api/dict-items/{id}` - 更新字典项
- `DELETE /api/dict-items/{id}` - 删除字典项

### 缓存管理
- `POST /api/cache/refresh` - 刷新缓存
- `POST /api/cache/clear` - 清理缓存
- `GET /api/cache/stats` - 缓存统计

### 导入导出
- `POST /api/import/excel` - Excel导入
- `GET /api/export/excel` - Excel导出
- `GET /api/export/template` - 下载模板

## 数据模型

### 字典类型表 (dict_type)
- id: 主键ID
- type_code: 类型编码
- type_name: 类型名称
- parent_id: 父级ID
- sort_order: 排序
- status: 状态
- description: 描述

### 字典项表 (dict_item)
- id: 主键ID
- type_id: 字典类型ID
- item_code: 字典编码
- item_name: 字典名称
- item_value: 字典值
- parent_id: 父级ID
- sort_order: 排序
- status: 状态
- description: 描述

## 缓存策略

- **缓存键前缀**：`dict:`
- **缓存过期时间**：24小时
- **缓存刷新策略**：定时刷新 + 手动刷新
- **缓存预热**：服务启动时自动加载热门字典

## 监控指标

- 字典访问频次统计
- 缓存命中率监控
- API响应时间统计
- 数据导入导出记录

## 开发指南

### 代码结构
```
src/main/java/com/hky/hr/dictionary/
├── config/          # 配置类
├── controller/      # 控制器
├── entity/          # 实体类
├── mapper/          # 数据访问层
├── service/         # 业务逻辑层
├── dto/             # 数据传输对象
├── enums/           # 枚举类
└── util/            # 工具类
```

### 开发规范
- 遵循RESTful API设计规范
- 使用统一的异常处理机制
- 完整的参数校验和数据验证
- 详细的日志记录和监控埋点

## 版本历史

- v1.0.0 - 初始版本，实现基础字典管理功能
