package com.hzwangda.edu.dictionary.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;

/**
 * 字典导入导出日志实体类
 * 记录字典数据的导入导出操作
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "dict_import_export_log")
public class DictImportExportLog extends BaseEntity {

    /**
     * 操作类型 (IMPORT: 导入, EXPORT: 导出)
     */
    @Column(name = "operation_type", length = 20, nullable = false)
    private String operationType;

    /**
     * 字典类型ID（可为空，表示全部类型）
     */
    @Column(name = "type_id", length = 36)
    private String typeId;

    /**
     * 文件名称
     */
    @Column(name = "file_name", length = 255)
    private String fileName;

    /**
     * 文件路径
     */
    @Column(name = "file_path", length = 500)
    private String filePath;

    /**
     * 文件大小（字节）
     */
    @Column(name = "file_size")
    private Long fileSize;

    /**
     * 操作状态 (SUCCESS: 成功, FAILED: 失败, PROCESSING: 处理中)
     */
    @Column(name = "status", length = 20, nullable = false)
    private String status;

    /**
     * 处理记录数
     */
    @Column(name = "record_count")
    private Integer recordCount;

    /**
     * 成功记录数
     */
    @Column(name = "success_count")
    private Integer successCount;

    /**
     * 失败记录数
     */
    @Column(name = "failed_count")
    private Integer failedCount;

    /**
     * 错误信息
     */
    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    /**
     * 操作耗时（毫秒）
     */
    @Column(name = "duration")
    private Long duration;

    /**
     * 操作人ID
     */
    @Column(name = "operator_id", length = 36)
    private String operatorId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name", length = 100)
    private String operatorName;

    /**
     * 备注信息
     */
    @Column(name = "remark", length = 500)
    private String remark;
}
