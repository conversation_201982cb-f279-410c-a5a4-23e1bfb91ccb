package com.hzwangda.edu.dictionary.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 字典项实体类
 * 存储具体的字典数据项
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "dict_item", indexes = {
    @Index(name = "idx_dict_item_type", columnList = "type_id"),
    @Index(name = "idx_dict_item_code", columnList = "type_id,item_code", unique = true),
    @Index(name = "idx_dict_item_parent", columnList = "parent_id"),
    @Index(name = "idx_dict_item_status", columnList = "status")
})
public class DictItem extends BaseEntity {

    /**
     * 字典类型ID
     */
    @NotBlank(message = "字典类型ID不能为空")
    @Column(name = "type_id", length = 36, nullable = false)
    private String typeId;

    /**
     * 字典编码（在同一类型下唯一）
     */
    @NotBlank(message = "字典编码不能为空")
    @Size(max = 100, message = "字典编码长度不能超过100个字符")
    @Column(name = "item_code", length = 100, nullable = false)
    private String itemCode;

    /**
     * 字典名称
     */
    @NotBlank(message = "字典名称不能为空")
    @Size(max = 200, message = "字典名称长度不能超过200个字符")
    @Column(name = "item_name", length = 200, nullable = false)
    private String itemName;

    /**
     * 字典值
     */
    @Size(max = 500, message = "字典值长度不能超过500个字符")
    @Column(name = "item_value", length = 500)
    private String itemValue;

    /**
     * 父级字典项ID（支持多级字典）
     */
    @Column(name = "parent_id", length = 36)
    private String parentId;

    /**
     * 字典项级别（1:一级, 2:二级, 3:三级...）
     */
    @Column(name = "item_level")
    private Integer itemLevel = 1;

    /**
     * 字典项路径（如：1/2/3，便于查询所有子级）
     */
    @Column(name = "item_path", length = 1000)
    private String itemPath;

    /**
     * 排序号
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    /**
     * 状态 (0: 禁用, 1: 启用)
     */
    @Column(name = "status")
    private Integer status = 1;

    /**
     * 是否系统内置 (0: 否, 1: 是)
     * 系统内置的字典项不允许删除
     */
    @Column(name = "is_system")
    private Integer isSystem = 0;

    /**
     * 是否允许添加子项 (0: 否, 1: 是)
     */
    @Column(name = "allow_children")
    private Integer allowChildren = 0;

    /**
     * 字典项颜色（用于前端显示）
     */
    @Size(max = 20, message = "颜色值长度不能超过20个字符")
    @Column(name = "item_color", length = 20)
    private String itemColor;

    /**
     * 字典项图标（用于前端显示）
     */
    @Size(max = 100, message = "图标值长度不能超过100个字符")
    @Column(name = "item_icon", length = 100)
    private String itemIcon;

    /**
     * 描述信息
     */
    @Size(max = 500, message = "描述信息长度不能超过500个字符")
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 备注信息
     */
    @Size(max = 1000, message = "备注信息长度不能超过1000个字符")
    @Column(name = "remark", length = 1000)
    private String remark;

    /**
     * 扩展属性（JSON格式）
     */
    @Column(name = "extra_properties", columnDefinition = "TEXT")
    private String extraProperties;
}
