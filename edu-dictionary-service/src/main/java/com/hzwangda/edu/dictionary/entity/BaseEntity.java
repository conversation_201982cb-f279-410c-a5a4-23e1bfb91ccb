package com.hzwangda.edu.dictionary.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 基础实体类
 * 包含公共字段：ID、创建时间、更新时间、创建人、更新人、逻辑删除标记
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
@Schema(description = "基础实体")
public abstract class BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", length = 36)
    @Schema(description = "主键ID")
    private String id;

    /**
     * 创建时间
     */
    @CreatedDate
    @Column(name = "create_time", updatable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @LastModifiedDate
    @Column(name = "update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    @CreatedBy
    @Column(name = "create_by", length = 100, updatable = false)
    @Schema(description = "创建人ID")
    private String createBy;

    /**
     * 更新人ID
     */
    @LastModifiedBy
    @Column(name = "update_by", length = 100)
    @Schema(description = "更新人ID")
    private String updateBy;

    /**
     * 逻辑删除标记 (0: 未删除, 1: 已删除)
     */
    @Column(name = "deleted", nullable = false)
    @Schema(description = "逻辑删除标记", hidden = true)
    private Integer deleted = 0;

    /**
     * 版本号（乐观锁）
     */
    @Version
    @Column(name = "version", nullable = false)
    @Schema(description = "版本号", hidden = true)
    private Integer version = 1;
}
