package com.hzwangda.edu.dictionary.service.impl;

import com.hzwangda.edu.common.exception.BusinessException;
import com.hzwangda.edu.common.result.ResultCode;
import com.hzwangda.edu.dictionary.dto.DictTypeDTO;
import com.hzwangda.edu.dictionary.dto.DictTypeQueryRequest;
import com.hzwangda.edu.dictionary.entity.DictType;
import com.hzwangda.edu.dictionary.enums.DictStatusEnum;
import com.hzwangda.edu.dictionary.repository.DictTypeRepository;
import com.hzwangda.edu.dictionary.service.DictTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import jakarta.persistence.criteria.Predicate;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 字典类型服务实现类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@Service
public class DictTypeServiceImpl implements DictTypeService {

    @Autowired
    private DictTypeRepository dictTypeRepository;

    @Override
    public Page<DictTypeDTO> queryDictTypes(DictTypeQueryRequest request) {
        log.debug("分页查询字典类型: {}", request);

        // 创建分页对象
        Pageable pageable = PageRequest.of(
            request.getPage(),
            request.getSize(),
            Sort.by(Sort.Direction.ASC, "sortOrder")
        );

        // 构建查询条件
        Specification<DictType> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 逻辑删除条件
            predicates.add(criteriaBuilder.equal(root.get("deleted"), 0));

            // 类型编码模糊查询
            if (StringUtils.hasText(request.getTypeCode())) {
                predicates.add(criteriaBuilder.like(root.get("typeCode"), "%" + request.getTypeCode() + "%"));
            }

            // 类型名称模糊查询
            if (StringUtils.hasText(request.getTypeName())) {
                predicates.add(criteriaBuilder.like(root.get("typeName"), "%" + request.getTypeName() + "%"));
            }

            // 状态查询
            if (request.getStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), request.getStatus()));
            }

            // 父级ID查询
            if (StringUtils.hasText(request.getParentId())) {
                predicates.add(criteriaBuilder.equal(root.get("parentId"), request.getParentId()));
            }

            // 是否系统内置查询
            if (request.getIsSystem() != null) {
                predicates.add(criteriaBuilder.equal(root.get("isSystem"), request.getIsSystem()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        Page<DictType> resultPage = dictTypeRepository.findAll(spec, pageable);

        // 转换为DTO
        return resultPage.map(this::convertToDTO);
    }

    @Override
    @Cacheable(value = "dict_type", key = "#id")
    public DictTypeDTO getDictTypeById(String id) {
        log.debug("根据ID查询字典类型: {}", id);

        Optional<DictType> dictTypeOpt = dictTypeRepository.findById(id);
        if (dictTypeOpt.isEmpty() || dictTypeOpt.get().getDeleted() != 0) {
            throw new BusinessException(ResultCode.NOT_FOUND, "字典类型不存在");
        }

        return convertToDTO(dictTypeOpt.get());
    }

    @Override
    @Cacheable(value = "dict_type", key = "'code:' + #typeCode")
    public DictTypeDTO getDictTypeByCode(String typeCode) {
        log.debug("根据类型编码查询字典类型: {}", typeCode);

        Optional<DictType> dictTypeOpt = dictTypeRepository.findByTypeCodeAndDeleted(typeCode, 0);
        if (dictTypeOpt.isEmpty()) {
            throw new BusinessException(ResultCode.NOT_FOUND, "字典类型不存在");
        }

        return convertToDTO(dictTypeOpt.get());
    }

    @Override
    @Transactional
    public DictTypeDTO createDictType(DictTypeDTO dictTypeDTO) {
        log.info("创建字典类型: {}", dictTypeDTO.getTypeCode());

        // 验证类型编码唯一性
        if (isTypeCodeExists(dictTypeDTO.getTypeCode(), null)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "类型编码已存在");
        }

        DictType dictType = new DictType();
        BeanUtils.copyProperties(dictTypeDTO, dictType);
        dictType.setId(UUID.randomUUID().toString());

        // 设置默认值
        if (dictType.getStatus() == null) {
            dictType.setStatus(DictStatusEnum.ENABLED.getCode());
        }
        if (dictType.getSortOrder() == null) {
            dictType.setSortOrder(0);
        }
        if (dictType.getIsSystem() == null) {
            dictType.setIsSystem(0);
        }
        if (dictType.getAllowChildren() == null) {
            dictType.setAllowChildren(1);
        }

        // 计算层级和路径
        calculateLevelAndPath(dictType);

        DictType savedType = dictTypeRepository.save(dictType);

        return convertToDTO(savedType);
    }

    @Override
    @Transactional
    @CacheEvict(value = "dict_type", allEntries = true)
    public DictTypeDTO updateDictType(String id, DictTypeDTO dictTypeDTO) {
        log.info("更新字典类型: {}", id);

        Optional<DictType> existingTypeOpt = dictTypeRepository.findById(id);
        if (existingTypeOpt.isEmpty() || existingTypeOpt.get().getDeleted() != 0) {
            throw new BusinessException(ResultCode.NOT_FOUND, "字典类型不存在");
        }

        DictType existingType = existingTypeOpt.get();

        // 验证类型编码唯一性
        if (!existingType.getTypeCode().equals(dictTypeDTO.getTypeCode()) &&
            isTypeCodeExists(dictTypeDTO.getTypeCode(), id)) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "类型编码已存在");
        }

        // 更新字段
        BeanUtils.copyProperties(dictTypeDTO, existingType, "id", "createTime", "createBy", "deleted", "version");

        // 重新计算层级和路径
        calculateLevelAndPath(existingType);

        DictType updatedType = dictTypeRepository.save(existingType);

        return convertToDTO(updatedType);
    }

    @Override
    @Transactional
    @CacheEvict(value = "dict_type", allEntries = true)
    public boolean deleteDictType(String id) {
        log.info("删除字典类型: {}", id);

        Optional<DictType> dictTypeOpt = dictTypeRepository.findById(id);
        if (dictTypeOpt.isEmpty() || dictTypeOpt.get().getDeleted() != 0) {
            throw new BusinessException(ResultCode.NOT_FOUND, "字典类型不存在");
        }

        DictType dictType = dictTypeOpt.get();

        // 检查是否为系统内置
        if (dictType.getIsSystem() == 1) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "系统内置字典类型不允许删除");
        }

        // 检查是否有子类型
        List<DictType> children = dictTypeRepository.findByParentIdAndDeletedOrderBySortOrderAsc(id, 0);
        if (!children.isEmpty()) {
            throw new BusinessException(ResultCode.BAD_REQUEST, "存在子类型，不允许删除");
        }

        // 逻辑删除
        dictType.setDeleted(1);
        dictTypeRepository.save(dictType);

        return true;
    }

    @Override
    @Transactional
    @CacheEvict(value = "dict_type", allEntries = true)
    public int batchDeleteDictTypes(List<String> ids) {
        log.info("批量删除字典类型: {}", ids);

        int successCount = 0;
        for (String id : ids) {
            try {
                if (deleteDictType(id)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.warn("删除字典类型失败: {}, 错误: {}", id, e.getMessage());
            }
        }

        return successCount;
    }

    @Override
    @Cacheable(value = "dict_type_roots")
    public List<DictTypeDTO> getRootNodes() {
        log.debug("获取根节点");

        List<DictType> rootNodes = dictTypeRepository.findRootNodes();
        return rootNodes.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Cacheable(value = "dict_type_children", key = "#parentId")
    public List<DictTypeDTO> getChildrenByParentId(String parentId) {
        log.debug("根据父级ID查询子节点: {}", parentId);

        List<DictType> children = dictTypeRepository.findByParentIdAndDeletedOrderBySortOrderAsc(parentId, 0);
        return children.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "dict_type_children", key = "#parentId")
    public List<DictTypeDTO> getChildren(String parentId) {
        return getChildrenByParentId(parentId);
    }

    @Override
    @Cacheable(value = "dict_type_tree")
    public List<DictTypeDTO> getTreeStructure() {
        log.debug("获取树形结构");

        List<DictTypeDTO> rootNodes = getRootNodes();
        for (DictTypeDTO root : rootNodes) {
            buildTree(root);
        }

        return rootNodes;
    }

    @Override
    @Cacheable(value = "dict_type_enabled")
    public List<DictTypeDTO> getEnabledDictTypes() {
        log.debug("获取启用状态的字典类型");

        List<DictType> enabledTypes = dictTypeRepository.findByStatusAndDeletedOrderBySortOrderAsc(1, 0);
        return enabledTypes.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public boolean isTypeCodeExists(String typeCode, String excludeId) {
        long count = dictTypeRepository.countByTypeCodeAndExcludeId(typeCode, excludeId);
        return count > 0;
    }

    @Override
    @CacheEvict(value = "dict_type", key = "#id")
    public boolean updateStatus(String id, Integer status) {
        log.info("更新字典类型状态: {}, 状态: {}", id, status);

        Optional<DictType> dictTypeOpt = dictTypeRepository.findById(id);
        if (dictTypeOpt.isEmpty() || dictTypeOpt.get().getDeleted() != 0) {
            return false;
        }

        DictType dictType = dictTypeOpt.get();
        dictType.setStatus(status);
        dictTypeRepository.save(dictType);

        return true;
    }

    @Override
    @CacheEvict(value = {"dict_type", "dict_type_roots", "dict_type_children", "dict_type_tree", "dict_type_enabled"}, allEntries = true)
    public void refreshCache() {
        log.info("刷新字典类型缓存");
    }

    @Override
    @CacheEvict(value = {"dict_type", "dict_type_roots", "dict_type_children", "dict_type_tree", "dict_type_enabled"}, allEntries = true)
    public void clearCache() {
        log.info("清理字典类型缓存");
    }

    @Override
    public Object getCacheStats() {
        log.debug("获取字典类型缓存统计信息");
        // 这里可以实现具体的缓存统计逻辑
        return "缓存统计功能待实现";
    }

    /**
     * 构建树形结构
     */
    private void buildTree(DictTypeDTO parent) {
        List<DictTypeDTO> children = getChildrenByParentId(parent.getId());
        parent.setChildren(children);

        for (DictTypeDTO child : children) {
            buildTree(child);
        }
    }

    /**
     * 计算层级和路径
     */
    private void calculateLevelAndPath(DictType dictType) {
        if (StringUtils.hasText(dictType.getParentId())) {
            Optional<DictType> parentOpt = dictTypeRepository.findById(dictType.getParentId());
            if (parentOpt.isPresent()) {
                DictType parent = parentOpt.get();
                dictType.setTypeLevel(parent.getTypeLevel() + 1);
                dictType.setTypePath(parent.getTypePath() + "/" + dictType.getId());
            } else {
                dictType.setTypeLevel(1);
                dictType.setTypePath(dictType.getId());
            }
        } else {
            dictType.setTypeLevel(1);
            dictType.setTypePath(dictType.getId());
        }
    }

    /**
     * 转换为DTO
     */
    private DictTypeDTO convertToDTO(DictType dictType) {
        DictTypeDTO dto = new DictTypeDTO();
        BeanUtils.copyProperties(dictType, dto);
        return dto;
    }
}
