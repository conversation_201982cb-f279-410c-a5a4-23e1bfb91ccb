package com.hzwangda.edu.dictionary.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 字典状态枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Getter
@AllArgsConstructor
public enum DictStatusEnum {

    /**
     * 禁用
     */
    DISABLED(0, "禁用"),

    /**
     * 启用
     */
    ENABLED(1, "启用");

    private final Integer code;
    private final String description;

    /**
     * 根据代码获取枚举
     */
    public static DictStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DictStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效状态
     */
    public static boolean isValid(Integer code) {
        return getByCode(code) != null;
    }
}
