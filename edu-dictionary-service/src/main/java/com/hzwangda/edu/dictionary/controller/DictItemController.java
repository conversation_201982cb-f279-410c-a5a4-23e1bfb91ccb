package com.hzwangda.edu.dictionary.controller;

import org.springframework.data.domain.Page;
import com.hzwangda.edu.common.dto.PageResult;
import com.hzwangda.edu.common.result.Result;
import com.hzwangda.edu.dictionary.dto.DictItemDTO;
import com.hzwangda.edu.dictionary.dto.DictItemQueryRequest;
import com.hzwangda.edu.dictionary.service.DictItemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 字典项控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/dict-items")
@Tag(name = "字典项管理", description = "字典项的增删改查操作")
@Validated
public class DictItemController {

    @Autowired
    private DictItemService dictItemService;

    @Operation(summary = "分页查询字典项", description = "根据条件分页查询字典项列表")
    @PostMapping("/query")
    public Result<PageResult<DictItemDTO>> queryDictItems(@Valid @RequestBody DictItemQueryRequest request) {
        log.info("分页查询字典项: {}", request);

        Page<DictItemDTO> page = dictItemService.queryDictItems(request);

        PageResult<DictItemDTO> pageResult = PageResult.of(
                page.getContent(),
                page.getTotalElements(),
                page.getNumber(),
                page.getSize()
        );

        return Result.success(pageResult);
    }

    @Operation(summary = "根据ID查询字典项", description = "根据字典项ID查询详细信息")
    @GetMapping("/{id}")
    public Result<DictItemDTO> getDictItemById(
            @Parameter(description = "字典项ID", required = true)
            @PathVariable @NotBlank(message = "字典项ID不能为空") String id) {
        log.info("根据ID查询字典项: {}", id);

        DictItemDTO dictItem = dictItemService.getDictItemById(id);
        return Result.success(dictItem);
    }

    @Operation(summary = "根据类型ID查询字典项", description = "根据字典类型ID查询字典项列表")
    @GetMapping("/type/{typeId}")
    public Result<List<DictItemDTO>> getDictItemsByTypeId(
            @Parameter(description = "字典类型ID", required = true)
            @PathVariable @NotBlank(message = "字典类型ID不能为空") String typeId) {
        log.info("根据类型ID查询字典项: {}", typeId);

        List<DictItemDTO> dictItems = dictItemService.getDictItemsByTypeId(typeId);
        return Result.success(dictItems);
    }

    @Operation(summary = "根据类型编码查询字典项", description = "根据字典类型编码查询字典项列表")
    @GetMapping("/type-code/{typeCode}")
    public Result<List<DictItemDTO>> getDictItemsByTypeCode(
            @Parameter(description = "字典类型编码", required = true)
            @PathVariable @NotBlank(message = "字典类型编码不能为空") String typeCode) {
        log.info("根据类型编码查询字典项: {}", typeCode);

        List<DictItemDTO> dictItems = dictItemService.getDictItemsByTypeCode(typeCode);
        return Result.success(dictItems);
    }

    @Operation(summary = "根据类型编码查询启用的字典项", description = "根据字典类型编码查询启用状态的字典项列表")
    @GetMapping("/type-code/{typeCode}/enabled")
    public Result<List<DictItemDTO>> getEnabledDictItemsByTypeCode(
            @Parameter(description = "字典类型编码", required = true)
            @PathVariable @NotBlank(message = "字典类型编码不能为空") String typeCode) {
        log.info("根据类型编码查询启用的字典项: {}", typeCode);

        List<DictItemDTO> dictItems = dictItemService.getEnabledDictItemsByTypeCode(typeCode);
        return Result.success(dictItems);
    }

    @Operation(summary = "根据类型ID和编码查询字典项", description = "根据字典类型ID和字典编码查询字典项")
    @GetMapping("/type/{typeId}/code/{itemCode}")
    public Result<DictItemDTO> getDictItemByTypeIdAndCode(
            @Parameter(description = "字典类型ID", required = true)
            @PathVariable @NotBlank(message = "字典类型ID不能为空") String typeId,
            @Parameter(description = "字典编码", required = true)
            @PathVariable @NotBlank(message = "字典编码不能为空") String itemCode) {
        log.info("根据类型ID和编码查询字典项: typeId={}, itemCode={}", typeId, itemCode);

        DictItemDTO dictItem = dictItemService.getDictItemByTypeIdAndCode(typeId, itemCode);
        return Result.success(dictItem);
    }

    @Operation(summary = "创建字典项", description = "创建新的字典项")
    @PostMapping
    public Result<DictItemDTO> createDictItem(@Valid @RequestBody DictItemDTO dictItemDTO) {
        log.info("创建字典项: typeId={}, itemCode={}", dictItemDTO.getTypeId(), dictItemDTO.getItemCode());

        DictItemDTO createdItem = dictItemService.createDictItem(dictItemDTO);
        return Result.success(createdItem);
    }

    @Operation(summary = "更新字典项", description = "根据ID更新字典项信息")
    @PutMapping("/{id}")
    public Result<DictItemDTO> updateDictItem(
            @Parameter(description = "字典项ID", required = true)
            @PathVariable @NotBlank(message = "字典项ID不能为空") String id,
            @Valid @RequestBody DictItemDTO dictItemDTO) {
        log.info("更新字典项: {}", id);

        DictItemDTO updatedItem = dictItemService.updateDictItem(id, dictItemDTO);
        return Result.success(updatedItem);
    }

    @Operation(summary = "删除字典项", description = "根据ID删除字典项")
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteDictItem(
            @Parameter(description = "字典项ID", required = true)
            @PathVariable @NotBlank(message = "字典项ID不能为空") String id) {
        log.info("删除字典项: {}", id);

        boolean result = dictItemService.deleteDictItem(id);
        return Result.success(result);
    }

    @Operation(summary = "批量删除字典项", description = "根据ID列表批量删除字典项")
    @DeleteMapping("/batch")
    public Result<Integer> batchDeleteDictItems(
            @Parameter(description = "字典项ID列表", required = true)
            @RequestBody @NotEmpty(message = "ID列表不能为空") List<String> ids) {
        log.info("批量删除字典项: {}", ids);

        int deletedCount = dictItemService.batchDeleteDictItems(ids);
        return Result.success(deletedCount);
    }

    @Operation(summary = "获取子项", description = "根据父级ID获取子项列表")
    @GetMapping("/children/{parentId}")
    public Result<List<DictItemDTO>> getChildrenByParentId(
            @Parameter(description = "父级ID", required = true)
            @PathVariable @NotBlank(message = "父级ID不能为空") String parentId) {
        log.info("获取子项列表: {}", parentId);

        List<DictItemDTO> children = dictItemService.getChildrenByParentId(parentId);
        return Result.success(children);
    }

    @Operation(summary = "获取树形结构", description = "根据类型ID获取树形结构")
    @GetMapping("/tree/{typeId}")
    public Result<List<DictItemDTO>> getTreeStructure(
            @Parameter(description = "字典类型ID", required = true)
            @PathVariable @NotBlank(message = "字典类型ID不能为空") String typeId) {
        log.info("获取树形结构: {}", typeId);

        List<DictItemDTO> treeStructure = dictItemService.getTreeStructure(typeId);
        return Result.success(treeStructure);
    }

    @Operation(summary = "检查编码是否存在", description = "检查字典项编码是否已存在")
    @GetMapping("/check-code")
    public Result<Boolean> checkItemCodeExists(
            @Parameter(description = "字典类型ID", required = true)
            @RequestParam @NotBlank(message = "字典类型ID不能为空") String typeId,
            @Parameter(description = "字典编码", required = true)
            @RequestParam @NotBlank(message = "字典编码不能为空") String itemCode,
            @Parameter(description = "排除的ID（用于更新时检查）")
            @RequestParam(required = false) String excludeId) {
        log.info("检查字典编码是否存在: typeId={}, itemCode={}, excludeId={}", typeId, itemCode, excludeId);

        boolean exists = dictItemService.isItemCodeExists(typeId, itemCode, excludeId);
        return Result.success(exists);
    }

    @Operation(summary = "更新状态", description = "更新字典项的启用/禁用状态")
    @PatchMapping("/{id}/status")
    public Result<Boolean> updateStatus(
            @Parameter(description = "字典项ID", required = true)
            @PathVariable @NotBlank(message = "字典项ID不能为空") String id,
            @Parameter(description = "状态值", required = true)
            @RequestParam Integer status) {
        log.info("更新字典项状态: id={}, status={}", id, status);

        boolean result = dictItemService.updateStatus(id, status);
        return Result.success(result);
    }

    @Operation(summary = "刷新缓存", description = "刷新指定类型的字典项缓存")
    @PostMapping("/cache/refresh")
    public Result<Void> refreshCache(
            @Parameter(description = "字典类型编码", required = true)
            @RequestParam @NotBlank(message = "字典类型编码不能为空") String typeCode) {
        log.info("刷新字典项缓存: {}", typeCode);

        dictItemService.refreshCache(typeCode);
        return Result.success();
    }

    @Operation(summary = "清理缓存", description = "清理指定类型的字典项缓存")
    @DeleteMapping("/cache")
    public Result<Void> clearCache(
            @Parameter(description = "字典类型编码", required = true)
            @RequestParam @NotBlank(message = "字典类型编码不能为空") String typeCode) {
        log.info("清理字典项缓存: {}", typeCode);

        dictItemService.clearCache(typeCode);
        return Result.success();
    }
}
