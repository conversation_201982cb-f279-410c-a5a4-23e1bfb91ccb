package com.hzwangda.edu.dictionary.constant;

/**
 * 字典服务常量类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public class DictConstants {

    /**
     * 缓存相关常量
     */
    public static class Cache {
        /** 缓存键前缀 */
        public static final String DICT_CACHE_PREFIX = "dict:";

        /** 字典类型缓存键 */
        public static final String DICT_TYPE_CACHE_KEY = DICT_CACHE_PREFIX + "type:";

        /** 字典项缓存键 */
        public static final String DICT_ITEM_CACHE_KEY = DICT_CACHE_PREFIX + "item:";

        /** 字典树缓存键 */
        public static final String DICT_TREE_CACHE_KEY = DICT_CACHE_PREFIX + "tree:";

        /** 启用字典缓存键 */
        public static final String DICT_ENABLED_CACHE_KEY = DICT_CACHE_PREFIX + "enabled:";

        /** 缓存名称 */
        public static final String DICT_CACHE_NAME = "dictCache";

        /** 缓存过期时间（秒） */
        public static final long CACHE_EXPIRE_SECONDS = 24 * 60 * 60; // 24小时
    }

    /**
     * API路径常量
     */
    public static class ApiPath {
        /** API基础路径 */
        public static final String API_BASE_PATH = "/api";

        /** 字典类型路径 */
        public static final String DICT_TYPE_PATH = API_BASE_PATH + "/dict-types";

        /** 字典项路径 */
        public static final String DICT_ITEM_PATH = API_BASE_PATH + "/dict-items";

        /** 缓存管理路径 */
        public static final String CACHE_PATH = API_BASE_PATH + "/cache";

        /** 导入导出路径 */
        public static final String IMPORT_EXPORT_PATH = API_BASE_PATH + "/import-export";

        /** 统计路径 */
        public static final String STATS_PATH = API_BASE_PATH + "/stats";
    }

    /**
     * 状态常量
     */
    public static class Status {
        /** 禁用 */
        public static final Integer DISABLED = 0;

        /** 启用 */
        public static final Integer ENABLED = 1;
    }

    /**
     * 系统标识常量
     */
    public static class System {
        /** 非系统内置 */
        public static final Integer NOT_SYSTEM = 0;

        /** 系统内置 */
        public static final Integer IS_SYSTEM = 1;
    }

    /**
     * 子项允许常量
     */
    public static class Children {
        /** 不允许子项 */
        public static final Integer NOT_ALLOW = 0;

        /** 允许子项 */
        public static final Integer ALLOW = 1;
    }

    /**
     * 默认值常量
     */
    public static class Default {
        /** 默认排序号 */
        public static final Integer DEFAULT_SORT_ORDER = 0;

        /** 默认级别 */
        public static final Integer DEFAULT_LEVEL = 1;

        /** 根节点路径 */
        public static final String ROOT_PATH = "/";

        /** 路径分隔符 */
        public static final String PATH_SEPARATOR = "/";
    }

    /**
     * 导入导出常量
     */
    public static class ImportExport {
        /** Excel文件扩展名 */
        public static final String EXCEL_EXTENSION = ".xlsx";

        /** 导入模板文件名 */
        public static final String IMPORT_TEMPLATE_NAME = "字典数据导入模板";

        /** 导出文件名前缀 */
        public static final String EXPORT_FILE_PREFIX = "字典数据导出_";

        /** 最大导入记录数 */
        public static final Integer MAX_IMPORT_SIZE = 10000;

        /** 批处理大小 */
        public static final Integer BATCH_SIZE = 1000;
    }

    /**
     * 错误码常量
     */
    public static class ErrorCode {
        /** 字典类型不存在 */
        public static final String DICT_TYPE_NOT_FOUND = "DICT_TYPE_NOT_FOUND";

        /** 字典项不存在 */
        public static final String DICT_ITEM_NOT_FOUND = "DICT_ITEM_NOT_FOUND";

        /** 字典编码已存在 */
        public static final String DICT_CODE_EXISTS = "DICT_CODE_EXISTS";

        /** 系统字典不允许删除 */
        public static final String SYSTEM_DICT_CANNOT_DELETE = "SYSTEM_DICT_CANNOT_DELETE";

        /** 存在子节点不允许删除 */
        public static final String HAS_CHILDREN_CANNOT_DELETE = "HAS_CHILDREN_CANNOT_DELETE";

        /** 导入文件格式错误 */
        public static final String IMPORT_FILE_FORMAT_ERROR = "IMPORT_FILE_FORMAT_ERROR";

        /** 导入数据超过限制 */
        public static final String IMPORT_DATA_EXCEED_LIMIT = "IMPORT_DATA_EXCEED_LIMIT";
    }

    /**
     * 消息常量
     */
    public static class Message {
        /** 操作成功 */
        public static final String SUCCESS = "操作成功";

        /** 创建成功 */
        public static final String CREATE_SUCCESS = "创建成功";

        /** 更新成功 */
        public static final String UPDATE_SUCCESS = "更新成功";

        /** 删除成功 */
        public static final String DELETE_SUCCESS = "删除成功";

        /** 缓存刷新成功 */
        public static final String CACHE_REFRESH_SUCCESS = "缓存刷新成功";

        /** 导入成功 */
        public static final String IMPORT_SUCCESS = "导入成功";

        /** 导出成功 */
        public static final String EXPORT_SUCCESS = "导出成功";
    }
}
