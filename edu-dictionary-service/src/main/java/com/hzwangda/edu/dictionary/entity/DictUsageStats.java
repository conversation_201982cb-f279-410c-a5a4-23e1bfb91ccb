package com.hzwangda.edu.dictionary.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.time.LocalDate;

/**
 * 字典使用统计实体类
 * 记录字典项的使用频次和统计信息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "dict_usage_stats", indexes = {
    @Index(name = "idx_dict_usage_type", columnList = "type_id"),
    @Index(name = "idx_dict_usage_item", columnList = "item_id"),
    @Index(name = "idx_dict_usage_date", columnList = "stat_date")
})
public class DictUsageStats extends BaseEntity {

    /**
     * 字典类型ID
     */
    @Column(name = "type_id", length = 36, nullable = false)
    private String typeId;

    /**
     * 字典项ID
     */
    @Column(name = "item_id", length = 36, nullable = false)
    private String itemId;

    /**
     * 统计日期
     */
    @Column(name = "stat_date", nullable = false)
    private LocalDate statDate;

    /**
     * 访问次数
     */
    @Column(name = "access_count", nullable = false)
    private Long accessCount = 0L;

    /**
     * 查询次数
     */
    @Column(name = "query_count", nullable = false)
    private Long queryCount = 0L;

    /**
     * 使用的模块
     */
    @Column(name = "module_name", length = 100)
    private String moduleName;

    /**
     * 使用的功能
     */
    @Column(name = "function_name", length = 100)
    private String functionName;

    /**
     * 用户ID
     */
    @Column(name = "user_id", length = 36)
    private String userId;

    /**
     * 用户名
     */
    @Column(name = "username", length = 100)
    private String username;

    /**
     * IP地址
     */
    @Column(name = "ip_address", length = 50)
    private String ipAddress;

    /**
     * 用户代理
     */
    @Column(name = "user_agent", length = 500)
    private String userAgent;

    /**
     * 响应时间（毫秒）
     */
    @Column(name = "response_time")
    private Long responseTime;

    /**
     * 备注信息
     */
    @Column(name = "remark", length = 500)
    private String remark;
}
