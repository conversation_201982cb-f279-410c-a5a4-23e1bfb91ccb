package com.hzwangda.edu.dictionary.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作状态枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Getter
@AllArgsConstructor
public enum OperationStatusEnum {

    /**
     * 处理中
     */
    PROCESSING("PROCESSING", "处理中"),

    /**
     * 成功
     */
    SUCCESS("SUCCESS", "成功"),

    /**
     * 失败
     */
    FAILED("FAILED", "失败");

    private final String code;
    private final String description;

    /**
     * 根据代码获取枚举
     */
    public static OperationStatusEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (OperationStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效状态
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }
}
