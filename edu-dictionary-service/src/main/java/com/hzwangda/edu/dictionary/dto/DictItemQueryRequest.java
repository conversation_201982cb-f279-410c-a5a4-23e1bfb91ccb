package com.hzwangda.edu.dictionary.dto;

import com.hzwangda.edu.common.dto.PageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典项查询请求DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "字典项查询请求")
public class DictItemQueryRequest extends PageQuery {

    @Schema(description = "字典类型ID")
    private String typeId;

    @Schema(description = "字典类型编码", example = "USER_STATUS")
    private String typeCode;

    @Schema(description = "字典编码", example = "ACTIVE")
    private String itemCode;

    @Schema(description = "字典名称", example = "激活")
    private String itemName;

    @Schema(description = "字典值", example = "1")
    private String itemValue;

    @Schema(description = "父级字典项ID")
    private String parentId;

    @Schema(description = "状态 (0: 禁用, 1: 启用)", example = "1")
    private Integer status;

    @Schema(description = "是否系统内置 (0: 否, 1: 是)", example = "0")
    private Integer isSystem;

    @Schema(description = "是否查询树形结构", example = "false")
    private Boolean tree = false;

    @Schema(description = "是否只查询启用状态", example = "false")
    private Boolean enabledOnly = false;
}
