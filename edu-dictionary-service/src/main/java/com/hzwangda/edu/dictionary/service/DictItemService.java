package com.hzwangda.edu.dictionary.service;

import org.springframework.data.domain.Page;
import com.hzwangda.edu.dictionary.dto.DictItemDTO;
import com.hzwangda.edu.dictionary.dto.DictItemQueryRequest;

import java.util.List;

/**
 * 字典项服务接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
public interface DictItemService {

    /**
     * 分页查询字典项
     *
     * @param request 查询请求
     * @return 分页结果
     */
    Page<DictItemDTO> queryDictItems(DictItemQueryRequest request);

    /**
     * 根据ID查询字典项
     *
     * @param id 字典项ID
     * @return 字典项DTO
     */
    DictItemDTO getDictItemById(String id);

    /**
     * 根据类型ID查询字典项列表
     *
     * @param typeId 类型ID
     * @return 字典项列表
     */
    List<DictItemDTO> getDictItemsByTypeId(String typeId);

    /**
     * 根据类型编码查询字典项列表
     *
     * @param typeCode 类型编码
     * @return 字典项列表
     */
    List<DictItemDTO> getDictItemsByTypeCode(String typeCode);

    /**
     * 根据类型编码查询启用状态的字典项列表
     *
     * @param typeCode 类型编码
     * @return 启用的字典项列表
     */
    List<DictItemDTO> getEnabledDictItemsByTypeCode(String typeCode);

    /**
     * 根据类型ID和字典编码查询字典项
     *
     * @param typeId 类型ID
     * @param itemCode 字典编码
     * @return 字典项DTO
     */
    DictItemDTO getDictItemByTypeIdAndCode(String typeId, String itemCode);

    /**
     * 创建字典项
     *
     * @param dictItemDTO 字典项DTO
     * @return 创建的字典项DTO
     */
    DictItemDTO createDictItem(DictItemDTO dictItemDTO);

    /**
     * 更新字典项
     *
     * @param id 字典项ID
     * @param dictItemDTO 字典项DTO
     * @return 更新的字典项DTO
     */
    DictItemDTO updateDictItem(String id, DictItemDTO dictItemDTO);

    /**
     * 删除字典项
     *
     * @param id 字典项ID
     * @return 是否删除成功
     */
    boolean deleteDictItem(String id);

    /**
     * 批量删除字典项
     *
     * @param ids 字典项ID列表
     * @return 删除成功的数量
     */
    int batchDeleteDictItems(List<String> ids);

    /**
     * 根据父级ID查询子项
     *
     * @param parentId 父级ID
     * @return 子项列表
     */
    List<DictItemDTO> getChildrenByParentId(String parentId);

    /**
     * 获取树形结构
     *
     * @param typeId 类型ID
     * @return 树形结构列表
     */
    List<DictItemDTO> getTreeStructure(String typeId);

    /**
     * 检查字典编码是否存在
     *
     * @param typeId 类型ID
     * @param itemCode 字典编码
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean isItemCodeExists(String typeId, String itemCode, String excludeId);

    /**
     * 更新字典项状态
     *
     * @param id 字典项ID
     * @param status 状态
     * @return 是否更新成功
     */
    boolean updateStatus(String id, Integer status);

    /**
     * 刷新缓存
     *
     * @param typeCode 类型编码
     */
    void refreshCache(String typeCode);

    /**
     * 清理缓存
     *
     * @param typeCode 类型编码
     */
    void clearCache(String typeCode);
}
