package com.hzwangda.edu.dictionary.controller;

import org.springframework.data.domain.Page;
import com.hzwangda.edu.common.dto.PageResult;
import com.hzwangda.edu.common.result.Result;
import com.hzwangda.edu.dictionary.dto.DictTypeDTO;
import com.hzwangda.edu.dictionary.dto.DictTypeQueryRequest;
import com.hzwangda.edu.dictionary.service.DictTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 字典类型控制器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/dict-types")
@Tag(name = "字典类型管理", description = "字典类型的增删改查操作")
@Validated
public class DictTypeController {

    @Autowired
    private DictTypeService dictTypeService;

    @Operation(summary = "分页查询字典类型", description = "根据条件分页查询字典类型列表")
    @PostMapping("/query")
    public Result<PageResult<DictTypeDTO>> queryDictTypes(@Valid @RequestBody DictTypeQueryRequest request) {
        log.info("分页查询字典类型: {}", request);

        Page<DictTypeDTO> page = dictTypeService.queryDictTypes(request);

        PageResult<DictTypeDTO> pageResult = PageResult.of(
                page.getContent(),
                page.getTotalElements(),
                page.getNumber(),
                page.getSize()
        );

        return Result.success(pageResult);
    }

    @Operation(summary = "根据ID查询字典类型", description = "根据字典类型ID查询详细信息")
    @GetMapping("/{id}")
    public Result<DictTypeDTO> getDictTypeById(
            @Parameter(description = "字典类型ID", required = true)
            @PathVariable @NotBlank(message = "字典类型ID不能为空") String id) {
        log.info("根据ID查询字典类型: {}", id);

        DictTypeDTO dictType = dictTypeService.getDictTypeById(id);
        return Result.success(dictType);
    }

    @Operation(summary = "根据编码查询字典类型", description = "根据字典类型编码查询详细信息")
    @GetMapping("/code/{typeCode}")
    public Result<DictTypeDTO> getDictTypeByCode(
            @Parameter(description = "字典类型编码", required = true)
            @PathVariable @NotBlank(message = "字典类型编码不能为空") String typeCode) {
        log.info("根据编码查询字典类型: {}", typeCode);

        DictTypeDTO dictType = dictTypeService.getDictTypeByCode(typeCode);
        return Result.success(dictType);
    }

    @Operation(summary = "创建字典类型", description = "创建新的字典类型")
    @PostMapping
    public Result<DictTypeDTO> createDictType(@Valid @RequestBody DictTypeDTO dictTypeDTO) {
        log.info("创建字典类型: {}", dictTypeDTO.getTypeCode());

        DictTypeDTO createdType = dictTypeService.createDictType(dictTypeDTO);
        return Result.success(createdType);
    }

    @Operation(summary = "更新字典类型", description = "根据ID更新字典类型信息")
    @PutMapping("/{id}")
    public Result<DictTypeDTO> updateDictType(
            @Parameter(description = "字典类型ID", required = true)
            @PathVariable @NotBlank(message = "字典类型ID不能为空") String id,
            @Valid @RequestBody DictTypeDTO dictTypeDTO) {
        log.info("更新字典类型: {}", id);

        DictTypeDTO updatedType = dictTypeService.updateDictType(id, dictTypeDTO);
        return Result.success(updatedType);
    }

    @Operation(summary = "删除字典类型", description = "根据ID删除字典类型")
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteDictType(
            @Parameter(description = "字典类型ID", required = true)
            @PathVariable @NotBlank(message = "字典类型ID不能为空") String id) {
        log.info("删除字典类型: {}", id);

        boolean result = dictTypeService.deleteDictType(id);
        return Result.success(result);
    }

    @Operation(summary = "批量删除字典类型", description = "根据ID列表批量删除字典类型")
    @DeleteMapping("/batch")
    public Result<Integer> batchDeleteDictTypes(
            @Parameter(description = "字典类型ID列表", required = true)
            @RequestBody @NotEmpty(message = "ID列表不能为空") List<String> ids) {
        log.info("批量删除字典类型: {}", ids);

        int deletedCount = dictTypeService.batchDeleteDictTypes(ids);
        return Result.success(deletedCount);
    }

    @Operation(summary = "获取根节点", description = "获取所有根节点（一级分类）")
    @GetMapping("/roots")
    public Result<List<DictTypeDTO>> getRootNodes() {
        log.info("获取所有根节点");

        List<DictTypeDTO> rootNodes = dictTypeService.getRootNodes();
        return Result.success(rootNodes);
    }

    @Operation(summary = "获取子节点", description = "根据父级ID获取子节点列表")
    @GetMapping("/children/{parentId}")
    public Result<List<DictTypeDTO>> getChildren(
            @Parameter(description = "父级ID", required = true)
            @PathVariable @NotBlank(message = "父级ID不能为空") String parentId) {
        log.info("获取子节点列表: {}", parentId);

        List<DictTypeDTO> children = dictTypeService.getChildren(parentId);
        return Result.success(children);
    }

    @Operation(summary = "获取树形结构", description = "获取完整的树形结构")
    @GetMapping("/tree")
    public Result<List<DictTypeDTO>> getTreeStructure() {
        log.info("获取树形结构");

        List<DictTypeDTO> treeStructure = dictTypeService.getTreeStructure();
        return Result.success(treeStructure);
    }

    @Operation(summary = "获取启用的字典类型", description = "获取所有启用状态的字典类型")
    @GetMapping("/enabled")
    public Result<List<DictTypeDTO>> getEnabledDictTypes() {
        log.info("获取启用状态的字典类型");

        List<DictTypeDTO> enabledTypes = dictTypeService.getEnabledDictTypes();
        return Result.success(enabledTypes);
    }

    @Operation(summary = "检查编码是否存在", description = "检查字典类型编码是否已存在")
    @GetMapping("/check-code")
    public Result<Boolean> checkTypeCodeExists(
            @Parameter(description = "类型编码", required = true)
            @RequestParam @NotBlank(message = "类型编码不能为空") String typeCode,
            @Parameter(description = "排除的ID（用于更新时检查）")
            @RequestParam(required = false) String excludeId) {
        log.info("检查类型编码是否存在: typeCode={}, excludeId={}", typeCode, excludeId);

        boolean exists = dictTypeService.isTypeCodeExists(typeCode, excludeId);
        return Result.success(exists);
    }

    @Operation(summary = "更新状态", description = "更新字典类型的启用/禁用状态")
    @PatchMapping("/{id}/status")
    public Result<Boolean> updateStatus(
            @Parameter(description = "字典类型ID", required = true)
            @PathVariable @NotBlank(message = "字典类型ID不能为空") String id,
            @Parameter(description = "状态值", required = true)
            @RequestParam Integer status) {
        log.info("更新字典类型状态: id={}, status={}", id, status);

        boolean result = dictTypeService.updateStatus(id, status);
        return Result.success(result);
    }

    @Operation(summary = "刷新缓存", description = "刷新字典类型缓存")
    @PostMapping("/cache/refresh")
    public Result<Void> refreshCache() {
        log.info("刷新字典类型缓存");

        dictTypeService.refreshCache();
        return Result.success();
    }

    @Operation(summary = "清理缓存", description = "清理字典类型缓存")
    @DeleteMapping("/cache")
    public Result<Void> clearCache() {
        log.info("清理字典类型缓存");

        dictTypeService.clearCache();
        return Result.success();
    }

    @Operation(summary = "获取缓存统计", description = "获取字典类型缓存统计信息")
    @GetMapping("/cache/stats")
    public Result<Object> getCacheStats() {
        log.info("获取字典类型缓存统计");

        Object stats = dictTypeService.getCacheStats();
        return Result.success(stats);
    }
}
