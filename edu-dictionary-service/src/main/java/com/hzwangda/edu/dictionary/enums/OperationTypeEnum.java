package com.hzwangda.edu.dictionary.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作类型枚举
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Getter
@AllArgsConstructor
public enum OperationTypeEnum {

    /**
     * 导入
     */
    IMPORT("IMPORT", "导入"),

    /**
     * 导出
     */
    EXPORT("EXPORT", "导出");

    private final String code;
    private final String description;

    /**
     * 根据代码获取枚举
     */
    public static OperationTypeEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (OperationTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效类型
     */
    public static boolean isValid(String code) {
        return getByCode(code) != null;
    }
}
