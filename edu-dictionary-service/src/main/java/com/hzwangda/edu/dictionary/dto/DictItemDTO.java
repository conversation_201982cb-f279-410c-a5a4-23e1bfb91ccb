package com.hzwangda.edu.dictionary.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 字典项数据传输对象
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
public class DictItemDTO {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 字典类型ID
     */
    @NotBlank(message = "字典类型ID不能为空")
    private String typeId;

    /**
     * 字典类型编码
     */
    private String typeCode;

    /**
     * 字典类型名称
     */
    private String typeName;

    /**
     * 字典编码
     */
    @NotBlank(message = "字典编码不能为空")
    @Size(max = 100, message = "字典编码长度不能超过100个字符")
    private String itemCode;

    /**
     * 字典名称
     */
    @NotBlank(message = "字典名称不能为空")
    @Size(max = 200, message = "字典名称长度不能超过200个字符")
    private String itemName;

    /**
     * 字典值
     */
    @Size(max = 500, message = "字典值长度不能超过500个字符")
    private String itemValue;

    /**
     * 父级字典项ID
     */
    private String parentId;

    /**
     * 父级字典项名称
     */
    private String parentName;

    /**
     * 字典项级别
     */
    private Integer itemLevel;

    /**
     * 字典项路径
     */
    private String itemPath;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 状态 (0: 禁用, 1: 启用)
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 是否系统内置
     */
    private Integer isSystem;

    /**
     * 是否允许添加子项
     */
    private Integer allowChildren;

    /**
     * 字典项颜色
     */
    @Size(max = 20, message = "颜色值长度不能超过20个字符")
    private String itemColor;

    /**
     * 字典项图标
     */
    @Size(max = 100, message = "图标值长度不能超过100个字符")
    private String itemIcon;

    /**
     * 描述信息
     */
    @Size(max = 500, message = "描述信息长度不能超过500个字符")
    private String description;

    /**
     * 备注信息
     */
    @Size(max = 1000, message = "备注信息长度不能超过1000个字符")
    private String remark;

    /**
     * 扩展属性
     */
    private String extraProperties;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 子节点列表
     */
    private List<DictItemDTO> children;

    /**
     * 是否有子节点
     */
    private Boolean hasChildren;
}
