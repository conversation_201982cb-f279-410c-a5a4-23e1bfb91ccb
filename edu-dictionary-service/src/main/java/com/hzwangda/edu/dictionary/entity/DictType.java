package com.hzwangda.edu.dictionary.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 字典类型实体类
 * 用于管理字典的分类和层级结构
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "dict_type", indexes = {
    @Index(name = "idx_dict_type_code", columnList = "type_code", unique = true),
    @Index(name = "idx_dict_type_parent", columnList = "parent_id"),
    @Index(name = "idx_dict_type_status", columnList = "status")
})
public class DictType extends BaseEntity {

    /**
     * 类型编码（唯一标识）
     */
    @NotBlank(message = "类型编码不能为空")
    @Size(max = 100, message = "类型编码长度不能超过100个字符")
    @Column(name = "type_code", length = 100, nullable = false, unique = true)
    private String typeCode;

    /**
     * 类型名称
     */
    @NotBlank(message = "类型名称不能为空")
    @Size(max = 200, message = "类型名称长度不能超过200个字符")
    @Column(name = "type_name", length = 200, nullable = false)
    private String typeName;

    /**
     * 父级类型ID（支持多级分类）
     */
    @Column(name = "parent_id", length = 36)
    private String parentId;

    /**
     * 类型级别（1:一级, 2:二级, 3:三级...）
     */
    @Column(name = "type_level")
    private Integer typeLevel = 1;

    /**
     * 类型路径（如：1/2/3，便于查询所有子级）
     */
    @Column(name = "type_path", length = 1000)
    private String typePath;

    /**
     * 排序号
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    /**
     * 状态 (0: 禁用, 1: 启用)
     */
    @Column(name = "status")
    private Integer status = 1;

    /**
     * 是否系统内置 (0: 否, 1: 是)
     * 系统内置的字典类型不允许删除
     */
    @Column(name = "is_system")
    private Integer isSystem = 0;

    /**
     * 是否允许添加子项 (0: 否, 1: 是)
     */
    @Column(name = "allow_children")
    private Integer allowChildren = 1;

    /**
     * 描述信息
     */
    @Size(max = 500, message = "描述信息长度不能超过500个字符")
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 备注信息
     */
    @Size(max = 1000, message = "备注信息长度不能超过1000个字符")
    @Column(name = "remark", length = 1000)
    private String remark;

    /**
     * 扩展属性（JSON格式）
     */
    @Column(name = "extra_properties", columnDefinition = "TEXT")
    private String extraProperties;
}
