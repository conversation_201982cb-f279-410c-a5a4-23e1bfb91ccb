-- 字典配置服务数据库表结构
-- 创建时间: 2024-01-01
-- 版本: 1.0.0

-- 字典类型表
CREATE TABLE IF NOT EXISTS dict_type (
    id VARCHAR(36) PRIMARY KEY,
    type_code VARCHAR(100) NOT NULL UNIQUE,
    type_name VARCHAR(200) NOT NULL,
    parent_id VARCHAR(36),
    type_level INTEGER DEFAULT 1,
    type_path VARCHAR(1000),
    sort_order INTEGER DEFAULT 0,
    status INTEGER DEFAULT 1,
    is_system INTEGER DEFAULT 0,
    allow_children INTEGER DEFAULT 1,
    description VARCHAR(500),
    remark VARCHAR(1000),
    extra_properties TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100),
    deleted INTEGER DEFAULT 0,
    version INTEGER DEFAULT 1
);

-- 字典项表
CREATE TABLE IF NOT EXISTS dict_item (
    id VARCHAR(36) PRIMARY KEY,
    type_id VARCHAR(36) NOT NULL,
    item_code VARCHAR(100) NOT NULL,
    item_name VARCHAR(200) NOT NULL,
    item_value VARCHAR(500),
    parent_id VARCHAR(36),
    item_level INTEGER DEFAULT 1,
    item_path VARCHAR(1000),
    sort_order INTEGER DEFAULT 0,
    status INTEGER DEFAULT 1,
    is_system INTEGER DEFAULT 0,
    allow_children INTEGER DEFAULT 0,
    item_color VARCHAR(20),
    item_icon VARCHAR(100),
    description VARCHAR(500),
    remark VARCHAR(1000),
    extra_properties TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100),
    deleted INTEGER DEFAULT 0,
    version INTEGER DEFAULT 1
);

-- 字典使用统计表
CREATE TABLE IF NOT EXISTS dict_usage_stats (
    id VARCHAR(36) PRIMARY KEY,
    type_id VARCHAR(36),
    item_id VARCHAR(36),
    stats_date DATE NOT NULL,
    access_count BIGINT DEFAULT 0,
    unique_users BIGINT DEFAULT 0,
    access_source VARCHAR(100),
    access_module VARCHAR(100),
    avg_response_time BIGINT DEFAULT 0,
    cache_hit_count BIGINT DEFAULT 0,
    cache_miss_count BIGINT DEFAULT 0,
    remark VARCHAR(500),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100),
    deleted INTEGER DEFAULT 0,
    version INTEGER DEFAULT 1
);

-- 字典导入导出日志表
CREATE TABLE IF NOT EXISTS dict_import_export_log (
    id VARCHAR(36) PRIMARY KEY,
    operation_type VARCHAR(20) NOT NULL,
    type_id VARCHAR(36),
    file_name VARCHAR(255),
    file_path VARCHAR(500),
    file_size BIGINT,
    operation_status VARCHAR(20) NOT NULL,
    total_records INTEGER DEFAULT 0,
    success_records INTEGER DEFAULT 0,
    failed_records INTEGER DEFAULT 0,
    error_message TEXT,
    error_details TEXT,
    processing_time BIGINT DEFAULT 0,
    operation_desc VARCHAR(500),
    remark VARCHAR(500),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100),
    deleted INTEGER DEFAULT 0,
    version INTEGER DEFAULT 1
);

-- 创建索引
-- 字典类型表索引
CREATE INDEX IF NOT EXISTS idx_dict_type_code ON dict_type(type_code);
CREATE INDEX IF NOT EXISTS idx_dict_type_parent_id ON dict_type(parent_id);
CREATE INDEX IF NOT EXISTS idx_dict_type_status ON dict_type(status);
CREATE INDEX IF NOT EXISTS idx_dict_type_deleted ON dict_type(deleted);

-- 字典项表索引
CREATE INDEX IF NOT EXISTS idx_dict_item_type_id ON dict_item(type_id);
CREATE INDEX IF NOT EXISTS idx_dict_item_code ON dict_item(item_code);
CREATE INDEX IF NOT EXISTS idx_dict_item_parent_id ON dict_item(parent_id);
CREATE INDEX IF NOT EXISTS idx_dict_item_status ON dict_item(status);
CREATE INDEX IF NOT EXISTS idx_dict_item_deleted ON dict_item(deleted);
CREATE INDEX IF NOT EXISTS idx_dict_item_type_code ON dict_item(type_id, item_code);

-- 字典使用统计表索引
CREATE INDEX IF NOT EXISTS idx_dict_usage_stats_type_id ON dict_usage_stats(type_id);
CREATE INDEX IF NOT EXISTS idx_dict_usage_stats_item_id ON dict_usage_stats(item_id);
CREATE INDEX IF NOT EXISTS idx_dict_usage_stats_date ON dict_usage_stats(stats_date);
CREATE INDEX IF NOT EXISTS idx_dict_usage_stats_deleted ON dict_usage_stats(deleted);

-- 字典导入导出日志表索引
CREATE INDEX IF NOT EXISTS idx_dict_import_export_log_type ON dict_import_export_log(operation_type);
CREATE INDEX IF NOT EXISTS idx_dict_import_export_log_status ON dict_import_export_log(operation_status);
CREATE INDEX IF NOT EXISTS idx_dict_import_export_log_create_time ON dict_import_export_log(create_time);
CREATE INDEX IF NOT EXISTS idx_dict_import_export_log_deleted ON dict_import_export_log(deleted);

-- 添加外键约束
ALTER TABLE dict_type ADD CONSTRAINT fk_dict_type_parent 
    FOREIGN KEY (parent_id) REFERENCES dict_type(id) ON DELETE SET NULL;

ALTER TABLE dict_item ADD CONSTRAINT fk_dict_item_type 
    FOREIGN KEY (type_id) REFERENCES dict_type(id) ON DELETE CASCADE;

ALTER TABLE dict_item ADD CONSTRAINT fk_dict_item_parent 
    FOREIGN KEY (parent_id) REFERENCES dict_item(id) ON DELETE SET NULL;

ALTER TABLE dict_usage_stats ADD CONSTRAINT fk_dict_usage_stats_type 
    FOREIGN KEY (type_id) REFERENCES dict_type(id) ON DELETE CASCADE;

ALTER TABLE dict_usage_stats ADD CONSTRAINT fk_dict_usage_stats_item 
    FOREIGN KEY (item_id) REFERENCES dict_item(id) ON DELETE CASCADE;

-- 添加注释
COMMENT ON TABLE dict_type IS '字典类型表';
COMMENT ON TABLE dict_item IS '字典项表';
COMMENT ON TABLE dict_usage_stats IS '字典使用统计表';
COMMENT ON TABLE dict_import_export_log IS '字典导入导出日志表';
