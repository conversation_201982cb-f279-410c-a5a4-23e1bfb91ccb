# see https://docs.gitlab.com/ee/ci/yaml/README.html for all available options
variables:
  KUBE_NAMESPACE: "$CI_PROJECT_ROOT_NAMESPACE-test"
  KUBE_DEPLOYMENT_NAME_FILE_SERVICE: "edu-file-service"
  KUBE_DEPLOYMENT_NAME_AUTH_SERVICE: "edu-auth-service"
  DOCKER_REPO_FILE_SERVICE: "$DOCKER_REGISTRY/hzwangda/$CI_PROJECT_ROOT_NAMESPACE/$KUBE_DEPLOYMENT_NAME_FILE_SERVICE:build-$CI_PIPELINE_ID"
  DOCKER_REPO_AUTH_SERVICE: "$DOCKER_REGISTRY/hzwangda/$CI_PROJECT_ROOT_NAMESPACE/$KUBE_DEPLOYMENT_NAME_AUTH_SERVICE:build-$CI_PIPELINE_ID"

  # 服务选择变量 - 可在GitLab网页端运行pipeline时设置
  DEPLOY_SERVICE_NAME: ""    # 是否部署自定义服务

workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" # gitlab网页端手工触发
      when: always
    - if: $CI_COMMIT_REF_NAME == "master"
      when: never
    - if: $CI_COMMIT_REF_NAME == "dev"
      when: never
    - if: $CI_COMMIT_REF_NAME =~ /^prev/
      when: always
    - if: $CI_COMMIT_REF_NAME =~ /^test/
      when: always
    - when: never

stages:
  - build-java
  - build-docker
  - deploy-rancher

# 构建文件服务
build_file_service:
  stage: build-java
  variables:
    MAVEN_CLI_OPTS: "-s .m2/settings.xml --batch-mode"
    MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"
  image: $DOCKER_REGISTRY_PUBLIC/maven:3.6.3-openjdk-17
  script:
    - echo "构建文件服务..."
    - cd edu-file-service
    - mvn clean package -U
  cache:
    key: mvn-repository-cache
    paths:
      - .m2/repository/
  artifacts:
    paths:
      - edu-file-service/target/edu-file-service-1.0.0-SNAPSHOT.jar
    expire_in: 2 hour
  rules:
    - if: $DEPLOY_SERVICE_NAME == "" || $DEPLOY_SERVICE_NAME == "edu-file-service"
      when: always
    - when: never

# 构建认证服务
build_auth_service:
  stage: build-java
  variables:
    MAVEN_CLI_OPTS: "-s .m2/settings.xml --batch-mode"
    MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"
  image: $DOCKER_REGISTRY_PUBLIC/maven:3.6.3-openjdk-17
  script:
    - echo "构建认证服务..."
    - cd edu-auth-service
    - mvn clean package -U
  cache:
    key: mvn-repository-cache
    paths:
      - .m2/repository/
  artifacts:
    paths:
      - edu-auth-service/target/edu-auth-service-1.0.0-SNAPSHOT.jar
    expire_in: 2 hour
  rules:
    - if: $DEPLOY_SERVICE_NAME == "" || $DEPLOY_SERVICE_NAME == "edu-auth-service"
      when: always
    - when: never

# 构建文件服务Docker镜像
build_file_service_image:
  stage: build-docker
  variables:
    DOCKER_HOST: tcp://localhost:2375
    DOCKER_TLS_CERTDIR: ''
  image: $DOCKER_REGISTRY_PUBLIC/docker:stable
  services:
    - name: $DOCKER_REGISTRY_PUBLIC/docker:18.09-dind
      command: ["--registry-mirror=https://$DOCKER_REGISTRY_PUBLIC"]
  script:
    - echo "构建文件服务Docker镜像..."
    - docker login -u $DOCKER_USER -p $DOCKER_PASSWORD $DOCKER_REGISTRY
    - docker build -t $DOCKER_REPO_FILE_SERVICE -f ./edu-file-service/Dockerfile .
    - docker push $DOCKER_REPO_FILE_SERVICE
  after_script:
    - echo "文件服务镜像地址 $DOCKER_REPO_FILE_SERVICE"
  rules:
    - if: $DEPLOY_SERVICE_NAME == "" || $DEPLOY_SERVICE_NAME == "edu-file-service"
      when: always
    - when: never
  needs: ["build_file_service"]

# 构建认证服务Docker镜像
build_auth_service_image:
  stage: build-docker
  variables:
    DOCKER_HOST: tcp://localhost:2375
    DOCKER_TLS_CERTDIR: ''
  image: $DOCKER_REGISTRY_PUBLIC/docker:stable
  services:
    - name: $DOCKER_REGISTRY_PUBLIC/docker:18.09-dind
      command: ["--registry-mirror=https://$DOCKER_REGISTRY_PUBLIC"]
  script:
    - echo "构建认证服务Docker镜像..."
    - docker login -u $DOCKER_USER -p $DOCKER_PASSWORD $DOCKER_REGISTRY
    - docker build -t $DOCKER_REPO_AUTH_SERVICE -f ./edu-auth-service/Dockerfile .
    - docker push $DOCKER_REPO_AUTH_SERVICE
  after_script:
    - echo "认证服务镜像地址 $DOCKER_REPO_AUTH_SERVICE"
  rules:
    - if: $DEPLOY_SERVICE_NAME == "" || $DEPLOY_SERVICE_NAME == "edu-auth-service"
      when: always
    - when: never
  needs: ["build_auth_service"]

# 部署文件服务到Rancher
deploy_file_service_rancher:
  stage: deploy-rancher
  variables:
    GIT_STRATEGY: none # 无需拉git源码
  image: $DOCKER_REGISTRY_PUBLIC/hzwangda/rancher-cli-k8s:2.4.10
  script:
    - echo "部署文件服务到Rancher..."
    - rancher login $RACHER_SERVER --token $RACHER_TOKEN --context $RACHER_CONTEXT
    - rancher kubectl set image deployment/$KUBE_DEPLOYMENT_NAME_FILE_SERVICE $KUBE_DEPLOYMENT_NAME_FILE_SERVICE=$DOCKER_REPO_FILE_SERVICE --namespace=$KUBE_NAMESPACE
    - echo "文件服务部署完成"
  after_script:
    - echo "部署命令 rancher kubectl set image deployment/$KUBE_DEPLOYMENT_NAME_FILE_SERVICE $KUBE_DEPLOYMENT_NAME_FILE_SERVICE=$DOCKER_REPO_FILE_SERVICE --namespace=$KUBE_NAMESPACE"
  dependencies: [] # 不需要前面环节的产物artifacts
  rules:
    - if: $DEPLOY_SERVICE_NAME == "" || $DEPLOY_SERVICE_NAME == "edu-file-service"
      when: always
    - when: never
  needs: ["build_file_service_image"]

# 部署认证服务到Rancher
deploy_auth_service_rancher:
  stage: deploy-rancher
  variables:
    GIT_STRATEGY: none # 无需拉git源码
  image: $DOCKER_REGISTRY_PUBLIC/hzwangda/rancher-cli-k8s:2.4.10
  script:
    - echo "部署认证服务到Rancher..."
    - rancher login $RACHER_SERVER --token $RACHER_TOKEN --context $RACHER_CONTEXT
    - rancher kubectl set image deployment/$KUBE_DEPLOYMENT_NAME_AUTH_SERVICE $KUBE_DEPLOYMENT_NAME_AUTH_SERVICE=$DOCKER_REPO_AUTH_SERVICE --namespace=$KUBE_NAMESPACE
    - echo "认证服务部署完成"
  after_script:
    - echo "部署命令 rancher kubectl set image deployment/$KUBE_DEPLOYMENT_NAME_AUTH_SERVICE $KUBE_DEPLOYMENT_NAME_AUTH_SERVICE=$DOCKER_REPO_AUTH_SERVICE --namespace=$KUBE_NAMESPACE"
  dependencies: [] # 不需要前面环节的产物artifacts
  rules:
    - if: $DEPLOY_SERVICE_NAME == "" || $DEPLOY_SERVICE_NAME == "edu-auth-service"
      when: always
    - when: never
  needs: ["build_auth_service_image"]

# 部署状态总结
deployment_summary:
  stage: deploy-rancher
  script:
    - echo "=== 部署状态总结 ==="
    - echo "Pipeline ID $CI_PIPELINE_ID"
    - echo "分支 $CI_COMMIT_REF_NAME"
    - echo "命名空间 $KUBE_NAMESPACE"
    - echo ""
    - |
      if [ "$DEPLOY_SERVICE_NAME" = "edu-file-service" ]; then
        echo "✅ 文件服务已部署: $DOCKER_REPO_FILE_SERVICE"
      else
        echo "⏭️  文件服务跳过部署"
      fi
    - |
      if [ "$DEPLOY_SERVICE_NAME" = "edu-auth-service" ]; then
        echo "✅ 认证服务已部署: $DOCKER_REPO_AUTH_SERVICE"
      else
        echo "⏭️  认证服务跳过部署"
      fi
    - echo ""
    - echo "=== 部署完成 ==="
  rules:
    - if: $DEPLOY_SERVICE_NAME == "" || $DEPLOY_SERVICE_NAME == "edu-file-service" || $DEPLOY_SERVICE_NAME == "edu-auth-service"
      when: always
    - when: never
